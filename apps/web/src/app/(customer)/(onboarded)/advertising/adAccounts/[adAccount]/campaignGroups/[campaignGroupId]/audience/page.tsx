"use client";

import React, {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useId,
  useMemo,
  useRef,
  useState,
} from "react";
import { useRouter } from "next/navigation";
import {
  AddCriteriaData,
  AudienceMutation,
  AudienceTargetingCriteria,
} from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/types/audience";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import {
  useAudienceActions,
  useAudienceState,
  useCampaignErrorHandler,
  useGlobalActions,
} from "@/hooks/useCampaignStore";
import { useCampaignCreationStore } from "@/stores/campaignCreationStore";
import { api } from "@/trpc/client";
import { DotsHorizontalIcon, DotsVerticalIcon } from "@radix-ui/react-icons";
import {
  ArrowLeft,
  ArrowRight,
  ChevronDown,
  CopyIcon,
  Loader2,
  PlusI<PERSON>,
  TrashIcon,
  XIcon,
} from "lucide-react";
import { uuidv7 } from "uuidv7";

import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { Checkbox } from "@kalos/ui/checkbox";
import { Chip } from "@kalos/ui/chip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { cn } from "@kalos/ui/index";
import { Input } from "@kalos/ui/input";
import { LottieAnimation, LottieLoading } from "@kalos/ui/lottie";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { Skeleton } from "@kalos/ui/skeleton";

import { linkedInAudienceTargetCriteriaSchema } from "../../../../../../../../../../../../backend/src/modules/advertising/domain/valueObjects/linkedinAudienceTargeting/linkedinTargetAudienceCriteria";
import { createUuid } from "../../../../../../../../../../../../backend/src/modules/core/utils/uuid";
import { defaultLocations, getFriendlyFacetName, LINKEDIN_LOCATIONS_FACET_URN, unitedStatesLocationCriteria } from "./utils/audienceUtils";

export default function AudiencePage({
  params,
}: {
  params: { adAccount: string; campaignGroupId: string };
}) {
  // Use specialized selectors from custom hooks
  const audienceState = useAudienceState();
  const { toggleCollapse, setCurrentAudience, addConfirmed } =
    useAudienceActions();
  const { setAdAccount, setCampaignGroupId } = useGlobalActions();
  const { handleTRPCError } = useCampaignErrorHandler();

  const {
    collapsedSegments = [],
    confirmedSegments = [],
    currentAudienceIds = {},
  } = audienceState;

  const [loading, setLoading] = useState(false);

  const [processedSegmentPairs, setProcessedSegmentPairs] = useState<
    Array<{
      previousSegmentId: string;
      nextSegmentId: string;
    }>
  >([]);

  const audienceSegments =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery({
      adProgramId: params.campaignGroupId,
    });

  useEffect(() => {
    if (audienceSegments.error) {
      handleTRPCError("audience", audienceSegments.error);
    }
  }, [audienceSegments.error, handleTRPCError]);

  // Set route params in store
  useEffect(() => {
    setAdAccount(params.adAccount);
    setCampaignGroupId(params.campaignGroupId);
  }, [params, setAdAccount, setCampaignGroupId]);

  const router = useRouter();

  const [initializedSegments, setInitializedSegments] = useState(false);
  const isTransitioningRef = useRef(false);

  // 1. Make sure segments initialize properly and stay that way
  useEffect(() => {
    if (
      audienceSegments.data &&
      audienceSegments.data.length > 0 &&
      !initializedSegments
    ) {
      // Get all segment IDs except the first one
      const segmentsToCollapse = audienceSegments.data
        .slice(1)
        .map((segment) => segment.id);

      // Initialize the collapsed segments in the store
      segmentsToCollapse.forEach((segmentId) => {
        if (!collapsedSegments.includes(segmentId)) {
          toggleCollapse(segmentId);
        }
      });

      setInitializedSegments(true);

      // Prevent any automatic opening until user interaction
      isTransitioningRef.current = false;
    }
  }, [
    audienceSegments.data,
    collapsedSegments,
    toggleCollapse,
    initializedSegments,
  ]);

  // Removed hardcoded delay, using zustand for loading state
  useEffect(() => {
    if (audienceSegments.data) {
      const timer = setTimeout(() => {
        setLoading(false);
      }, 800);
      return () => clearTimeout(timer);
    }
  }, [audienceSegments.data, setLoading]);

  // 2. Refactor openNextSegment to be more controlled
  const openNextSegment = useCallback(
    (currentSegmentId: string) => {
      // Don't open anything if we're in a transition
      if (isTransitioningRef.current) return;

      if (audienceSegments.data) {
        const currentIndex = audienceSegments.data.findIndex(
          (segment) => segment.id === currentSegmentId,
        );
        const nextSegment = audienceSegments.data[currentIndex + 1];

        if (nextSegment && collapsedSegments.includes(nextSegment.id)) {
          toggleCollapse(nextSegment.id);
        }
      }
    },
    [audienceSegments.data, collapsedSegments, toggleCollapse],
  );

  const autoCollapseSegment = useCallback(
    (segmentId: string) => {
      if (isTransitioningRef.current) return;

      const safeCollapsedSegments = Array.isArray(collapsedSegments)
        ? collapsedSegments
        : [];

      if (!safeCollapsedSegments.includes(segmentId)) {
        isTransitioningRef.current = true;

        toggleCollapse(segmentId);

        setTimeout(() => {
          openNextSegment(segmentId);
          isTransitioningRef.current = false;
        }, 100);
      }
    },
    [collapsedSegments, toggleCollapse, openNextSegment],
  );

  // Check if all segments have been confirmed
  const allSegmentsConfirmed = useMemo(() => {
    if (!audienceSegments.data) return false;

    // Only consider ready segments
    const readySegments = audienceSegments.data.filter(
      (segment) => segment.ready,
    );
    if (readySegments.length === 0) return false;

    return readySegments.every((segment) =>
      confirmedSegments.includes(segment.id),
    );
  }, [audienceSegments.data, confirmedSegments]);

  const handleSegmentCollapse = useCallback(
    (segmentId: string) => {
      if (isTransitioningRef.current) return;

      isTransitioningRef.current = true;
      const isCurrentlyExpanded = !collapsedSegments.includes(segmentId);

      toggleCollapse(segmentId);

      if (isCurrentlyExpanded) {
        setTimeout(() => {
          openNextSegment(segmentId);
          isTransitioningRef.current = false;
        }, 100);
      } else {
        setTimeout(() => {
          isTransitioningRef.current = false;
        }, 100);
      }
    },
    [collapsedSegments, toggleCollapse, openNextSegment],
  );

  /**
   * Handles transitioning between audience segments.
   * - Confirms the current segment and collapses it if needed.
   * - Expands the next segment if it hasn't been processed before.
   * - Uses timeouts to ensure sequential processing.
   */
  const handleSegmentTransition = useCallback(
    (currentSegmentId: string, index: number) => {
      if (isTransitioningRef.current) return;
      isTransitioningRef.current = true;

      addConfirmed(currentSegmentId);

      setTimeout(() => {
        if (!collapsedSegments.includes(currentSegmentId)) {
          toggleCollapse(currentSegmentId);
        }

        setTimeout(() => {
          const nextSegment = audienceSegments.data?.[index + 1];

          if (nextSegment) {
            const pairExists = processedSegmentPairs.some(
              (pair) =>
                pair.previousSegmentId === currentSegmentId &&
                pair.nextSegmentId === nextSegment.id,
            );

            if (!pairExists) {
              if (collapsedSegments.includes(nextSegment.id)) {
                toggleCollapse(nextSegment.id);
              }

              setProcessedSegmentPairs((prev) => [
                ...prev,
                {
                  previousSegmentId: currentSegmentId,
                  nextSegmentId: nextSegment.id,
                },
              ]);
            }
          }

          isTransitioningRef.current = false;
        }, 300);
      }, 100);
    },
    [
      addConfirmed,
      audienceSegments.data,
      collapsedSegments,
      toggleCollapse,
      processedSegmentPairs,
      setProcessedSegmentPairs,
    ],
  );

  return (
    <div className="flex h-screen w-full flex-col overflow-hidden">
      <div className="w-full flex-grow overflow-y-auto pb-24">
        <div className="relative flex w-full flex-col items-start justify-start">
          <div className="flex w-full flex-col items-start justify-start space-y-4 p-4">
            <div className="flex w-full flex-col items-start justify-start">
              <h1 className="text-xl font-medium">Audience</h1>
              <h2 className="text-base">
                Let's create a few test audiences to find the most efficient way
                to reach our ICP. We'll launch a test for each and prioritize
                based on performance.
              </h2>
            </div>
          </div>

          {loading && (
            <LottieLoading
              messages={[
                "Performing research",
                "Accessing CRM",
                "Reviewing your Closed/Won deals",
                "Discovering relevant job titles",
                "Comparing to thousands of Linkedin titles",
                "Finding matches",
                "Preparing to display...",
              ]}
              animations={{
                primary: {
                  src: "/animations/Blue_Research_Circles.lottie",
                  autoplay: true,
                  loop: false,
                },
                secondary: {
                  src: "/animations/Blue_Research_Waves.lottie",
                  autoplay: true,
                  loop: false,
                },
              }}
            />
          )}

          <div
            className={cn(
              "flex w-full flex-col items-start justify-start space-y-4 p-4",
              loading
                ? "opacity-0"
                : "opacity-100 transition-opacity duration-300 ease-in-out",
            )}
          >
            <div className="mb-16 flex w-full flex-col items-start justify-between gap-y-4">
              {!audienceSegments.isLoading &&
                !audienceSegments.isRefetching &&
                audienceSegments.data?.map((segment, index) => (
                  <AudienceSegment
                    key={segment.id}
                    adProgramId={params.campaignGroupId}
                    adAccount={params.adAccount}
                    adSegment={segment}
                    segmentNumber={index + 1}
                    isCollapsed={
                      Array.isArray(collapsedSegments)
                        ? collapsedSegments.includes(segment.id)
                        : false
                    }
                    toggleCollapse={() => handleSegmentCollapse(segment.id)}
                    autoCollapseSegment={() => autoCollapseSegment(segment.id)}
                    currentAudienceId={currentAudienceIds[segment.id] ?? null}
                    setCurrentAudience={(audienceId) =>
                      setCurrentAudience(segment.id, audienceId)
                    }
                    onAllAudiencesConfirmed={() =>
                      handleSegmentTransition(segment.id, index)
                    }
                  />
                ))}
              {(audienceSegments.isLoading ||
                audienceSegments.isRefetching) && (
                  <div className="flex w-full items-center justify-center">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                )}
            </div>
          </div>
        </div>
      </div>

      <div className="fixed bottom-0 left-0 right-0 w-full border-t border-gray-200 bg-white p-4">
        <div className="flex items-center justify-between">
          <Button
            onClick={() =>
              router.push(
                `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/segments`,
              )
            }
            className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
          >
            <ArrowLeft width="16" className="mr-2" /> Previous
          </Button>
          <Button
            onClick={() => {
              router.push(
                `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/abTest`,
              );
            }}
            disabled={!allSegmentsConfirmed}
            className={
              !allSegmentsConfirmed ? "cursor-not-allowed opacity-50" : ""
            }
          >
            Next
            <ArrowRight width="16" className="ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}

function AudienceSegment({
  adProgramId,
  adAccount,
  adSegment,
  segmentNumber,
  isCollapsed,
  toggleCollapse,
  autoCollapseSegment,
  currentAudienceId,
  setCurrentAudience,
  onAllAudiencesConfirmed,
}: {
  adProgramId: string;
  adAccount: string;
  adSegment: {
    segmentId: string;
    id: string;
    adProgramId: string;
    ready: boolean;
  };
  segmentNumber: number;
  isCollapsed: boolean;
  toggleCollapse: () => void;
  autoCollapseSegment: () => void;
  currentAudienceId: string | null;
  setCurrentAudience: (audienceId: string | null) => void;
  onAllAudiencesConfirmed: () => void;
}) {
  const [refetchInterval, setRefetchInterval] = useState<number | undefined>(
    1000,
  );
  const [initializedSegments, setInitializedSegments] = useState(false);

  const segmentQuery = api.v2.core.segment.getSegment.useQuery({
    segmentId: adSegment.segmentId,
  });
  const audiencesInSegmentQuery =
    api.v2.ads.adAudience.getAllForAdSegment.useQuery(
      {
        adSegmentId: adSegment.id,
      },
      {
        refetchInterval: refetchInterval,
      },
    );

  // State to track which audiences are confirmed
  const [confirmedAudiences, setConfirmedAudiences] = useState<string[]>([]);

  useEffect(() => {
    if (adSegment.ready) {
      setRefetchInterval(undefined);
    }
  }, [adSegment.ready]);

  // Check if all audiences in this segment are confirmed
  useEffect(() => {
    if (audiencesInSegmentQuery.data && adSegment.ready) {
      const allAudiences = audiencesInSegmentQuery.data;
      if (
        allAudiences.length > 0 &&
        confirmedAudiences.length === allAudiences.length
      ) {
        onAllAudiencesConfirmed();
      }
    }
  }, [
    confirmedAudiences,
    audiencesInSegmentQuery.data,
    adSegment.ready,
    onAllAudiencesConfirmed,
  ]);

  const apiUtils = api.useUtils();

  const addAudienceMutation = api.v2.ads.adAudience.create.useMutation({
    onSuccess: async () => {
      await apiUtils.v2.ads.invalidate();
    },
  });

  // Handle confirming all audiences in the segment
  const handleConfirmAllAudiences = () => {
    if (audiencesInSegmentQuery.data) {
      // Mark all audiences as confirmed
      audiencesInSegmentQuery.data.forEach((audience) => {
        if (!confirmedAudiences.includes(audience.id)) {
          setConfirmedAudiences((prev) => [...prev, audience.id]);
        }
      });

      // Set current audience to null
      setCurrentAudience(null);

      // Call the existing function that handles collapsing
      onAllAudiencesConfirmed();
    }
  };

  const [
    loadingThingForDuplicateAudience,
    setLoadingThingForDuplicateAudience,
  ] = useState(false);

  return (
    <div className="w-full">
      <Card className="w-full overflow-hidden">
        <CardHeader
          className="flex cursor-pointer items-center bg-gray-50 py-3"
          onClick={toggleCollapse}
        >
          <div className="flex w-full items-center justify-between">
            <div>
              <div className="mb-1 text-xs text-muted-foreground">
                Segment {segmentNumber}
              </div>
              {segmentQuery.data && (
                <div>
                  <SegmentDetails row={{ original: segmentQuery.data }} />
                </div>
              )}
              {!segmentQuery.data && (
                <Skeleton className="h-6 w-64 bg-gray-200" />
              )}
            </div>
            <div className="flex items-center">
              <ChevronDown
                className={cn(
                  "h-5 w-5 text-gray-500 transition-transform duration-200",
                  isCollapsed ? "rotate-180" : "",
                )}
              />
            </div>
          </div>
        </CardHeader>

        <div
          className={cn(
            "transition-all duration-300 ease-in-out",
            isCollapsed
              ? "max-h-0 opacity-0"
              : "max-h-[5000px] p-4 opacity-100",
          )}
        >
          <CardContent className="flex w-full flex-col items-start justify-start gap-y-6">
            {adSegment.ready &&
              !audiencesInSegmentQuery.isLoading &&
              !audiencesInSegmentQuery.isRefetching &&
              !loadingThingForDuplicateAudience &&
              audiencesInSegmentQuery.data?.map((audience, index) => (
                <div key={audience.id} className="w-full">
                  <Audience
                    audienceId={audience.id}
                    adAccount={adAccount}
                    adProgramId={adProgramId}
                    audienceIndex={index + 1}
                    loadingThingForDuplicateAudience={
                      loadingThingForDuplicateAudience
                    }
                    setLoadingThingForDuplicateAudience={
                      setLoadingThingForDuplicateAudience
                    }
                  />
                </div>
              ))}
            {(audiencesInSegmentQuery.isLoading ||
              audiencesInSegmentQuery.isRefetching ||
              loadingThingForDuplicateAudience) && (
                <div className="flex w-full items-center justify-center">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
              )}
            {!adSegment.ready && (
              <div className="flex w-full items-center justify-center">
                <h1 className="text-sm">
                  This segment is not ready yet. Please wait for it to be ready.
                </h1>
              </div>
            )}
            <div className="flex w-full items-center justify-start">
              <Button
                className="border-blue-200 bg-blue-50 font-normal text-primary hover:bg-blue-100"
                variant="outline"
                size="sm"
                onClick={() => {
                  addAudienceMutation.mutate({
                    adSegmentId: adSegment.id,
                    audienceTargetCriteria: unitedStatesLocationCriteria,
                  });
                }}
              >
                <PlusIcon width="16" className="mr-1" />
                Add audience
              </Button>
            </div>

            {adSegment.ready &&
              (audiencesInSegmentQuery.data?.length ?? 0) > 0 && (
                <div className="mt-4 flex w-full items-center justify-end">
                  <Button
                    onClick={handleConfirmAllAudiences}
                    className="transition-color bg-primary px-3 py-1 text-sm text-white duration-200 hover:bg-blue-600 hover:shadow-md"
                  >
                    Confirm Segment
                  </Button>
                </div>
              )}
          </CardContent>
        </div>
      </Card>
    </div>
  );
}

function Audience({
  audienceId,
  adAccount,
  adProgramId,
  audienceIndex,
  loadingThingForDuplicateAudience,
  setLoadingThingForDuplicateAudience,
}: {
  audienceId: string;
  adAccount: string;
  adProgramId: string;
  audienceIndex: number;
  loadingThingForDuplicateAudience: boolean;
  setLoadingThingForDuplicateAudience: Dispatch<SetStateAction<boolean>>;
}) {
  const [addCriteriaOpen, setAddCriteriaOpen] = useState(false);
  const audienceQuery = api.v2.ads.adAudience.getOne.useQuery(
    {
      id: audienceId,
    },
    {
      refetchInterval: **********,
    },
  );

  const [targetingCriteria, setTargetingCriteria] =
    useState<AudienceTargetingCriteria | null>(null);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [editingInProgress, setEditingInProgress] = useState(false);

  const apiUtils = api.useUtils();

  const updateAudienceMutation =
    api.v2.ads.adAudience.updateTargetCriteria.useMutation({
      scope: {
        id: "UPDATE_THING",
      },
      onSuccess: async () => {
        await apiUtils.v2.ads.adAudience.getOne.invalidate({
          id: audienceId,
        });
        await apiUtils.v2.ads.linkedInApi.getAudienceCount.invalidate();
      },
    });

  // Set up a check for unsaved changes when targeting criteria updates
  useEffect(() => {
    console.log("targetingCriteria", targetingCriteria);
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (!audienceQuery.data?.audienceTargetCriteria) {
      return;
    }
    if (!targetingCriteria) {
      setTargetingCriteria(audienceQuery.data?.audienceTargetCriteria);
      return;
    }
    if (
      compareTargetingCriteria(
        targetingCriteria,
        audienceQuery.data.audienceTargetCriteria,
      )
    ) {
      console.log("No changes detected");
      return;
    }
    setEditingInProgress(true);
    // Create a new timeout that will trigger after 10 seconds
    timeoutRef.current = setTimeout(() => {
      // Only run if the data still differs from server data
      if (
        targetingCriteria &&
        audienceQuery.data?.audienceTargetCriteria &&
        !compareTargetingCriteria(
          targetingCriteria,
          audienceQuery.data.audienceTargetCriteria,
        )
      ) {
        console.log("Detected unsaved changes after 10 seconds of inactivity", {
          local: JSON.stringify(targetingCriteria, null, 2),
          server: JSON.stringify(
            audienceQuery.data.audienceTargetCriteria,
            null,
            2,
          ),
        });

        // Update the server with our local changes
        updateAudienceMutation.mutate({
          id: audienceId,
          targetCriteria: targetingCriteria,
        });
      }
      setEditingInProgress(false);
    }, 2500); // Wait 10 seconds

    // Clean up function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [targetingCriteria, audienceQuery.data]);

  const idHookThing = useId();

  function handleAddCriteria(data: {
    facetUrn: string;
    entites: {
      entityUrn: string;
      entityName: string;
    }[];
    facetName: string;
  }) {

    setAddCriteriaOpen(false);
    const currentAudienceTargets = structuredClone(targetingCriteria);
    if (!currentAudienceTargets) {
      return;
    }

    // Find if there's an existing AND group containing this facet
    const foundOrGroupWhereFacetExists =
      currentAudienceTargets.include.and.find((and) =>
        and.or.find((or) => or.facetUrn === data.facetUrn),
      );

    if (foundOrGroupWhereFacetExists) {
      // Find the specific facet group within the AND group
      const foundFacetGroup = foundOrGroupWhereFacetExists.or.find(
        (or) => or.facetUrn === data.facetUrn,
      );

      if (foundFacetGroup) {
        // Add new entities to existing facet group
        foundFacetGroup.facetEntites.push(
          ...data.entites.map((entity) => ({
            facetUrn: data.facetUrn,
            entityUrn: entity.entityUrn,
            entityName: entity.entityName,
          })),
        );
      } else {
        // Create new facet group in existing AND group
        foundOrGroupWhereFacetExists.or.push({
          facetUrn: data.facetUrn,
          facetName: data.facetName,
          facetEntites: data.entites.map((entity) => ({
            facetUrn: data.facetUrn,
            entityUrn: entity.entityUrn,
            entityName: entity.entityName,
          })),
        });
      }
    } else {
      // Create new AND group with new facet
      currentAudienceTargets.include.and.push({
        or: [
          {
            facetUrn: data.facetUrn,
            facetName: data.facetName,
            facetEntites: data.entites.map((entity) => ({
              facetUrn: data.facetUrn,
              entityUrn: entity.entityUrn,
              entityName: entity.entityName,
            })),
          },
        ],
      });
    }

    const mutationId = uuidv7();

    setTargetingCriteria(currentAudienceTargets);
  }

  function handleAddExcludeCriteria(data: {
    facetUrn: string;
    entites: {
      entityUrn: string;
      entityName: string;
    }[];
    facetName: string;
  }) {
    setAddCriteriaOpen(false);
    const currentAudienceTargets = structuredClone(targetingCriteria);

    if (!currentAudienceTargets) {
      return;
    }

    const excludeTargets = currentAudienceTargets.exclude ?? {
      or: [],
    };

    const foundFacet = excludeTargets.or.find(
      (or) => or.facetUrn === data.facetUrn,
    );

    if (foundFacet) {
      // Add new entities to existing facet group
      foundFacet.facetEntites.push(
        ...data.entites.map((entity) => ({
          facetUrn: data.facetUrn,
          entityUrn: entity.entityUrn,
          entityName: entity.entityName,
        })),
      );
    } else {
      // Create new facet group with all entities
      excludeTargets.or.push({
        facetUrn: data.facetUrn,
        facetName: data.facetName,
        facetEntites: data.entites.map((entity) => ({
          facetUrn: data.facetUrn,
          entityUrn: entity.entityUrn,
          entityName: entity.entityName,
        })),
      });
    }

    const mutationId = uuidv7();

    setTargetingCriteria({
      include: currentAudienceTargets.include,
      exclude: excludeTargets,
    });
  }

  const updateAudienceToBeUsedMutation =
    api.v2.ads.adAudience.updateToBeUsed.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.adAudience.invalidate();
      },
    });

  const deleteAudienceMutation = api.v2.ads.adAudience.deleteOne.useMutation({
    onSuccess: async () => {
      await apiUtils.v2.ads.adAudience.invalidate();
    },
    onMutate: () => {
      setLoadingThingForDuplicateAudience(true);
    },
    onSettled: () => {
      setLoadingThingForDuplicateAudience(false);
    },
  });

  const duplicateAudienceMutation = api.v2.ads.adAudience.duplicate.useMutation(
    {
      onSuccess: async () => {
        await apiUtils.v2.ads.adAudience.invalidate();
      },
      onMutate: () => {
        setLoadingThingForDuplicateAudience(true);
      },
      onSettled: () => {
        setLoadingThingForDuplicateAudience(false);
      },
    },
  );

  return (
    <div
      className={
        "height-vh relative flex w-full flex-col items-start justify-start gap-y-2 rounded-md border p-3"
      }
    >
      <div className="flex w-full flex-col items-start justify-start pb-2">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center justify-start gap-x-2">
            <Checkbox
              checked={audienceQuery.data?.toBeUsed}
              onCheckedChange={() => {
                updateAudienceToBeUsedMutation.mutate({
                  id: audienceId,
                  toBeUsed: !audienceQuery.data?.toBeUsed,
                });
              }}
              className="h-4 w-4"
            />

            {audienceQuery.data && (
              <AudienceHeader
                audienceTargetingCriteria={
                  audienceQuery.data.audienceTargetCriteria
                }
                audienceIndex={audienceIndex}
              />
            )}
          </div>
          <div className="flex items-center gap-1">
            <AudienceTargetingDialog
              isOpen={addCriteriaOpen}
              setIsOpen={setAddCriteriaOpen}
              onAddIncludeSubmit={handleAddCriteria}
              onAddExcludeSubmit={handleAddExcludeCriteria}
              disabled={!audienceQuery.data?.toBeUsed}
              audienceId={audienceId}
              onOpen={() => {
                // Implementation of onOpen function
              }}
            />
            {audienceQuery.data && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <DotsVerticalIcon />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="flex w-32 flex-col p-0">
                  <Button
                    variant="ghost"
                    className="w-full justify-start border-b "
                    onClick={() => {
                      deleteAudienceMutation.mutate({
                        adAudienceId: audienceId,
                      });
                    }}
                  >
                    <TrashIcon width="16" className="mr-2" />
                    Delete
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    onClick={() => {
                      duplicateAudienceMutation.mutate({
                        adAudienceId: audienceId,
                        adSegmentId: audienceQuery.data.linkedInAdSegmentId,
                      });
                    }}
                  >
                    <CopyIcon width="16" className="mr-2" />
                    Duplicate
                  </Button>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>
        <div className="flex w-full items-center justify-between">
          {targetingCriteria && audienceQuery.data && (
            <AudienceCount
              targetingCriteria={targetingCriteria}
              editingInProgress={editingInProgress}
              dbData={audienceQuery.data.audienceTargetCriteria}
            />
          )}
        </div>
      </div>
      {audienceQuery.data && (
        <>
          <AudienceTargets
            audienceId={audienceId}
            targetingCriteria={
              targetingCriteria ?? {
                include: {
                  and: [],
                },
                exclude: {
                  or: [],
                },
              }
            }
            setAudienceTargetingCriteria={setTargetingCriteria}
          />
          {targetingCriteria?.exclude && (
            <ExcludedTargets
              audienceId={audienceId}
              targetingCriteria={targetingCriteria}
              setAudienceTargetingCriteria={setTargetingCriteria}
            />
          )}
        </>
      )}
    </div>
  );
}

export function AudienceTargetingDialog({
  audienceId,
  isOpen,
  setIsOpen,
  onAddIncludeSubmit,
  onAddExcludeSubmit,
  disabled,
  onOpen,
}: {
  audienceId: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onAddIncludeSubmit: (data: {
    facetUrn: string;
    entites: {
      entityUrn: string;
      entityName: string;
    }[];
    facetName: string;
  }) => void;
  onAddExcludeSubmit: (data: {
    facetUrn: string;
    entites: {
      entityUrn: string;
      entityName: string;
    }[];
    facetName: string;
  }) => void;
  disabled: boolean;
  onOpen?: () => void | Promise<void>;
}) {
  const [mode, setMode] = useState<
    "VIEW_FACETS" | "VIEW_ENTITIES" | "VIEW_SEGMENT"
  >("VIEW_FACETS");
  const facetsQuery =
    api.v2.ads.linkedInApi.adTargeting.getAdTargetingFacets.useQuery();

  const [selectedFacet, setSelectedFacet] = useState<{
    facetUrn: string;
    finders: ("TYPEAHEAD" | "AD_TARGETING_FACET")[];
  } | null>(null);

  const [selectedSegment, setSelectedSegment] = useState<
    "BULK" | "RETARGETING" | "MARKET_AUTOMATION" | null
  >(null);

  function handleFacetClick(
    facetUrn: string,
    finders: ("TYPEAHEAD" | "AD_TARGETING_FACET")[],
  ) {
    setSelectedFacet({ facetUrn, finders });
    setSelectedSegment(null);
    setMode("VIEW_ENTITIES");
  }

  useEffect(() => {
    setMode("VIEW_FACETS");
    setSelectedFacet(null);
    setIncludeOrExclude(null);
    setSelectedSegment(null);
  }, [isOpen]);

  function handleSegmentClick(
    segmentType: "BULK" | "RETARGETING" | "MARKET_AUTOMATION",
  ) {
    setMode("VIEW_SEGMENT");
    setSelectedSegment(segmentType);
  }

  const [includeOrExclude, setIncludeOrExclude] = useState<
    "INCLUDE" | "EXCLUDE" | null
  >(null);

  function onSubmit(data: {
    facetUrn: string;
    entites: {
      entityUrn: string;
      entityName: string;
    }[];
    facetName: string;
  }) {
    if (includeOrExclude == "INCLUDE") {
      onAddIncludeSubmit(data);
    } else if (includeOrExclude == "EXCLUDE") {
      onAddExcludeSubmit(data);
    }
    setIsOpen(false);
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          disabled={disabled}
          variant="outline"
          size="sm"
          className="border-blue-200 bg-blue-50 font-normal text-primary hover:bg-blue-100"
          onClick={onOpen}
        >
          <PlusIcon width="14" className="mr-1" />
          Add criteria
        </Button>
      </DialogTrigger>
      <DialogContent>
        {includeOrExclude !== null && (
          <>
            {mode == "VIEW_FACETS" && (
              <>
                <DialogHeader>
                  <DialogTitle>Add Criteria</DialogTitle>
                </DialogHeader>
                <ul className="flex h-[500px] w-full flex-col items-start justify-start overflow-auto border-t">
                  {facetsQuery.data?.map((facet) => (
                    <li key={facet.adTargetingFacetUrn} className="flex w-full items-center justify-start border-x border-b">
                      <Button
                        variant="ghost"
                        className="h-full w-full justify-start"
                        onClick={() =>
                          handleFacetClick(
                            facet.adTargetingFacetUrn,
                            facet.availableEntityFinders,
                          )
                        }
                      >
                        {getFriendlyFacetName(camelCaseToWords(facet.facetName))}
                      </Button>
                    </li>
                  ))}
                  <li className="flex w-full items-center justify-start border-x border-b">
                    <Button
                      variant="ghost"
                      className="h-full w-full justify-start"
                      onClick={() => handleSegmentClick("BULK")}
                    >
                      List Upload
                    </Button>
                  </li>
                  <li className="flex w-full items-center justify-start border-x border-b">
                    <Button
                      variant="ghost"
                      className="h-full w-full justify-start"
                      onClick={() => handleSegmentClick("RETARGETING")}
                    >
                      Retargeting
                    </Button>
                  </li>
                  <li className="flex w-full items-center justify-start border-x border-b">
                    <Button
                      variant="ghost"
                      className="h-full w-full justify-start"
                      onClick={() => handleSegmentClick("MARKET_AUTOMATION")}
                    >
                      Third Party
                    </Button>
                  </li>
                </ul>
              </>
            )}
            {mode == "VIEW_ENTITIES" && selectedFacet && (
              <AudienceFacetEntities
                audienceId={audienceId}
                facetUrn={selectedFacet.facetUrn}
                finders={selectedFacet.finders}
                facetName={facetUrnToReadableName(selectedFacet.facetUrn)}
                setMode={setMode}
                onSubmit={onSubmit}
              />
            )}
            {mode == "VIEW_SEGMENT" && selectedSegment && (
              <AudienceSegments
                types={selectedSegment}
                setMode={setMode}
                onSubmit={onSubmit}
              />
            )}
          </>
        )}
        {includeOrExclude === null && (
          <>
            <DialogHeader>
              <DialogTitle>Add Criteria</DialogTitle>
              <DialogDescription>
                Select the type of criteria you want to add
              </DialogDescription>
            </DialogHeader>
            <div className="flex w-full flex-col items-center justify-start gap-x-2">
              <Button
                onClick={() => setIncludeOrExclude("INCLUDE")}
                variant="outline"
                className="border-b-none w-full shadow-none"
              >
                Include
              </Button>
              <Button
                onClick={() => setIncludeOrExclude("EXCLUDE")}
                variant="outline"
                className="border-t-none w-full shadow-none"
              >
                Exclude
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}

export function AudienceCount({
  targetingCriteria,
  editingInProgress,
  dbData,
}: {
  targetingCriteria: AudienceTargetingCriteria;
  editingInProgress: boolean;
  dbData: AudienceTargetingCriteria;
}) {
  const audienceCountQuery = api.v2.ads.linkedInApi.getAudienceCount.useQuery(
    {
      targetingCriteria,
    },
    {
      enabled: !editingInProgress,
      placeholderData: (prev) => {
        return prev;
      },
    },
  );

  return (
    <>
      <h1 className="flex items-center justify-start gap-x-2  text-xs">
        Audience Size:{" "}
        {!editingInProgress && (
          <>
            <b>
              {audienceCountQuery.data
                ?.toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
            </b>
            Members
          </>
        )}
        {editingInProgress && <Skeleton className="h-4 w-20 bg-secondary" />}
      </h1>
    </>
  );
}

function AudienceFacetEntities({
  audienceId,
  facetUrn,
  facetName,
  finders,
  setMode,
  onSubmit,
}: {
  audienceId: string;
  facetUrn: string;
  facetName: string;
  finders: ("TYPEAHEAD" | "AD_TARGETING_FACET")[];
  setMode: (mode: "VIEW_FACETS" | "VIEW_ENTITIES" | "VIEW_SEGMENT") => void;
  onSubmit: (data: {
    facetUrn: string;
    entites: {
      entityUrn: string;
      entityName: string;
    }[];
    facetName: string;
  }) => void;
}) {
  const [typeaheadQueryInput, setTypeaheadQueryInput] = useState<string>("");

  const [selectedEntities, setSelectedEntities] = useState<
    {
      entityUrn: string;
      entityName: string;
    }[]
  >([]);

  let entitiesQuery

  if (facetUrn === LINKEDIN_LOCATIONS_FACET_URN) {
    entitiesQuery = { data: defaultLocations };
  }
  else {
    entitiesQuery = finders.includes("AD_TARGETING_FACET")
      ? api.v2.ads.linkedInApi.adTargeting.getAdTargetingFacetEntitiesForFacet.useQuery(
        {
          facetUrn: facetUrn,
        },
      )
      : null;
  }

  const typeaheadQuery = finders.includes("TYPEAHEAD")
    ? api.v2.ads.linkedInApi.adTargeting.getAdTargetingFacetEntitiesByTypehead.useQuery(
      {
        facetUrn: facetUrn,
        query: typeaheadQueryInput,
      },
    )
    : null;


  return (
    <>
      <DialogHeader>
        <Button
          variant="ghost"
          size="icon"
          className="h-4 w-4 hover:bg-transparent"
          onClick={() => setMode("VIEW_FACETS")}
        >
          <ArrowLeft width="16" />
        </Button>
        <DialogTitle>Add Criteria</DialogTitle>
        <DialogDescription>
          {getFriendlyFacetName(camelCaseToWords(facetName.split(":")[3] ?? ""))}
        </DialogDescription>
      </DialogHeader>
      {finders.includes("TYPEAHEAD") && (
        <Input
          placeholder="Search..."
          value={typeaheadQueryInput}
          onChange={(e) => setTypeaheadQueryInput(e.target.value)}
          className="w-full"
        />
      )}
      <ul className="flex h-[500px] w-full flex-col items-start justify-start overflow-auto border-t">
        {typeaheadQueryInput == "" &&
          entitiesQuery?.data?.map((entity: { urn: string; name: string }) => (
            <li key={entity.urn} className="flex w-full items-center justify-start border-x border-b">
              <Button
                variant="ghost"
                className={cn(
                  selectedEntities.some((e) => e.entityUrn === entity.urn)
                    ? "bg-blue-100"
                    : "",
                  "h-full w-full justify-start",
                )}
                onClick={() => {
                  if (
                    selectedEntities.some((e) => e.entityUrn === entity.urn)
                  ) {
                    setSelectedEntities((prev) =>
                      prev.filter((e) => e.entityUrn !== entity.urn),
                    );
                  } else {
                    setSelectedEntities((prev) => [
                      ...prev,
                      { entityUrn: entity.urn, entityName: entity.name },
                    ]);
                  }
                }}
              >
                {entity.name}
              </Button>
            </li>
          ))}
        {typeaheadQueryInput != "" &&
          typeaheadQuery?.data?.map((entity) => (
            <li key={entity.urn} className="flex w-full items-center justify-start border-x border-b">
              <Button
                variant="ghost"
                className={cn(
                  selectedEntities.some((e) => e.entityUrn === entity.urn)
                    ? "bg-blue-100"
                    : "",
                  "h-full w-full justify-start",
                )}
                onClick={() => {
                  if (
                    selectedEntities.some((e) => e.entityUrn === entity.urn)
                  ) {
                    setSelectedEntities((prev) =>
                      prev.filter((e) => e.entityUrn !== entity.urn),
                    );
                  } else {
                    setSelectedEntities((prev) => [
                      ...prev,
                      { entityUrn: entity.urn, entityName: entity.name },
                    ]);
                  }
                }}
              >
                {entity.name}
              </Button>
            </li>
          ))}
        {typeaheadQuery &&
          typeaheadQueryInput != "" &&
          typeaheadQuery.isLoading && <span>Loading...</span>}
        {entitiesQuery &&
          typeaheadQueryInput == "" &&
          entitiesQuery.isLoading && <span>Loading...</span>}
      </ul>
      {selectedEntities.length > 0 && (
        <div className="flex w-full flex-col items-start justify-start  space-y-2">
          <p className="text-xs">
            <b>Select Entities: </b>
            {selectedEntities.map((each) => (
              <Chip
                key={each.entityUrn}
                label={each.entityName}
                onRemove={() => setSelectedEntities(prev => prev.filter(e => e.entityUrn !== each.entityUrn))}
              />
            ))}
          </p>
          <Button
            onClick={() =>
              onSubmit({ facetUrn, entites: selectedEntities, facetName })
            }
          >
            Add
          </Button>
        </div>
      )}
    </>
  );
}

function AudienceSegments({
  types,
  onSubmit,
  setMode,
}: {
  types: "BULK" | "RETARGETING" | "MARKET_AUTOMATION";
  onSubmit: (data: {
    facetUrn: string;
    entites: {
      entityUrn: string;
      entityName: string;
    }[];
    facetName: string;
  }) => void;
  setMode: (mode: "VIEW_FACETS" | "VIEW_ENTITIES" | "VIEW_SEGMENT") => void;
}) {
  const linkedInApiAudienceSegmentsQuery =
    api.v2.ads.linkedInApi.adTargeting.getAudienceSegments.useQuery({
      types: types,
      start: 0,
      count: 100,
    });

  return (
    <>
      <DialogHeader>
        <Button
          variant="ghost"
          size="icon"
          className="h-4 w-4 hover:bg-transparent"
          onClick={() => setMode("VIEW_FACETS")}
        >
          <ArrowLeft width="16" />
        </Button>
        <DialogTitle>Add Criteria</DialogTitle>
      </DialogHeader>
      <ul className="flex h-[500px] w-full flex-col items-start justify-start overflow-auto border-t">
        {linkedInApiAudienceSegmentsQuery.data?.elements
          .filter((segment) => segment.status == "READY")
          .map((segment) => (
            <li key={segment.id} className="flex w-full items-center justify-start border-x border-b">
              <Button
                variant="ghost"
                className="h-full w-full justify-start"
                onClick={() =>
                  onSubmit({
                    facetUrn:
                      types == "BULK"
                        ? `urn:li:adTargetingFacet:audienceMatchingSegments`
                        : types == "RETARGETING"
                          ? "urn:li:adTargetingFacet:dynamicSegments"
                          : "urn:li:adTargetingFacet:audienceMatchingSegments",
                    entites: [
                      {
                        entityUrn: `urn:li:adSegment:${segment.id}`,
                        entityName: segment.name,
                      },
                    ],
                    facetName: "Audience Segments",
                  })
                }
              >
                {segment.name}
              </Button>
            </li>
          ))}
      </ul>
    </>
  );
}

export function ExcludedTargets({
  audienceId,
  useCompleteMutation, // includes linkedin updates - used from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/_components/Audience"
  targetingCriteria,
  setAudienceTargetingCriteria,
}: {
  audienceId: string;
  useCompleteMutation?: boolean;
  targetingCriteria: AudienceTargetingCriteria | null;
  setAudienceTargetingCriteria: Dispatch<
    SetStateAction<AudienceTargetingCriteria | null>
  >;
}) {
  return (
    <div className="flex w-full flex-col items-start justify-start gap-y-2 rounded-md pt-2">
      <h1 className="text-sm font-medium">Excluded:</h1>

      <div className="flex w-full flex-wrap items-start justify-start gap-4">
        {targetingCriteria?.exclude?.or.map((orGroup) => (
          <div key={orGroup.facetUrn} className="flex flex-col items-start justify-start gap-y-2 rounded-md bg-secondary p-2">
            <div className="flex items-center justify-between">
              <h1 className="text-sm font-medium">{getFriendlyFacetName(orGroup.facetName)}</h1>
            </div>
            <AudienceEntites
              audienceId={audienceId}
              entities={orGroup.facetEntites}
              mode="EXCLUDE"
              useCompleteMutation={useCompleteMutation}
              targetingCriteria={targetingCriteria}
              setTargetingCriteria={setAudienceTargetingCriteria}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

export function AudienceTargets({
  audienceId,
  useCompleteMutation,
  targetingCriteria,
  setAudienceTargetingCriteria,
}: {
  audienceId: string;
  useCompleteMutation?: boolean;
  targetingCriteria: AudienceTargetingCriteria | null;
  setAudienceTargetingCriteria: Dispatch<
    SetStateAction<AudienceTargetingCriteria | null>
  >;
}) {
  return (


    <div className="flex w-full flex-wrap items-start justify-start gap-4">
      {targetingCriteria?.include.and.map((orGroup, index) => (
        <div key={index} className="flex flex-col items-start justify-start gap-y-2 rounded-md bg-secondary p-2">
          {orGroup.or.length > 1 && (
            <h1>UNSUPPORTED TO HAVE MORE THAN 1 FACET IN OR GROUP</h1>
          )}
          {orGroup.or.length === 1 && orGroup.or[0] && (
            <>
              <div className="flex items-center justify-between">
                <h1 className="text-sm font-medium">
                  {getFriendlyFacetName(orGroup.or[0].facetName)}
                </h1>
              </div>
              <AudienceEntites
                audienceId={audienceId}
                entities={orGroup.or[0].facetEntites}
                mode="INCLUDE"
                targetingCriteria={targetingCriteria}
                setTargetingCriteria={setAudienceTargetingCriteria}
              />
            </>
          )}
        </div>
      ))}
    </div>
  );
}

function AudienceEntites({
  audienceId,
  entities,
  mode,
  useCompleteMutation,
  targetingCriteria,
  setTargetingCriteria,
}: {
  audienceId: string;
  entities: {
    entityUrn: string;
    entityName: string;
  }[];
  mode: "INCLUDE" | "EXCLUDE";
  useCompleteMutation?: boolean;
  targetingCriteria: AudienceTargetingCriteria | null;
  setTargetingCriteria: Dispatch<
    SetStateAction<AudienceTargetingCriteria | null>
  >;
}) {
  /**
   * Implements optimistic UI for audience entity management:
   * - Maintains local state that updates immediately when users delete entities
   * - Shows instant feedback in the UI while backend requests happen asynchronously
   * - Automatically reverts changes if the backend operation fails
   * - Prevents multiple clicks on the same entity while deletion is in progress
   * - Handles error cases with appropriate rollback to maintain UI consistency
   */
  const audienceQuery = api.v2.ads.adAudience.getOne.useQuery({
    id: audienceId,
  });

  function deleteEntity(entityUrn: string, mode: "INCLUDE" | "EXCLUDE") {
    // Optimistically update UI immediately

    if (mode === "INCLUDE") {
      deleteEntityFromInclude(entityUrn);
    } else {
      deleteEntityFromExclude(entityUrn);
    }
  }

  function deleteEntityFromInclude(entityUrn: string) {
    const currentTargetCriteria = structuredClone(targetingCriteria);
    if (!currentTargetCriteria) {
      return;
    }

    // Count total entities in include criteria
    const totalIncludeEntities = currentTargetCriteria.include.and.reduce(
      (total, andGroup) => {
        return (
          total +
          andGroup.or.reduce(
            (orTotal, orGroup) => orTotal + orGroup.facetEntites.length,
            0,
          )
        );
      },
      0,
    );

    // Find the AND group containing the entity
    const foundOrGroupIndex = currentTargetCriteria.include.and.findIndex(
      (and) =>
        and.or.find((or) =>
          or.facetEntites.some((entity) => entity.entityUrn === entityUrn),
        ),
    );

    if (foundOrGroupIndex === -1) {
      return;
    }

    const foundOrGroup = currentTargetCriteria.include.and[foundOrGroupIndex];
    if (!foundOrGroup) {
      return;
    }

    // Find the facet group containing the entity
    const foundFacetGroupIndex = foundOrGroup.or.findIndex((or) =>
      or.facetEntites.some((entity) => entity.entityUrn === entityUrn),
    );

    if (foundFacetGroupIndex === -1) {
      return;
    }

    const foundFacetGroup = foundOrGroup.or[foundFacetGroupIndex];
    if (!foundFacetGroup) {
      return;
    }

    if (foundFacetGroup.facetUrn === LINKEDIN_LOCATIONS_FACET_URN && foundFacetGroup.facetEntites.length === 1) {
      let totalLocationEntities = 0;
      currentTargetCriteria.include.and.forEach(andG => {
        if (andG && andG.or) {
          andG.or.forEach(orG => {
            if (orG && orG.facetUrn === LINKEDIN_LOCATIONS_FACET_URN && orG.facetEntites) {
              totalLocationEntities += orG.facetEntites.length;
            }
          });
        }
      });

      if (totalLocationEntities === 1) {
        alert("Cannot remove the last location. At least one location criterion is required for LinkedIn Audiences.");
        return;
      }
    }

    // Remove the entity
    foundFacetGroup.facetEntites = foundFacetGroup.facetEntites.filter(
      (entity) => entity.entityUrn !== entityUrn,
    );

    // If no entities left in the facet group, remove it from the OR array
    if (foundFacetGroup.facetEntites.length === 0) {
      foundOrGroup.or = foundOrGroup.or.filter(
        (or) => or.facetUrn !== foundFacetGroup.facetUrn,
      );
    }

    // If no OR groups left in the AND group, remove the AND group
    if (foundOrGroup.or.length === 0) {
      currentTargetCriteria.include.and =
        currentTargetCriteria.include.and.filter(
          (_, index) => index !== foundOrGroupIndex,
        );
    }

    // Ensure we always have at least one AND group
    if (currentTargetCriteria.include.and.length === 0) {
      console.warn("Cannot delete the last AND group");
      return;
    }

    const parsedTargetCriteria = linkedInAudienceTargetCriteriaSchema.safeParse(
      currentTargetCriteria,
    );
    if (!parsedTargetCriteria.success) {
      console.error(parsedTargetCriteria.error);
      alert(
        "Error parsing target criteria, please contact support. Error: " +
        parsedTargetCriteria.error.message,
      );
      return;
    }
    console.log("DATA", parsedTargetCriteria.data);

    setTargetingCriteria(parsedTargetCriteria.data);
  }

  function deleteEntityFromExclude(entityUrn: string) {
    const currentTargetCriteria = structuredClone(targetingCriteria);
    if (!currentTargetCriteria) {
      return;
    }

    const exclude = currentTargetCriteria.exclude;
    if (!exclude) {
      return;
    }

    const foundFacetGroup = exclude.or.find((or) =>
      or.facetEntites.some((entity) => entity.entityUrn === entityUrn),
    );
    if (!foundFacetGroup) {
      return;
    }

    foundFacetGroup.facetEntites = foundFacetGroup.facetEntites.filter(
      (entity) => entity.entityUrn !== entityUrn,
    );

    if (foundFacetGroup.facetEntites.length === 0) {
      exclude.or = exclude.or.filter((or) => or !== foundFacetGroup);
    }

    if (exclude.or.length === 0) {
      currentTargetCriteria.exclude = undefined;
    }

    const mutationId = uuidv7();

    setTargetingCriteria(currentTargetCriteria);
  }

  // Common handler for deletion failures

  return (
    <div className="flex max-w-full flex-wrap gap-1">
      {entities.map((entity) => (
        <Chip
          key={entity.entityUrn}
          label={entity.entityName}
          disabled={!audienceQuery.data}
          onRemove={() => deleteEntity(entity.entityUrn, mode)}
        />
      ))}
    </div>
  );
}

export function AudienceHeader({
  audienceTargetingCriteria,
  audienceIndex,
}: {
  audienceTargetingCriteria: AudienceTargetingCriteria;
  audienceIndex: number;
}) {
  return (
    <div className="text-sm font-medium">
      <span>Audience {audienceIndex}: </span>
      {audienceTargetingCriteria?.include?.and?.map((andGroup, andIndex) => (
        <React.Fragment key={andIndex}>
          {andIndex > 0 && <span className="mx-1 font-bold">AND</span>}
          {andGroup.or.map((orGroup, orIndex) => (
            <React.Fragment key={orIndex}>
              {orIndex > 0 && <span className="mx-1">OR</span>}
              <span>{getFriendlyFacetName(orGroup.facetName)}</span>
            </React.Fragment>
          ))}
        </React.Fragment>
      ))}
    </div>
  );
}

function SegmentDetails({
  row,
}: {
  row: {
    original: {
      name?: string | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  };
}) {

  if (!row.original.name) {
    const nameArray: string[] = [];

    if (row.original.jobFunction) {
      nameArray.push(snakeCaseToWords(row.original.jobFunction));
    }
    if (row.original.jobSeniority) {
      nameArray.push(snakeCaseToWords(row.original.jobSeniority));
    }

    if (row.original.verticals) {
      nameArray.push(...row.original.verticals);
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    if (nameArray.length == 0) {
      return (
        <div className="space-y-1">
          <span className="w-[80px]">All</span>
        </div>
      );
    }
    return (
      <div className="w-full space-y-1 text-wrap py-1">
        <span className="">{nameArray.join(" • ")}</span>
      </div>
    );
  }
  return (
    <div className="space-y-1">
      <span className="w-[80px]">{row.original.name}</span>
    </div>
  );
}

function camelCaseToWords(camelCase: string) {
  return camelCase
    .replace(/([a-z])([A-Z])/g, "$1 $2")
    .replace(/^./, (str) => str.toUpperCase())
    .replace(/ ([a-z])/g, (match) => match.toUpperCase());
}

/**
 * Handles adding criteria to an audience's targeting
 */
export function handleAddCriteria(
  data: AddCriteriaData,
  targetingCriteria: AudienceTargetingCriteria | null,
  setTargetingCriteria: Dispatch<
    SetStateAction<AudienceTargetingCriteria | null>
  >,
) {
  if (!targetingCriteria) {
    return;
  }

  // Find if there's an existing AND group containing this facet
  const foundOrGroupWhereFacetExists = targetingCriteria.include.and.find(
    (and) => and.or.find((or) => or.facetUrn === data.facetUrn),
  );

  if (foundOrGroupWhereFacetExists) {
    // Find the specific facet group within the AND group
    const foundFacetGroup = foundOrGroupWhereFacetExists.or.find(
      (or) => or.facetUrn === data.facetUrn,
    );

    if (foundFacetGroup) {
      // Add new entities to existing facet group
      foundFacetGroup.facetEntites.push(
        ...data.entites.map((entity) => ({
          facetUrn: data.facetUrn,
          entityUrn: entity.entityUrn,
          entityName: entity.entityName,
        })),
      );
    } else {
      // Create new facet group in existing AND group
      foundOrGroupWhereFacetExists.or.push({
        facetUrn: data.facetUrn,
        facetName: data.facetName,
        facetEntites: data.entites.map((entity) => ({
          facetUrn: data.facetUrn,
          entityUrn: entity.entityUrn,
          entityName: entity.entityName,
        })),
      });
    }
  } else {
    // Create new AND group with new facet
    targetingCriteria.include.and.push({
      or: [
        {
          facetUrn: data.facetUrn,
          facetName: data.facetName,
          facetEntites: data.entites.map((entity) => ({
            facetUrn: data.facetUrn,
            entityUrn: entity.entityUrn,
            entityName: entity.entityName,
          })),
        },
      ],
    });
  }

  setTargetingCriteria(targetingCriteria);
}

/**
 * Handles adding exclude criteria to an audience's targeting
 */
export function handleAddExcludeCriteria(
  data: AddCriteriaData,
  targetingCriteria: AudienceTargetingCriteria | null,
  setTargetingCriteria: Dispatch<
    SetStateAction<AudienceTargetingCriteria | null>
  >,
) {
  if (!targetingCriteria) {
    return;
  }
  console.log("data", data);
  console.log("t2", targetingCriteria);
  const excludeTargets = targetingCriteria.exclude ?? {
    or: [],
  };

  const foundFacet = excludeTargets.or.find(
    (or) => or.facetUrn === data.facetUrn,
  );

  if (!targetingCriteria.exclude) {
    targetingCriteria.exclude = excludeTargets;
  }
  console.log("found facet", foundFacet);
  if (foundFacet) {
    // Add new entities to existing facet group
    foundFacet.facetEntites.push(
      ...data.entites.map((entity) => ({
        facetUrn: data.facetUrn,
        entityUrn: entity.entityUrn,
        entityName: entity.entityName,
      })),
    );
  } else {
    // Create new facet group with all entities
    excludeTargets.or.push({
      facetUrn: data.facetUrn,
      facetName: data.facetName,
      facetEntites: data.entites.map((entity) => ({
        facetUrn: data.facetUrn,
        entityUrn: entity.entityUrn,
        entityName: entity.entityName,
      })),
    });
  }

  const mutationId = uuidv7();
  console.log("TARGET CRITERIA", targetingCriteria);
  setTargetingCriteria(targetingCriteria);
}

/**
 * Converts a LinkedIn targeting facet URN to a readable name
 * Example: "urn:li:adTargetingFacet:companyIndustry" becomes "Company Industry"
 */
function facetUrnToReadableName(urn: string) {
  // Extract the camel case part after the last colon
  const match = urn.match(/^urn:li:adTargetingFacet:([^:]+)$/);

  if (!match) {
    return "Unknown Facet";
  }

  const camelCaseName = match[1];

  if (!camelCaseName) {
    return "Unknown Facet";
  }

  // Convert camel case to words
  return camelCaseName
    .replace(/([a-z])([A-Z])/g, "$1 $2")
    .replace(/^./, (str) => str.toUpperCase())
    .replace(/ ([a-z])/g, (match) => match.toUpperCase());
}

export function compareTargetingCriteria(
  local: AudienceTargetingCriteria,
  server: AudienceTargetingCriteria,
) {
  const localIncludeEntityUrns = getEntitiesFromInclude(local.include);
  const serverIncludeEntityUrns = getEntitiesFromInclude(server.include);
  const localExcludeEntityUrns = getEntitiesFromExclude(local.exclude);
  const serverExcludeEntityUrns = getEntitiesFromExclude(server.exclude);
  if (localIncludeEntityUrns.length !== serverIncludeEntityUrns.length) {
    return false;
  }
  if (localExcludeEntityUrns.length !== serverExcludeEntityUrns.length) {
    return false;
  }
  for (const localEntityUrn of localIncludeEntityUrns) {
    if (!serverIncludeEntityUrns.includes(localEntityUrn)) {
      return false;
    }
  }
  for (const localEntityUrn of localExcludeEntityUrns) {
    if (!serverExcludeEntityUrns.includes(localEntityUrn)) {
      return false;
    }
  }
  return true;
}

function getEntitiesFromInclude(include: AudienceTargetingCriteria["include"]) {
  const includeEntityUrns = [];
  for (const and of include.and) {
    for (const or of and.or) {
      for (const entity of or.facetEntites) {
        includeEntityUrns.push(entity.entityUrn);
      }
    }
  }
  return includeEntityUrns;
}

function getEntitiesFromExclude(exclude: AudienceTargetingCriteria["exclude"]) {
  const excludeEntityUrns: string[] = [];
  if (!exclude) {
    return excludeEntityUrns;
  }
  for (const or of exclude.or) {
    for (const entity of or.facetEntites) {
      excludeEntityUrns.push(entity.entityUrn);
    }
  }
  return excludeEntityUrns;
}
