"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "@/trpc/client";
import { FaceIcon, PaddingIcon } from "@radix-ui/react-icons";
import { format, subDays, subMonths } from "date-fns";
import {
  ArrowLeft,
  CalendarIcon,
  ImageIcon,
  MessageCircleIcon,
  XIcon,
} from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import { Calendar } from "@kalos/ui/calender";
import { cn } from "@kalos/ui/index";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { Skeleton } from "@kalos/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@kalos/ui/tabs";

// Import custom components
import ActiveAdsTable from "../_components/ActiveAdsTable";
import PastAdsSection from "../_components/PastAdsSection";
import SegmentHeader from "../_components/SegmentHeader";
import UpcomingLearningGoalsTable from "../_components/UpcomingLearningGoalsTable";
import { AdFormatType, SegmentDetail } from "../_types";
// Import custom hooks and utilities
import {
  useAdSegmentPageData,
  useSegmentPageData,
  useUpcomingTests,
} from "../_utils/hooks";
import { mapMetrics } from "../_utils/mappers";

/**
 * Ad Segment Detail Page Component
 * Shows detailed information about a specific ad segment, including:
 * - Current running AB tests and stages
 * - Performance metrics for ad variants
 * - Past and upcoming tests
 */

function useDebounce<T>(value: T, delay: number): T | undefined {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
}

export default function AdSegmentDetailPage({
  params,
}: {
  params: { adSegmentId: string };
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selected, setSelected] = useState<"Running" | "Completed">("Running");

  const [currentTab, setCurrentTab] = useState("active");
  const [expandedRounds, setExpandedRounds] = useState<Record<string, boolean>>(
    {},
  );

  const midCampaignNotification =
    api.v2.ads.adSegmentMidCampaignNotification.getOne.useQuery({
      adSegmentId: params.adSegmentId,
    });

  // New date range state (replaces timePeriod)
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  // Extract segment metadata from URL parameters
  const segmentName = searchParams.get("name") || "Unknown Segment";
  const segmentBudget = parseFloat(searchParams.get("budget") || "0");
  const segmentFormat = searchParams.get("format");
  const coreSegmentId = searchParams.get("segmentId") || "";
  const adProgramObjectiveType = searchParams.get("objectiveType");
  const startDate = searchParams.get("startDate") || new Date().toISOString();
  const endDate = searchParams.get("endDate") || "";
  const adProgramId = searchParams.get("adProgramId") || "";
  const totalSpent = searchParams.get("totalSpent") || "0";
  const segmentStatus = (searchParams.get("status") || "ACTIVE") as
    | "ACTIVE"
    | "PAUSED"
    | "COMPLETED";
  const [cleanedSegmentFormat, setCleanedSegmentFormat] =
    useState<AdFormatType>("SINGLE_IMAGE");

  // Create segment detail from URL parameters
  const segmentDetailFromParams = useMemo<SegmentDetail>(
    () => ({
      adProgramId: "", // We don't have this info from the table
      adSegmentId: params.adSegmentId,
      name: segmentName,
      budget: segmentBudget,
      startDate: startDate,
      endDate: endDate,
      status: segmentStatus,
      adProgramType: "EVERGREEN",
      metrics: {
        totalImpressions: 0,
        totalClicks: 0,
        totalEngagements: 0,
        totalLeads: 0,
        totalSpent: parseFloat(totalSpent),
      },
    }),
    [params.adSegmentId, segmentName, segmentBudget, startDate, segmentStatus],
  );

  const debouncedFrom = useDebounce(dateRange.from ?? undefined, 300);
  const debouncedTo = useDebounce(dateRange.to ?? undefined, 300);

  useEffect(() => {
    console.log("🔍 debouncedFrom", debouncedFrom);
    console.log("🔍 debouncedTo", debouncedTo);
  }, [debouncedFrom, debouncedTo]);
  // Get AB test data using custom hook (but not segment details)
  const { currentAbTest, isLoading, isError, error } = useAdSegmentPageData(
    params.adSegmentId,
    adProgramId,
    cleanedSegmentFormat,
    debouncedFrom,
    debouncedTo,
  );

  const getPastRanAbTests = api.v2.ads.abTest.getPastRanAbTests.useQuery(
    {
      adSegmentId: params.adSegmentId,
      fromDate: undefined,
      toDate: undefined,
    },
    {
      enabled: !!params.adSegmentId,
      refetchInterval: 60000, // Refresh data every minute
    },
  );

  // const segmentPageData = useSegmentPageData(params.adSegmentId);

  // Initialize expanded state for rounds when data loads
  useEffect(() => {
    if (currentAbTest?.rounds) {
      setExpandedRounds(
        Object.fromEntries(
          currentAbTest.rounds.map((round) => [
            round.id,
            round.status === "IN_PROGRESS",
          ]),
        ),
      );
    }
  }, [currentAbTest]);

  useEffect(() => {
    console.log("Get Past Ran Ab Tests", getPastRanAbTests.data);
  }, [getPastRanAbTests.data]);
  // Log current data for debugging
  useEffect(() => {
    console.log("Ad Segment ID:", params.adSegmentId);
    console.log("Segment Detail from Params:", segmentDetailFromParams);
    console.log("Current AB Test Data:", currentAbTest);
    console.log("Current AB Test Data:", isLoading);

    // Might not need state for segmentFormat
    setCleanedSegmentFormat(segmentFormat as AdFormatType);
  }, [params.adSegmentId, segmentDetailFromParams, currentAbTest]);

  // Handle error state
  if (isError) {
    return (
      <div className="h-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-red-600">Error</h1>
          <p className="mt-2 text-muted-foreground">
            {error ? String(error) : "Failed to load ad segment details"}
          </p>
          <Button
            className="mt-4"
            variant="secondary"
            onClick={() => router.back()}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Performance
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex max-h-screen w-full max-w-[100%] flex-col overflow-x-hidden">
      {/* Main Content */}

      <main className="flex-1 p-4 md:p-6">
        {/* Segment Header */}
        <SegmentHeader
          segmentDetail={segmentDetailFromParams}
          router={router}
        />
        {/* Tabs for Ads */}
        <div className="mb-4 flex justify-between">
          <Tabs
            defaultValue="active"
            className="w-full"
            onValueChange={setCurrentTab}
          >
            <div className="my-6 flex  items-center justify-between overflow-x-hidden">
              <div className="">
                <div className="flex items-center">
                  <TabsList className="flex overflow-hidden border border-gray-300 bg-white">
                    <TabsTrigger
                      value="active"
                      onMouseDown={() => setSelected("Running")}
                      className={cn(
                        " px-4 py-2 text-sm font-medium",
                        selected === "Running"
                          ? " bg-white text-blue-600 hover:text-blue-600"
                          : "bg-gray-100 text-black hover:text-black",
                        " last:rounded-r-md",
                      )}
                    >
                      Active Ads
                    </TabsTrigger>
                    <TabsTrigger
                      value="past"
                      onMouseDown={() => setSelected("Completed")}
                      className={cn(
                        "border-0 px-4 py-2 text-sm font-medium",
                        selected === "Completed"
                          ? "bg-white text-blue-600 hover:text-blue-600"
                          : "bg-gray-100 text-black hover:text-black",
                        " last:rounded-r-md",
                      )}
                    >
                      Past Ads
                    </TabsTrigger>
                  </TabsList>
                </div>
              </div>
              <div className="flex  items-center gap-0">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="date"
                      variant={"outline"}
                      className={cn(
                        "w-[260px] justify-start text-left font-normal",
                        !dateRange.from &&
                          !dateRange.to &&
                          "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "LLL dd, y")} -{" "}
                            {format(dateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRange.from, "LLL dd, y")
                        )
                      ) : (
                        "Select Date Range"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="end">
                    <div className="border-b border-border p-2">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setDateRange({
                              from: subDays(new Date(), 7),
                              to: new Date(),
                            })
                          }
                        >
                          Last 1 Week
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setDateRange({
                              from: subMonths(new Date(), 1),
                              to: new Date(),
                            })
                          }
                        >
                          Last 1 Month
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setDateRange({
                              from: subMonths(new Date(), 3),
                              to: new Date(),
                            })
                          }
                        >
                          Last 3 months
                        </Button>
                      </div>
                    </div>
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange.from}
                      selected={{
                        from: dateRange.from,
                        to: dateRange.to,
                      }}
                      onSelect={(range) => {
                        if (range) {
                          setDateRange({
                            from: range.from,
                            to: range.to,
                          });
                        } else {
                          setDateRange({ from: undefined, to: undefined });
                        }
                      }}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
                <Button
                  onClick={() => {
                    setDateRange({ from: undefined, to: undefined });
                  }}
                  variant="outline"
                  size="sm"
                  className="p-1"
                >
                  <XIcon size={2} className="h-3 w-3" />
                </Button>
              </div>
            </div>

            {/* Active Ads Tab */}
            <TabsContent value="active" className="mt-4">
              <>
                {/* Current Stage Header */}
                <div className="mb-4 flex">
                  <h3 className="flex text-lg ">
                    {"Current Learning Goal: "}

                    {currentAbTest ? (
                      currentAbTest.type === "valueProp" ? (
                        <Badge variant="outline" className="ml-2 rounded-full">
                          <PaddingIcon className="mr-2 text-indigo-500"></PaddingIcon>
                          {" Value Prop"}
                        </Badge>
                      ) : currentAbTest.type === "audience" ? (
                        <Badge variant="outline" className="ml-2 rounded-full">
                          <FaceIcon className="mr-2 text-indigo-500" />{" "}
                          {" Audience"}
                        </Badge>
                      ) : currentAbTest.type === "creative" ? (
                        <Badge variant="outline" className="ml-2 rounded-full">
                          <ImageIcon
                            size={18}
                            className="mr-2 text-indigo-500"
                          />
                          {" Creative"}
                        </Badge>
                      ) : currentAbTest.type === "conversationSubject" ? (
                        <Badge variant="outline" className="ml-2 rounded-full">
                          <MessageCircleIcon
                            size={18}
                            className="mr-2 text-indigo-500"
                          />
                          {" Conversation Subject"}
                        </Badge>
                      ) : currentAbTest.type === "socialPostBodyCopy" ? (
                        <Badge variant="outline" className="ml-2 rounded-full">
                          <MessageCircleIcon
                            size={18}
                            className="mr-2 text-indigo-500"
                          />
                          {" Social Post Body Copy"}
                        </Badge>
                      ) : currentAbTest.type === "conversationCallToAction" ? (
                        <Badge variant="outline" className="ml-2 rounded-full">
                          {" Conversation Call To Action"}
                        </Badge>
                      ) : currentAbTest.type === "socialPostCallToAction" ? (
                        <Badge variant="outline" className="ml-2 rounded-full">
                          {" Social Post Call To Action"}
                        </Badge>
                      ) : (
                        <Skeleton className="ml-2 mt-1 h-5 w-24 rounded bg-gray-300" />
                      )
                    ) : null}
                  </h3>
                </div>

                {/* Render the current AB test */}
                <div className="mb-8 w-full overflow-x-auto">
                  <ActiveAdsTable
                    ads={(currentAbTest?.ads || []).map((ad) => {
                      // Use our utility function to map API response metrics
                      const adMetrics = ad.metrics || {};

                      // Ensure we're passing the metrics through our mapper
                      // to calculate derived metrics like CPC and CPM
                      const metrics = mapMetrics(adMetrics as any);
                      // Determine goal (Awareness vs LeadGen)
                      const adGoal =
                        adProgramObjectiveType === "LEAD_GENERATION"
                          ? "LEAD_GENERATION"
                          : "AWARENESS";

                      return {
                        sponsoredCreativeId: ad.id || "",
                        adVarients: ad.adVarients || {},
                        metrics,
                        title: ad.adVarients?.title,
                        description: ad.adVarients?.description,
                        imageUrl: ad.adVarients?.imageUrl,
                        adFormat: cleanedSegmentFormat,
                        goal: adGoal as "Awareness" | "LeadGen",
                      };
                    })}
                    stageType={currentAbTest?.type}
                    adFormat={cleanedSegmentFormat}
                    goal={
                      adProgramObjectiveType === "LEAD_GENERATION"
                        ? "LEAD_GENERATION"
                        : "AWARENESS"
                    }
                    tableDataIsLoading={isLoading}
                  />
                </div>

                <div className="mb-8 w-full overflow-x-auto">
                  {/* Upcoming Learning Goals Section */}
                  <UpcomingLearningGoalsTable
                    adSegmentId={params.adSegmentId}
                    adProgramId={adProgramId}
                    adFormat={cleanedSegmentFormat}
                  />
                </div>
              </>
            </TabsContent>

            {/* Past Ads Tab Content */}
            <TabsContent value="past" className="mt-4 w-full">
              <PastAdsSection
                abTestData={getPastRanAbTests.data || []}
                linkedInAdProgramId={adProgramId}
                adFormat={cleanedSegmentFormat}
                learningGoal={
                  adProgramObjectiveType === "LEAD_GENERATION"
                    ? "LEAD_GENERATION"
                    : "AWARENESS"
                }
                fromDate={dateRange.from}
                toDate={dateRange.to}
                dataIsLoading={getPastRanAbTests.isLoading}
              />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}
