import type { ColumnDef } from "@tanstack/react-table";
import React, { useEffect, useMemo, useState } from "react";
import { api } from "@/trpc/client";
import { FaceIcon, PaddingIcon } from "@radix-ui/react-icons";
import {
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ImageIcon, MessageCircleIcon, TrendingUp, Trophy } from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Skeleton } from "@kalos/ui/skeleton";
import { DataTable } from "@kalos/ui/table/datatable";

import { AbTestType, AdFormatType, Metrics, StageType } from "../_types";

type AdVariant = {
  sponsoredCreativeId: string;
  adVarients: Record<string, string>; // e.g., {valueProp: "I like Pizza"}
  adFormat?: AdFormatType;
  goal?: "Awareness" | "LeadGen";
  metrics?: Metrics;
  title?: string;
  description?: string;
  imageUrl?: string;
  targetingParams?: string; // Field to store targeting parameters if available
  campaign?: string; // Field for campaign URNs similar to upcoming goals
};

type AbTestData = {
  adVarients?: Record<string, string>;
  sponsoredCreativeId: string;
  sponsoredCreativeUrn: string;
  rounds?: any[];
  stageId: string;
  stageType: StageType;
  status: string;
  type: AbTestType;
  metrics?: Metrics;
} | null;
/**
 * Past Ads Section Component
 * Shows previously completed AB tests
 */

const renderAdCreativeTitle = (variant: Record<string, any>) => {
  const adVariantData = variant;
  return (
    <div className="flex items-center gap-2">
      {adVariantData?.adCreativeMetadata?.adCreative?.fileName}
    </div>
  );
};

const renderAdCreative = (variant: Record<string, any>) => {
  const adVariantData = variant;

  if (adVariantData.adCreativeMetadata?.adCreative?.fileType === "VIDEO") {
    if (adVariantData.adCreativeMetadata?.presignedUrl) {
      return (
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="ghost" className="h-16 w-16 p-0">
                <video>
                  <source
                    width="64"
                    height="64"
                    src={adVariantData?.adCreativeMetadata?.presignedUrl}
                  ></source>
                </video>
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogTitle>Creative Preview</DialogTitle>
              <video controls>
                <source
                  src={adVariantData?.adCreativeMetadata?.presignedUrl}
                  className="p-2"
                />
              </video>
            </DialogContent>
          </Dialog>
        </div>
      );
    }
  }

  if (adVariantData.adCreativeMetadata?.presignedUrl) {
    return (
      <div className="flex items-center gap-2">
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" className="h-16 w-16 p-0">
              <img
                width="64"
                height="64"
                src={adVariantData?.adCreativeMetadata?.presignedUrl}
              ></img>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>Creative Preview</DialogTitle>
            <img
              src={adVariantData?.adCreativeMetadata?.presignedUrl}
              className="p-2"
            />
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return <></>;
};

const renderConversationSubject = (
  variant: Record<string, any>,
  rowIndex: number,
) => {
  return (
    <div className="flex items-center gap-2">
      {variant?.messageCopyContent ? variant.messageCopyContent : ""}
    </div>
  );
};

const renderSocialPostBodyCopy = (
  variant: Record<string, any>,
  rowIndex: number,
) => {
  const adVariantData = variant;
  return (
    <div className="flex items-center gap-2">
      {adVariantData?.socialPostBodyCopy ?? ""}
    </div>
  );
};

const renderSocialPostCallToAction = (
  variant: Record<string, any>,
  rowIndex: number,
) => {
  const adVariantData = variant;
  return (
    <div className="flex items-center gap-2">
      {adVariantData?.callToAction ?? ""}
    </div>
  );
};

const renderConversationCallToAction = (
  variant: Record<string, any>,
  rowIndex: number,
) => {
  const adVariantData = variant;
  return (
    <div className="flex items-center gap-2">
      {adVariantData?.callToAction ?? ""}
    </div>
  );
};

const renderValuePropTitle = (
  variant: Record<string, any>,
  rowIndex: number,
) => {
  const adVarients = variant;
  return (
    <div className="flex flex-row items-center gap-2">
      {adVarients?.valueProp ?? ""}
    </div>
  );
};

const parseCampaignUrns = (campaign?: string): string[] => {
  if (!campaign) return [];

  // Split the comma-separated URNs
  return campaign
    .split(",")
    .map((urn) => {
      // Extract the targeting parameter from the URN
      // Format could be like: urn:li:adTargetingFacet:jobFunctions
      const match = urn.trim().match(/urn:li:adTargetingFacet:(\w+)/);
      if (!match || !match[1]) {
        return null;
      }

      // Convert to human-readable format (camelCase to Title Case with spaces)
      const param = match[1];

      // Format different known parameters
      switch (param) {
        case "jobFunctions":
          return "Job Function";
        case "industries":
          return "Industry";
        case "revenue":
          return "Annual Revenue";
        case "companies":
          return "Companies";
        case "skills":
          return "Skills";
        case "locations":
          return "Locations";
        default:
          // For unknown parameters, use generic formatting
          return param
            .replace(/([A-Z])/g, " $1") // Add space before capital letters
            .replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
            .trim(); // Remove any extra spaces
      }
    })
    .filter(Boolean) as string[]; // Remove any null values
};
const renderAudienceTargetingBadges = (
  variant: Record<string, any>,
  rowIndex: number,
) => {
  const campaignData = variant.campaign;

  // If we found campaign data, parse it for URNs
  if (campaignData) {
    const params = parseCampaignUrns(campaignData);

    if (params.length > 0) {
      return (
        <div className="flex flex-col  gap-2 whitespace-nowrap">
          {/* <div className="">{"Audience " + (rowIndex + 1)}</div> */}
          <div className="">
            {params.map((param, index) => (
              <React.Fragment key={`${param}-${index}`}>
                {/* {index > 0 && <span className="text-xs"></span>} */}
                {/* <Badge
                variant="outline"
                className="whitespace-nowrap border-gray-500 bg-gray-50 text-gray-500"
              > */}
                {index + 1 === params.length ? param : param + ", "}

                {/* </Badge> */}
              </React.Fragment>
            ))}
          </div>
        </div>
      );
    }
  }

  return null;
};

const renderAdTitle = (
  variant: Record<string, any>,
  abTestType: AbTestType | undefined,
  rowIndex: number,
) => {
  if (!abTestType) {
    return;
  }

  if (abTestType === "audience") {
    return renderAudienceTargetingBadges(variant, rowIndex);
  }

  if (abTestType === "valueProp") {
    return renderValuePropTitle(variant, rowIndex);
  }

  if (abTestType === "creative") {
    return renderAdCreativeTitle(variant);
  }

  if (abTestType === "conversationSubject") {
    return renderConversationSubject(variant, rowIndex);
  }

  if (abTestType === "socialPostBodyCopy") {
    return renderSocialPostBodyCopy(variant, rowIndex);
  }

  if (abTestType === "socialPostCallToAction") {
    return renderSocialPostCallToAction(variant, rowIndex);
  }

  if (abTestType === "conversationCallToAction") {
    return renderConversationCallToAction(variant, rowIndex);
  }
};
export default function PastAdsSection({
  abTestData,
  linkedInAdProgramId = "",
  adFormat,
  learningGoal,
  fromDate,
  toDate,
  dataIsLoading,
}: {
  abTestData: AbTestData[];
  linkedInAdProgramId: string;
  adFormat: string;
  learningGoal: string;
  fromDate?: Date;
  toDate?: Date;
  dataIsLoading: boolean;
}) {
  const [mergedAbTests, setMergedAbTests] = useState(abTestData || []);
  const allColumns: ColumnDef<AbTestData>[] = [
    // Ad Title column with targeting badges (position 0)
    {
      accessorKey: "adType",
      header: "Learning Goal",
      id: "learningGoal",
      minSize: 250,
      maxSize: 350,
      cell: ({ row }) => {
        // Get the ad variant data
        const rowData = row?.original;

        if (!rowData) {
          return "";
        }

        const abTestType = rowData.type;

        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap pr-2">
            {abTestType === "valueProp" ? (
              <Badge variant="outline" className="ml-2 rounded-full">
                {/* <PaddingIcon className="mr-2 text-indigo-500"></PaddingIcon> */}
                {" Value Prop"}
              </Badge>
            ) : abTestType === "audience" ? (
              <Badge variant="outline" className="ml-2 rounded-full">
                {/* <FaceIcon className="mr-2 text-indigo-500" />  */}
                {" Audience"}
              </Badge>
            ) : abTestType === "creative" ? (
              <Badge variant="outline" className="ml-2 rounded-full">
                {/* <ImageIcon size={18} className="mr-2 text-indigo-500" /> */}
                {" Creative"}
              </Badge>
            ) : abTestType === "conversationSubject" ? (
              <Badge variant="outline" className="ml-2 rounded-full">
                {/* <MessageCircleIcon size={18} className="mr-2 text-indigo-500" /> */}
                {" Conversation Subject"}
              </Badge>
            ) : (
              " Copy"
            )}{" "}
          </div>
        );
      },
    },
    {
      accessorKey: "adTargeting",
      header: "Ad Title",
      id: "adTitle",
      minSize: 250,
      maxSize: 350,
      cell: ({ row }) => {
        // Get the ad variant data
        const rowData = row?.original;

        if (!rowData) {
          return "";
        }
        const variant = rowData.adVarients;
        if (!variant) {
          return "";
        }
        const rowIndex = row.index;
        const abTestType = rowData.type;

        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap pr-2">
            {renderAdTitle(variant, abTestType, rowIndex)}
          </div>
        );
      },
    },
    {
      accessorKey: "adCreativePreview",
      header: "",
      id: "adCreativePreview",
      minSize: 250,
      maxSize: 350,
      cell: ({ row }) => {
        // Get the ad variant data
        const variant = row?.original?.adVarients;
        const rowIndex = row?.index;
        if (!variant) {
          return "";
        }
        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap pr-2">
            {renderAdCreative(variant)}
          </div>
        );
      },
    },
    {
      accessorFn: (row) => row?.metrics?.costInUsd || 0,
      id: "costInUsd",
      header: "Spend",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? `$${value.toFixed(2)}` : 0;
      },
    },
    // Impressions - for SingleImage format
    {
      accessorFn: (row) => row?.metrics?.impressions || 0,
      id: "impressions",
      header: "Impressions",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value.toLocaleString() : 0;
      },
    },
    // CPM - for SingleImage format
    {
      accessorFn: (row) => row?.metrics?.cpm || 0,
      id: "cpm",
      header: "CPM",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? `$${value.toFixed(2)}` : 0;
      },
    },
    // Clicks - for SingleImage format
    {
      accessorFn: (row) => row?.metrics?.clicks || 0,
      id: "clicks",
      header: "Clicks",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // CTR - for SingleImage format
    {
      accessorFn: (row) => row?.metrics?.ctr || 0,
      id: "ctr",
      header: "CTR",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? `${value.toFixed(2)}%` : 0;
      },
    },
    // CPC - for SingleImage format
    {
      accessorFn: (row) => row?.metrics?.cpc || 0,
      id: "cpc",
      header: "CPC",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? `$${value.toFixed(2)}` : 0;
      },
    },
    {
      // accessorFn: (row) => row?.metrics?.clickToOpenRate || 0,
      id: "videosFirstQuartile",
      header: "Videos @ 25%",
      cell: ({ row }) => {
        const videoData =
          row?.original?.metrics?.videoFirstQuartileCompletions || 0;
        return videoData;
      },
    },
    {
      // accessorFn: (row) => row?.metrics?.clickToOpenRate || 0,
      id: "videosMidpoint",
      header: "Videos @ 50%",
      cell: ({ row }) => {
        const videoData = row?.original?.metrics?.videoMidpointCompletions || 0;
        return videoData;
      },
    },
    {
      // accessorFn: (row) => row?.metrics?.clickToOpenRate || 0,
      id: "videosThirdQuartile",
      header: "Videos @ 75%",
      cell: ({ row }) => {
        const videoData =
          row?.original?.metrics?.videoThirdQuartileCompletions || 0;
        return videoData;
      },
    },
    {
      // accessorFn: (row) => row.metrics?.clickToOpenRate || 0,
      id: "videoCompletionRate",
      header: "Completion Rate",
      cell: ({ row }) => {
        const impressions = row?.original?.metrics?.impressions || 0;
        const videoCompletions = row?.original?.metrics?.videoCompletions || 0;

        if (impressions === 0 || videoCompletions === 0) {
          return 0;
        }
        return `${((videoCompletions / impressions) * 100).toFixed(2)}%`;
      },
    },
    // Total Social Actions - for SingleImage format
    {
      accessorFn: (row) => row?.metrics?.totalEngagements || 0,
      id: "totalEngagements",
      header: "Total Engagements",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Clicks to Landing Page - for SingleImage format
    {
      // accessorFn: (row) => row?.metrics?.landingPageClicks || 0,
      id: "clicksToLandingPage",
      header: "Clicks to Landing Page",
      cell: ({ row }) => {
        const value = row?.original?.metrics?.landingPageClicks || 0;
        return value > 0 ? value : 0;
      },
    },

    // Sends - for Conversation format
    {
      accessorFn: (row) => row?.metrics?.sends || 0,
      id: "sends",
      header: "Sends",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Opens - for Conversation format
    {
      accessorFn: (row) => row?.metrics?.opens || 0,
      id: "opens",
      header: "Opens",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Open Rate - for Conversation format
    {
      // accessorFn: (row) => row.metrics?.openRate || 0,
      id: "openRate",
      header: "Open Rate",
      cell: ({ row }) => {
        const sends = row.original?.metrics?.sends || 0;
        const opens = row.original?.metrics?.opens || 0;

        if (sends == 0 || opens == 0) {
          return 0;
        }

        return `${((opens / sends) * 100).toFixed(2)}%`;
      },
    },
    // Button Clicks - for Conversation format
    {
      accessorFn: (row) => row?.metrics?.actionClicks || 0,
      id: "buttonClicks",
      header: "Button Clicks",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Click to Open Rate - for Conversation format
    {
      // accessorFn: (row) => row?.metrics?.clickToOpenRate || 0,
      id: "clickToOpenRate",
      header: "Click to Open Rate",
      cell: ({ row }) => {
        const opens = Number(row?.original?.metrics?.opens || 0);
        const clicks = Number(row?.original?.metrics?.actionClicks || 0);

        if (clicks === 0 || opens === 0) {
          return 0;
        }

        return `${((clicks / opens) * 100).toFixed(2)}%`;
      },
    },
    // Lead Form Opens - for both formats with LeadGen goal
    {
      accessorFn: (row) => row?.metrics?.oneClickLeadFormOpens || 0,
      id: "leadFormOpens",
      header: "Lead Form Opens",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Lead Form Completion Rate - for both formats with LeadGen goal
    {
      // accessorFn: (row) => row?.metrics?.leadFormCompletionRate || 0,
      id: "leadFormCompletionRate",
      header: "Lead Form Completion Rate",
      cell: ({ row }) => {
        const opens = row?.original?.metrics?.oneClickLeadFormOpens || 0;
        const leads = row?.original?.metrics?.oneClickLeads || 0;
        if (opens == 0 || leads == 0) {
          return 0;
        }
        return `${((leads / opens) * 100).toFixed(2)}%`;
      },
    },
    // Leads - for both formats with LeadGen goal
    {
      accessorFn: (row) => row?.metrics?.oneClickLeads || 0,
      id: "leads",
      header: "Leads",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
  ];

  // Filter columns based on ad format and goal
  // No filters yet - return all
  const filteredColumns = allColumns.filter((column) => {
    // Always include the title column
    if (column.id === "title") return true;

    // Scenario 1: SingleImage + Awareness
    // if (effectiveAdFormat === "SingleImage" && effectiveGoal === "Awareness") {
    if (adFormat === "SINGLE_IMAGE" && learningGoal === "AWARENESS") {
      return [
        "learningGoal",
        "adTitle",
        "adCreativePreview",
        "costInUsd",
        "impressions",
        "cpm",
        "clicks",
        "ctr",
        "cpc",
        "totalEngagements",
        "clicksToLandingPage",
      ].includes(column.id as string);
    }

    // Scenario 2: SingleImage + LeadGen
    if (adFormat === "SINGLE_IMAGE" && learningGoal === "LEAD_GENERATION") {
      return [
        "learningGoal",
        "adTitle",
        "adCreativePreview",
        "costInUsd",
        "impressions",
        "cpm",
        "clicks",
        "ctr",
        "cpc",
        "totalEngagements",
        "clicksToLandingPage",
        "leadFormOpens",
        "leadFormCompletionRate",
        "leads",
      ].includes(column.id as string);
    }

    // Scenario 3: Conversation + LeadGen
    if (
      (adFormat === "SPONSORED_CONVERSATION" &&
        learningGoal === "LEAD_GENERATION") ||
      (adFormat === "SPONSORED_INMAIL" && learningGoal === "LEAD_GENERATION")
    ) {
      return [
        "learningGoal",
        "adTitle",
        "adCreativePreview",
        "costInUsd",
        "sends",
        "opens",
        "openRate",
        "buttonClicks",
        "clickToOpenRate",
        "leadFormOpens",
        "leadFormCompletionRate",
        "leads",
      ].includes(column.id as string);
    }

    if (adFormat === "VIDEO" && learningGoal === "AWARENESS") {
      return [
        "learningGoal",
        "adTitle",
        "adCreativePreview",
        "costInUsd",
        "impressions",
        "cpm",
        "videosFirstQuartile", // TODO
        "videosMidpoint", // TODO
        "videosThirdQuartile", // TODO
        "videoCompletionRate",
        "totalEngagements",
        "clicksToLandingPage",
      ].includes(column.id as string);
    }

    if (adFormat === "DOCUMENT" && learningGoal === "LEAD_GENERATION") {
      return [
        "learningGoal",
        "adTitle",
        "costInUsd",
        "impressions",
        "cpm",
        "clicks",
        "ctr",
        "cpc",
        "totalEngagements",
        "clicksToLandingPage",
        "leadFormOpens",
        "leadFormCompletionRate",
        "leads",
      ].includes(column.id as string);
    }

    // Default to showing the column
    return [
      "learningGoal",
      "adTitle",
      "adCreativePreview",
      "costInUsd",
      "impressions",
      "cpm",
      "clicks",
      "ctr",
      "cpc",
      "totalEngagements",
      "clicksToLandingPage",
      "leadFormOpens",
      "leadFormCompletionRate",
      "leads",
      "videoFirstQuartile", // TODO
      "videoMidpoint", // TODO
      "videoThirdQuartile", // TODO
      "videoCompletionRate",
      "totalEngagements",
    ];
  });
  const sponsoredCreativeUrns = useMemo(() => {
    return abTestData?.map((abTest) => {
      if (!abTest) {
        return;
      }
      return abTest.sponsoredCreativeUrn;
    });
  }, [abTestData]);

  const sponsoredCreativeMetrics = api.v2.ads.abTest.getAdMetrics.useQuery(
    {
      linkedInAdProgramId: linkedInAdProgramId,
      sponsoredCreativeUrns: sponsoredCreativeUrns || [],
      fromDate: fromDate,
      toDate: toDate,
    },
    {
      enabled: true,
    },
  );

  useEffect(() => {
    console.log("Past data", abTestData);
    console.log("adProgramId", linkedInAdProgramId);
    console.log("creativemetrics", sponsoredCreativeMetrics.data);
    console.log("Ad format", adFormat);
    console.log("learning goal", learningGoal);
  }, []);

  useEffect(() => {
    const metricData = sponsoredCreativeMetrics.data;

    if (!metricData || Array.isArray(metricData) || !abTestData) return;

    const updated = abTestData.map((abTest) => {
      if (!abTest) {
        return;
      }
      const metrics = metricData[abTest.sponsoredCreativeUrn];
      return {
        ...abTest,
        metrics,
      };
    });

    setMergedAbTests(updated);
  }, [sponsoredCreativeMetrics.data, abTestData]);

  const skeletonColumns = useMemo(() => {
    return filteredColumns.map((column) => ({
      ...column,
      cell: () => <Skeleton className="h-4 w-24 rounded bg-gray-200" />,
    }));
  }, [filteredColumns]);

  const loadingTableData = useMemo(() => {
    return Array(3).fill({
      sponsoredCreativeId: "",
      adVarients: {},
      metrics: {},
      stageId: "",
      stageType: "valuePropTest", // default
      status: "FINISHED",
      type: "valueProp", // default
      sponsoredCreativeUrn: "",
    });
  }, []);

  const table = useReactTable({
    data: dataIsLoading ? loadingTableData : mergedAbTests,
    columns: dataIsLoading ? skeletonColumns : filteredColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    columnResizeMode: "onChange",
    enableColumnResizing: true,
  });

  return (
    <div className="w-full overflow-x-auto">
      <div className="min-w-[600px]">
        <DataTable
          table={table}
          columns={filteredColumns}
          noResultsMessage="No Past Ads in this Campaign Group"
          noHover={false}
        />
      </div>
    </div>
  );
}
