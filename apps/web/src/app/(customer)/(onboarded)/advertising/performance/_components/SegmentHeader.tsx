import { useEffect, useState } from "react";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { Audience } from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/_components/Audience";
import { api } from "@/trpc/client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import {
  ArrowLeft,
  ChevronDown,
  Edit,
  Info,
  LoaderCircleIcon,
} from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import { Checkbox } from "@kalos/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Skeleton } from "@kalos/ui/skeleton";

import { SegmentDetail } from "../_types";

interface SegmentHeaderProps {
  segmentDetail: SegmentDetail;
  router: AppRouterInstance;
}

interface EditScopeOption {
  id: string;
  label: string;
}

const EDIT_SCOPE_OPTIONS: EditScopeOption[] = [
  { id: "current", label: "Current Campaign" },
  { id: "future", label: "Future Campaigns" },
];

function EditAudienceModal({
  segmentDetail,
  isOpen,
  setIsOpen,
}: {
  segmentDetail: SegmentDetail;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}) {
  const [selectedScopes, setSelectedScopes] = useState<string[]>([]);

  const handleScopeChange = (scopeId: string) => {
    setSelectedScopes((prev) => {
      if (prev.includes(scopeId)) {
        return prev.filter((id) => id !== scopeId);
      }
      return [...prev, scopeId];
    });
  };

  return (
    <div
      role="button"
      onClick={() => setIsOpen(true)}
      className="flex w-full cursor-pointer items-center"
    >
      <Edit className="mr-2 h-4 w-4" />
      Audience
    </div>
  );
}

/**
 * Segment Header Component
 * Displays segment title, status, and metrics
 */
export default function SegmentHeader({
  segmentDetail,
  router,
}: SegmentHeaderProps) {
  const [isEditAudienceOpen, setIsEditAudienceOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleAudienceClick = () => {
    setIsEditAudienceOpen(true);
    setIsDropdownOpen(false);
  };

  const handleDialogClose = (open: boolean) => {
    setIsEditAudienceOpen(open);
    setIsDropdownOpen(false);
  };

  const apiUtils = api.useUtils();

  // Prefetch all audiences for the segment
  const audiencesQuery = api.v2.ads.adAudience.getAllForAdSegment.useQuery({
    adSegmentId: segmentDetail.adSegmentId,
  });

  const getCampaignGroupMonthAnalytics =
    api.v2.ads.linkedInCampaignGroup.getAnalyticsForOneCampaignGroup.useQuery(
      {
        linkedInAdSegment: segmentDetail.adSegmentId,
        fromDate: new Date(
          new Date().getFullYear(),
          new Date().getMonth(),
          1,
          0,
          0,
          0,
          0,
        ),
        toDate: new Date(
          new Date().getFullYear(),
          new Date().getMonth() + 1,
          1,
          0,
          0,
          0,
          0,
        ),
      },
      {
        enabled: !!segmentDetail.adSegmentId && !segmentDetail.endDate,
      },
    );

  // Prefetch facets that will be needed for the dialog
  const facetsQuery =
    api.v2.ads.linkedInApi.adTargeting.getAdTargetingFacets.useQuery();

  // If we have audiences, prefetch their details and counts
  useEffect(() => {
    if (audiencesQuery.data) {
      audiencesQuery.data.forEach((audience) => {
        // Prefetch audience details
        apiUtils.v2.ads.adAudience.getOne.prefetch({ id: audience.id });
        // Prefetch audience counts
        apiUtils.v2.ads.linkedInApi.getAudienceCount.prefetch({
          targetingCriteria: audience.audienceTargetCriteria,
        });
      });
    }
  }, [audiencesQuery.data, apiUtils]);

  return (
    <>
      <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <div className="flex items-start gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold tracking-tight">
                  {segmentDetail.name}
                </h1>
                <Badge variant="outline" className="bg-blue-50 text-blue-700">
                  {segmentDetail.status}
                </Badge>
              </div>

              <p className="text-sm text-muted-foreground">
                {new Date(segmentDetail.startDate).toLocaleDateString()} to{" "}
                {segmentDetail.endDate
                  ? new Date(segmentDetail.endDate).toLocaleDateString()
                  : "Evergreen"}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="space-y-0.5 text-right">
            <div>
              <span className="text-sm text-muted-foreground">Total:</span>{" "}
              <span className="text-base font-semibold">
                $
                {(segmentDetail.metrics.totalSpent || 0).toLocaleString(
                  undefined,
                  {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  },
                )}
              </span>
            </div>
            {!segmentDetail.endDate && (
              <div>
                <span className="text-sm text-muted-foreground">
                  This Month:
                </span>
                {" $"}
                <span className="text-base font-semibold">
                  {getCampaignGroupMonthAnalytics.isLoading ? (
                    <LoaderCircleIcon className="mb-1 inline-block h-4 w-4 animate-spin align-middle" />
                  ) : getCampaignGroupMonthAnalytics.isError ? (
                    <span className="text-red-500">Error</span>
                  ) : (
                    (
                      getCampaignGroupMonthAnalytics.data?.costInUsd || 0
                    ).toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })
                  )}
                </span>
              </div>
            )}

            <div>
              <span className="text-sm text-muted-foreground">Budget:</span>{" "}
              <span className="text-base font-semibold">
                ${segmentDetail.budget.toLocaleString()}
              </span>
            </div>
          </div>

          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button className="flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Edit
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[200px] space-y-1 rounded-md border bg-white p-2 shadow-md"
            >
              <DropdownMenuItem className="flex cursor-pointer items-center rounded-sm px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none">
                <div
                  role="button"
                  onClick={handleAudienceClick}
                  className="flex w-full cursor-pointer items-center"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Audience
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Dialog open={isEditAudienceOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="min-w-[900px]">
          <DialogHeader>
            <DialogTitle>Edit Audience</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <p className="flex items-center gap-2">
                <Info></Info> Changes will apply to this campaign and will be
                default settings for future campaigns
              </p>
            </div>
            {isEditAudienceOpen && (
              <div className="max-h-[600px] overflow-y-auto">
                <Audience
                  adSegmentId={segmentDetail.adSegmentId}
                  adAccount={segmentDetail.adProgramId.split("/")[1] || ""}
                  adProgramId={segmentDetail.adProgramId}
                  prefetchedAudiences={audiencesQuery.data}
                  prefetchedFacets={facetsQuery.data}
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
