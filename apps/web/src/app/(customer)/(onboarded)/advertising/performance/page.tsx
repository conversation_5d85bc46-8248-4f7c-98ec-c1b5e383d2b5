"use client";

import { useEffect, useState } from "react";
import { FeatureFlag } from "@/app/utils/feature-flag";
import { api } from "@/trpc/client";
import { format, subDays, subMonths } from "date-fns";
import { CalendarIcon, XIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Calendar } from "@kalos/ui/calender";
import { cn } from "@kalos/ui/index";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";

import { CampaignPerformanceTable } from "./_components/CampaignPerformanceTable";
import MetricsCards, {
  MetricsCardsArray,
  MetricsCardsProps,
} from "./_components/MetricsCards";

export default function PerformancePage() {
  const [selectedPeriod, setSelectedPeriod] = useState("6");
  const [currentTab, setCurrentTab] = useState("running");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  const [selectedAdAccount, setSelectedAdAccount] = useState<string | null>(
    null,
  );
  const [selected, setSelected] = useState<"Running" | "Completed">("Running");

  const [stats, setStats] = useState<MetricsCardsProps[]>([]);

  // Fetch all ad accounts
  const adAccountsQuery = api.v2.ads.adAccounts.getForOrganization.useQuery();

  // Set the first ad account as selected when data loads
  useEffect(() => {
    if (
      adAccountsQuery.data?.length &&
      !selectedAdAccount &&
      adAccountsQuery.data[0]?.id !== selectedAdAccount
    ) {
      setSelectedAdAccount(adAccountsQuery.data[0]?.id ?? null);
    }
  }, [adAccountsQuery.data, selectedAdAccount]);

  const activeCampaignsQuery =
    api.v2.ads.linkedInCampaignGroup.getFilteredCampaigns.useQuery(
      {
        adAccountId: selectedAdAccount || "",
        statuses:
          selected === "Running"
            ? ["ACTIVE"]
            : [
                "PAUSED",
                "ARCHIVED",
                "CANCELLED",
                "PENDING_DELETION",
                "REMOVED",
              ],
      },
      {
        enabled: !!selectedAdAccount,
      },
    );

  // Determine loading state based on the current tab and query states
  const isActiveTabLoading =
    activeCampaignsQuery.isLoading || !activeCampaignsQuery.data;

  function toUTCDate(localDate: Date, addDay: boolean = false): Date {
    return new Date(
      Date.UTC(
        localDate.getFullYear(),
        localDate.getMonth(),
        addDay ? localDate.getDate() + 1 : localDate.getDate(),
        0,
        0,
        0,
        0,
      ),
    );
  }
  // TODO don't hardcode
  const leadsAndPipelinesQuery =
    api.v2.ads.linkedInLeadFormLead.getLeadsAndPipelinesStatsByAccountId.useQuery(
      {
        linkedInAdAccountId: selectedAdAccount || "",
        startDateFilter: dateRange.from ? toUTCDate(dateRange.from) : undefined,
        endDateFilter: dateRange.to ? toUTCDate(dateRange.to, true) : undefined,
      },
    );

  useEffect(() => {
    if (leadsAndPipelinesQuery.data) {
      const pipelineData = leadsAndPipelinesQuery.data;
      const stats: MetricsCardsArray = [
        {
          value: pipelineData.totalLeads,
          label: "LinkedIn Leads",
          change: 0,
          trend: "neutral",
          currency: false,
        },
        // {
        //   value: pipelineData.totalPipelineValue,
        //   label: "Pipeline Created",
        //   change: 0,
        //   trend: "neutral",
        //   currency: true,
        // },
        // {
        //   value: pipelineData.totalPipelineInfluence,
        //   label: "Pipeline Influenced",
        //   change: 0,
        //   trend: "neutral",
        //   currency: true,
        // },
      ];

      setStats(stats);
    }
  }, [leadsAndPipelinesQuery.data]);

  return (
    <div className="flex w-full flex-col space-y-6 p-6">
      <FeatureFlag flag="leads-card-performance-screen">
        <h2 className="mb-2 text-lg font-medium">Impact</h2>
        <MetricsCards stats={stats} />
      </FeatureFlag>

      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Campaign Groups</h1>
      </div>

      <div className="flex w-full justify-between">
        <div className="inline-flex items-center overflow-hidden rounded-md border border-gray-300">
          <Button
            variant="ghost"
            onClick={() => setSelected("Running")}
            className={`rounded-none px-4 py-2 text-sm font-medium ${
              selected === "Running"
                ? "border  bg-white text-blue-600 hover:text-blue-600"
                : "border-transparent bg-gray-100 text-black hover:text-black"
            }`}
          >
            Active
          </Button>
          <Button
            variant="ghost"
            onClick={() => setSelected("Completed")}
            className={`rounded-none px-4 py-2 text-sm font-medium ${
              selected === "Completed"
                ? "border  bg-white text-blue-600 hover:text-blue-600"
                : "border-transparent bg-gray-100 text-black hover:text-black"
            }`}
          >
            Not Active
          </Button>
        </div>
        <div className="flex items-center justify-self-end">
          {" "}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date"
                variant={"outline"}
                className={cn(
                  "w-[260px] justify-start text-left font-normal",
                  !dateRange.from && !dateRange.to && "text-muted-foreground",
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  "Select Date Range"
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <div className="justify-items-center border-b border-border p-2">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-primary text-white "
                    onClick={() =>
                      setDateRange({
                        from: subDays(new Date(), 7),
                        to: new Date(),
                      })
                    }
                  >
                    Last 1 Week
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-primary text-white "
                    onClick={() =>
                      setDateRange({
                        from: subMonths(new Date(), 1),
                        to: new Date(),
                      })
                    }
                  >
                    Last 1 Month
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-primary text-white "
                    onClick={() =>
                      setDateRange({
                        from: subMonths(new Date(), 3),
                        to: new Date(),
                      })
                    }
                  >
                    Last 3 Months
                  </Button>
                </div>
              </div>
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={{
                  from: dateRange.from,
                  to: dateRange.to,
                }}
                onSelect={(range) => {
                  if (range) {
                    setDateRange({
                      from: range.from,
                      to: range.to,
                    });
                  } else {
                    setDateRange({ from: undefined, to: undefined });
                  }
                }}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
          <Button
            onClick={() => {
              setDateRange({ from: undefined, to: undefined });
            }}
            variant="outline"
            size="sm"
            className="p-1"
          >
            <XIcon size={2} className="h-3 w-3" />
          </Button>
        </div>
      </div>

      <div className="flex flex-col space-y-4">
        <CampaignPerformanceTable
          isLoading={isActiveTabLoading}
          adPrograms={activeCampaignsQuery.data || []}
          adAccountId={selectedAdAccount || ""}
          periodMonths={parseInt(selectedPeriod)}
          status="ACTIVE"
          fromDate={dateRange.from}
          toDate={dateRange.to}
        />
      </div>
    </div>
  );
}
