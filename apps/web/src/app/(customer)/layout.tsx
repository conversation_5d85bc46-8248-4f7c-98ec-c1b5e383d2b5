import type { Metada<PERSON> } from "next";
import dynamic from "next/dynamic";
import { Inter } from "next/font/google";
import { OrganizationContextProvider } from "@/features/organization/contexts/organization-context";
import { PHProvider } from "@/posthog/posthog";
import { TRPCReactProvider } from "@/trpc/provider";
import { api } from "@/trpc/server";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Kalos",
  description: "",
};

const PostHogPageView = dynamic(
  () => import("../../posthog/posthog-page-view"),
  {
    ssr: false,
  },
);

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const organizationQuery = api.user.organizationUser.get.organization();
  return (
    <PHProvider>
      <html lang="en">
        <body className={inter.className}>
          <TRPCReactProvider>
            <PostHogPageView />
            <OrganizationContextProvider organizationData={organizationQuery}>
              {children}
            </OrganizationContextProvider>
          </TRPCReactProvider>
        </body>
      </html>
    </PHProvider>
  );
}
