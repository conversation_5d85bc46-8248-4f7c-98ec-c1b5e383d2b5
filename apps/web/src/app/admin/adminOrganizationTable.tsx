"use client";

import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { api } from "@/trpc/client";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Loader2 } from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { cn } from "@kalos/ui/index";
import { DataTable } from "@kalos/ui/table/datatable";

export function AdminOrganizationTable() {
  const organizationQuery = api.v2.core.admin.getAllOrganizations.useQuery();

  const columns: ColumnDef<{
    organizationId: number;
    name: string;
  }>[] = [
    {
      header: "Organization Name",
      cell: ({ row }) => {
        return (
          <Link href={`/admin/organization/${row.original.organizationId}`}>
            {row.original.name}
          </Link>
        );
      },
    },
    {
      header: "Is Onboarded",
      cell: ({ row }) => {
        const isOnboarded = api.opportunity.isOnboarded.useQuery({
          organizationId: row.original.organizationId,
        });
        console.log(
          `isOnboarded: ${isOnboarded.data} for ${row.original.name}`,
        );
        return (
          <>
            {!isOnboarded.isLoading && !isOnboarded.isError && (
              <Badge
                variant={"secondary"}
                className={cn(
                  isOnboarded.data == true ? "bg-green-200" : "bg-red-200",
                  "gap-2 border",
                )}
              >
                <div
                  className={cn(
                    isOnboarded.data == true ? "bg-green-500" : "bg-red-500",
                    "h-3 w-3 rounded-full border",
                  )}
                ></div>
                {isOnboarded.data == true
                  ? "Yes"
                  : isOnboarded.data == false
                    ? "No"
                    : "ERR"}
              </Badge>
            )}
          </>
        );
      },
    },
  ];

  const table = useReactTable({
    data: organizationQuery.data ?? [],
    columns,
    enableRowSelection: false,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="flex h-screen w-screen flex-col items-center justify-center">
      <Card className="h-fit w-full md:w-1/2">
        <CardHeader>
          <CardTitle className="text-xl">Organizations</CardTitle>
        </CardHeader>
        <CardContent className="px-0 py-0 pt-4">
          {organizationQuery.data && (
            <DataTable
              columns={columns}
              table={table}
              rowHeight="16"
              noHover={false}
            />
          )}
          {organizationQuery.isLoading && (
            <Loader2 className="h-10 w-10 animate-spin" />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
