import Link from "next/link";
import { OrganizationIsOnboardedBoundry } from "@/features/organization/components/organization-is-onboarded-boundry";
import { api } from "@/trpc/server";
import {
  BarChartIcon,
  FilePlusIcon,
  GlobeIcon,
  Pencil1Icon,
  PieChartIcon,
  RocketIcon,
} from "@radix-ui/react-icons";
import { UsersIcon } from "lucide-react";

import WinLossIcon from "@kalos/ui/icons/win-loss-icon";
import { NavBar } from "@kalos/ui/navbar";

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { organizationId: string };
}>) {
  const account = await api.user.organizationUser.get.organization();
  return (
    <div className="flex h-screen w-screen justify-start">
      <NavBar.Shell className="w-[240px] min-w-[240px] flex-shrink-0 bg-white">
        <NavBar.ClientInfoSection clientName="Middesk" />
        <NavBar.Items>
          <NavBar.ItemsGroup>
            <NavBar.ItemsGroupHeader>
              <WinLossIcon color="blue" />
              <NavBar.ItemsGroupHeaderTitle title="ICP Engine" />
            </NavBar.ItemsGroupHeader>
            <Link
              href={`/admin/organization/${params.organizationId}/segments`}
              className="flex w-full items-center justify-start"
            >
              <NavBar.Item>
                <BarChartIcon />
                <NavBar.ItemTitle title="Segments" />
              </NavBar.Item>
            </Link>
            <Link
              href={`/admin/organization/${params.organizationId}/conversion-tracking`}
              className="flex w-full items-center justify-start"
            >
              <NavBar.Item>
                <PieChartIcon />
                <NavBar.ItemTitle title="Conversion Tracking" />
              </NavBar.Item>
            </Link>
            <Link
              href={`/admin/organization/${params.organizationId}/users`}
              className="flex w-full items-center justify-start"
            >
              <NavBar.Item>
                <UsersIcon size={16} />
                <NavBar.ItemTitle title="Users" />
              </NavBar.Item>
            </Link>
          </NavBar.ItemsGroup>
        </NavBar.Items>
      </NavBar.Shell>
      {children}
    </div>
  );
}
