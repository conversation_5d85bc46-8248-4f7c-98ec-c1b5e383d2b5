"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { api } from "@/trpc/client";
import { zodResolver } from "@hookform/resolvers/zod";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Loader2, PlusIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Button } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@kalos/ui/form";
import { Input } from "@kalos/ui/input";
import { DataTable } from "@kalos/ui/table/datatable";

const formSchema = z.object({
  email: z.string().email(),
});

export default function AdminOrganizationUsersPage({
  params,
}: {
  params: { organizationId: string };
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const apiUtils = api.useUtils();

  const usersQuery = api.v2.core.admin.getUsersForOrganization.useQuery({
    organizationId: parseInt(params.organizationId),
  });

  const addUserMutation = api.v2.core.admin.addUser.useMutation({
    onSuccess: () => {
      apiUtils.v2.core.admin.getUsersForOrganization.invalidate({
        organizationId: parseInt(params.organizationId),
      });
      setIsModalOpen(false);
      form.reset();
    },
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    alert(values);
    addUserMutation.mutate({
      email: values.email,
      organizationId: parseInt(params.organizationId),
    });
  };

  const columns: ColumnDef<{
    userId: string;
    email: string;
  }>[] = [
    {
      header: "User",
      cell: ({ row }) => {
        return (
          <div className="flex flex-col items-start justify-start">
            <h1 className="text-sm font-medium">{row.original.email}</h1>
            <p className="text-xs text-muted-foreground">
              {row.original.userId}
            </p>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: usersQuery.data ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="flex h-full w-full flex-col justify-start">
      <div className="sticky inset-x-0 top-0 flex h-16 w-full items-center justify-between border-b bg-white px-6">
        <h1 className="text-xl font-medium">Users</h1>
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" /> Add User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New User</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setIsModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={addUserMutation.isPending}>
                    {addUserMutation.isPending ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    Add User
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      <div className="h-full w-full overflow-auto bg-background px-6 pt-8">
        {usersQuery.isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <DataTable columns={columns} table={table} noHover={false} />
        )}
      </div>
    </div>
  );
}
