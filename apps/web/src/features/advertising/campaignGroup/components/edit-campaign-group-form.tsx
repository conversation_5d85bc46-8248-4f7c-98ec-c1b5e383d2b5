"use client";

import type { z } from "zod";
import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { useAuth } from "@clerk/nextjs";
import { Label } from "@radix-ui/react-dropdown-menu";
import {
  CalendarIcon,
  InfoCircledIcon,
  ReloadIcon,
} from "@radix-ui/react-icons";
import { format } from "date-fns";
import {
  ArrowRight,
  CalendarClock,
  FileText,
  Image,
  MessageSquare,
  TreePine,
  TrendingUp,
  Video,
} from "lucide-react";

// UI Components
import { Button } from "@kalos/ui/button";
import { Calendar } from "@kalos/ui/calender";
import { Card } from "@kalos/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@kalos/ui/form";
import { cn } from "@kalos/ui/index";
import { Input } from "@kalos/ui/input";
import { MoneyInput } from "@kalos/ui/money-input";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { RadioGroup, RadioGroupItem } from "@kalos/ui/radiogroup";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@kalos/ui/tooltip";

import type { CampaignGroupFormSchema } from "../hooks/campaign-group-form";
import { useCampaignGroupContext } from "../../context";
import { useCampaignGroupForm } from "../hooks/campaign-group-form";
import LeadGenFormsDialog from "./leadGenFormsDialog";

const enabledUserIds = [
  "user_2p2YdEOYsl2oCmgvKrj8AWAf5Td",
  "user_2hpfJ7nV9VwkpT8o4pmV3vkLsd0",
  "user_2gvKecrxU1ugrxInkMeeqc4KJsp",
  "user_2yI7bjxJLeRjphdosN4lAfjhcGq",
];

export function EditCampaignGroupForm({
  adAccount: adAccountId,
  campaignGroupId,
}: {
  adAccount: string;
  campaignGroupId: string;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFormId, setSelectedFormId] = useState<string | null>(null);
  const router = useRouter();
  const apiUtil = api.useUtils();
  const [destinationUrl, setDestinationUrl] = useState<string | null>(null);

  const linkedInAdProgramQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: campaignGroupId,
  });

  const updateCampaignGroupMutation =
    api.v2.ads.linkedInAdProgram.updateOne.useMutation({
      onSuccess: async (data) => {
        await apiUtil.v2.ads.invalidate();
        router.push(
          `/advertising/adAccounts/${adAccountId}/campaignGroups/${data.id}/segments`,
        );
        setIsSubmitting(false);
      },
    });

  useEffect(() => {
    if (selectedFormId !== null) {
      setDestinationUrl(null);
    }
  }, [selectedFormId]);

  // Initialize destinationUrl from query data when it becomes available
  useEffect(() => {
    if (linkedInAdProgramQuery.data?.destinationUrl) {
      setDestinationUrl(linkedInAdProgramQuery.data.destinationUrl);
    }
  }, [linkedInAdProgramQuery.data]);

  function getAdFormat(values: CampaignGroupFormSchema):
    | {
        type: "SPONSORED_CONTENT";
        format: "SINGLE_IMAGE" | "VIDEO" | "DOCUMENT";
      }
    | { type: "SPONSORED_INMAIL"; format: "SPONSORED_INMAIL" } {
    if (values.adFormat == "SINGLE_IMAGE") {
      return { type: "SPONSORED_CONTENT", format: "SINGLE_IMAGE" };
    } else if (values.adFormat == "VIDEO") {
      return { type: "SPONSORED_CONTENT", format: "VIDEO" };
    } else if (values.adFormat == "SPONSORED_INMAIL") {
      return { type: "SPONSORED_INMAIL", format: "SPONSORED_INMAIL" };
    } else if (values.adFormat == "DOCUMENT") {
      return { type: "SPONSORED_CONTENT", format: "DOCUMENT" };
    }
    throw new Error("Invalid ad format");
  }

  function onSubmit(values: CampaignGroupFormSchema) {
    const adFormat = getAdFormat(values);
    console.log({
      title: values.title,
      linkedInAdAccountId: adAccountId,
      startDatetime: values.startDatetime,
      endDatetime: values.endDatetime,
      totalBudget: values.totalBudget,
      monthlyBudget: values.monthlyBudget,
      adFormat: adFormat,
      leadGenForm: selectedFormId,
      type: values.type,
    });
    if (
      values.objectiveType === "LEAD_GENERATION" ||
      adFormat.format === "SPONSORED_INMAIL"
    ) {
      if (!selectedFormId && !destinationUrl) return;

      updateCampaignGroupMutation.mutate({
        id: campaignGroupId,
        data: {
          title: values.title,
          linkedInAdAccountId: adAccountId,
          startDatetime: values.startDatetime,
          endDatetime: values.endDatetime,
          totalBudget: values.totalBudget ?? null,
          monthlyBudget: values.monthlyBudget ?? null,
          adFormat: adFormat,
          objectiveType: "LEAD_GENERATION",
          leadGenForm: selectedFormId ?? undefined,
          type: values.type,
          destinationUrl: destinationUrl ?? undefined,
        },
      });
    } else {
      if (adFormat.format === "VIDEO") {
        updateCampaignGroupMutation.mutate({
          id: campaignGroupId,
          data: {
            title: values.title,
            linkedInAdAccountId: adAccountId,
            startDatetime: values.startDatetime,
            endDatetime: values.endDatetime ?? null,
            totalBudget: values.totalBudget ?? null,
            monthlyBudget: values.monthlyBudget ?? null,
            adFormat: adFormat,
            objectiveType: "VIDEO_VIEW",
            type: values.type,
          },
        });
      }
      if (adFormat.format === "SINGLE_IMAGE") {
        updateCampaignGroupMutation.mutate({
          id: campaignGroupId,
          data: {
            title: values.title,
            linkedInAdAccountId: adAccountId,
            startDatetime: values.startDatetime,
            endDatetime: values.endDatetime ?? null,
            totalBudget: values.totalBudget ?? null,
            monthlyBudget: values.monthlyBudget ?? null,
            adFormat: adFormat,
            objectiveType: values.objectiveType,
            type: values.type,
          },
        });
      }
      if (adFormat.format === "DOCUMENT") {
        updateCampaignGroupMutation.mutate({
          id: campaignGroupId,
          data: {
            title: values.title,
            linkedInAdAccountId: adAccountId,
            startDatetime: values.startDatetime,
            endDatetime: values.endDatetime,
            totalBudget: values.totalBudget ?? null,
            monthlyBudget: values.monthlyBudget ?? null,
            adFormat: adFormat,
            objectiveType: values.objectiveType,
            type: values.type,
          },
        });
      }
    }
  }

  return (
    <>
      {linkedInAdProgramQuery.data && (
        <CampaignGroupForm
          adAccount={adAccountId}
          onSubmit={onSubmit}
          isSubmitting={isSubmitting}
          setSelectedFormId={setSelectedFormId}
          selectedFormId={selectedFormId}
          adFormUrn={linkedInAdProgramQuery.data.leadGenForm}
          destinationUrl={destinationUrl}
          setDestinationUrl={setDestinationUrl}
          initalData={{
            title: linkedInAdProgramQuery.data.title,
            startDatetime: linkedInAdProgramQuery.data.startDatetime,
            endDatetime: linkedInAdProgramQuery.data.endDatetime,
            totalBudget: linkedInAdProgramQuery.data.totalBudget,
            monthlyBudget: linkedInAdProgramQuery.data.monthlyBudget,
            type: linkedInAdProgramQuery.data.type,
            adFormat: linkedInAdProgramQuery.data.adFormat.format as
              | "SINGLE_IMAGE"
              | "VIDEO",
            objectiveType:
              linkedInAdProgramQuery.data.objectiveType == "LEAD_GENERATION"
                ? "LEAD_GENERATION"
                : "BRAND_AWARENESS",
          }}
        />
      )}
    </>
  );
}

export function CampaignGroupForm({
  adAccount,
  onSubmit,
  isSubmitting,
  setSelectedFormId,
  selectedFormId,
  initalData,
  adFormUrn,
  destinationUrl,
  setDestinationUrl,
}: {
  adAccount: string;
  onSubmit: (values: CampaignGroupFormSchema) => void;
  isSubmitting: boolean;
  setSelectedFormId: (id: string) => void;
  selectedFormId: string | null;
  initalData: CampaignGroupFormSchema;
  adFormUrn?: string | null;
  destinationUrl?: string | null;
  setDestinationUrl: (url: string | null) => void;
}) {
  const { form, schema } = useCampaignGroupForm(initalData);
  const { userId } = useAuth();
  const [showFade, setShowFade] = useState(true);
  const contentRef = useRef<HTMLDivElement>(null);

  const handleScroll = useCallback(() => {
    if (!contentRef.current) return;
    const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 30;
    setShowFade(!isNearBottom);
  }, []);

  useEffect(() => {
    const contentElement = contentRef.current;
    if (contentElement) {
      contentElement.addEventListener("scroll", handleScroll);
      handleScroll();
    }
    return () => {
      if (contentElement)
        contentElement.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  useEffect(() => {
    if (adFormUrn) {
      setSelectedFormId(adFormUrn);
      setDestinationUrl(null);
    }
  }, [adFormUrn, setSelectedFormId]);

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "objectiveType") {
        const currentObjectiveType = value.objectiveType;
        const currentAdFormat = value.adFormat;

        if (
          currentObjectiveType !== "LEAD_GENERATION" &&
          currentAdFormat === "SPONSORED_INMAIL"
        ) {
          form.setValue("adFormat", "SINGLE_IMAGE");
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  function submitForm(values: z.infer<typeof schema>) {
    onSubmit(values);
  }

  const todayDate = new Date(Date.now());
  todayDate.setHours(0, 0, 0, 0);

  const [firstPass, setFirstPass] = useState(true);

  useEffect(() => {
    if (form.watch("type") == "EVENT_DRIVEN") {
      form.setValue("monthlyBudget", undefined);
    }
    if (form.watch("type") == "EVERGREEN") {
      form.setValue("totalBudget", undefined);
      form.setValue("endDatetime", undefined);
    }
  }, [form.watch("type")]);

  return (
    <div className="h-full w-full bg-background">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="relative flex h-full w-full flex-col items-start justify-between"
        >
          <div
            ref={contentRef}
            className="flex h-[calc(100vh-16px)] w-full flex-col items-start justify-start overflow-auto pb-40 pl-8 pr-8 pt-8 md:pl-20"
          >
            {/* Header */}
            <div id="header" className="mb-6">
              <h2 className="text-2xl font-semibold">Campaign Setup</h2>
              <h3 className="mt-1 text-sm text-muted-foreground">
                Details of Campaign
              </h3>
            </div>

            <div className="flex w-full max-w-md flex-col items-start justify-start space-y-6">
              {/* Title Field */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="w-full max-w-md">
                    <FormLabel className="text-lg font-medium">Title</FormLabel>
                    <FormControl>
                      <Input
                        className="w-full bg-background"
                        placeholder="Enter campaign title"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* Goal Field */}
              <div className="w-full">
                <FormField
                  control={form.control}
                  name="objectiveType"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <div className="mb-2">
                        <FormLabel className="text-lg font-medium">
                          Goal
                        </FormLabel>
                      </div>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="space-y-2"
                      >
                        {/* Brand Awareness Option */}
                        <FormItem className="space-y-0">
                          <label className="text-sm" htmlFor="brand-awareness">
                            <Card
                              className={cn(
                                "relative cursor-pointer border-2 transition-all",
                                field.value === "BRAND_AWARENESS"
                                  ? "border-primary bg-accent/20"
                                  : "border-border hover:border-muted-foreground/50",
                              )}
                            >
                              <div className="flex items-center space-x-3 p-3">
                                <FormControl>
                                  <RadioGroupItem
                                    value="BRAND_AWARENESS"
                                    id="brand-awareness"
                                    className="bg-background"
                                  />
                                </FormControl>
                                <div className="flex cursor-pointer items-center font-medium">
                                  <TrendingUp className="mr-1 h-4 w-4 text-primary" />
                                  Awareness
                                </div>
                              </div>
                            </Card>
                          </label>
                        </FormItem>

                        {/* Lead Generation Option */}
                        <FormItem className="space-y-0">
                          <label className="text-sm" htmlFor="lead-generation">
                            <Card
                              className={cn(
                                "relative cursor-pointer border-2 transition-all",
                                field.value === "LEAD_GENERATION"
                                  ? "border-primary bg-accent/20"
                                  : "border-border hover:border-muted-foreground/50",
                              )}
                            >
                              <div className="flex items-center space-x-3 p-3">
                                <FormControl>
                                  <RadioGroupItem
                                    value="LEAD_GENERATION"
                                    id="lead-generation"
                                    className="bg-background"
                                  />
                                </FormControl>
                                <div className="flex cursor-pointer items-center font-medium">
                                  <MessageSquare className="mr-1 h-4 w-4 text-primary" />
                                  Lead generation
                                </div>
                              </div>
                            </Card>
                          </label>
                        </FormItem>
                      </RadioGroup>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* Type Field */}
              <div className="w-full">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <div className="mb-2">
                        <FormLabel className="text-lg font-medium">
                          Type
                        </FormLabel>
                      </div>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="space-y-2"
                      >
                        {/* Brand Awareness Option */}
                        <FormItem className="space-y-0">
                          <label className="text-sm" htmlFor="evergreen">
                            <Card
                              className={cn(
                                "relative cursor-pointer border-2 transition-all",
                                field.value === "EVERGREEN"
                                  ? "border-primary bg-accent/20"
                                  : "border-border hover:border-muted-foreground/50",
                              )}
                            >
                              <div className="flex items-center space-x-3 p-3">
                                <FormControl>
                                  <RadioGroupItem
                                    value="EVERGREEN"
                                    id="evergreen"
                                    className="bg-background"
                                  />
                                </FormControl>
                                <div className="flex cursor-pointer items-center font-medium">
                                  <TreePine className="mr-1 h-4 w-4 text-primary" />
                                  Evergreen
                                </div>
                              </div>
                            </Card>
                          </label>
                        </FormItem>

                        {/* Lead Generation Option */}
                        <FormItem className="space-y-0">
                          <label className="text-sm" htmlFor="event-driven">
                            <Card
                              className={cn(
                                "relative cursor-pointer border-2 transition-all",
                                field.value === "EVENT_DRIVEN"
                                  ? "border-primary bg-accent/20"
                                  : "border-border hover:border-muted-foreground/50",
                              )}
                            >
                              <div className="flex items-center space-x-3 p-3">
                                <FormControl>
                                  <RadioGroupItem
                                    value="EVENT_DRIVEN"
                                    id="event-driven"
                                    className="bg-background"
                                  />
                                </FormControl>
                                <div className="flex cursor-pointer items-center font-medium">
                                  <CalendarClock className="mr-1 h-4 w-4 text-primary" />
                                  Event-driven
                                </div>
                              </div>
                            </Card>
                          </label>
                        </FormItem>
                      </RadioGroup>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Timeline Fields */}
              {form.watch("type") != undefined && (
                <div className="w-full">
                  <FormLabel className="mb-2 block text-lg font-medium">
                    Timeline
                  </FormLabel>
                  <div className="flex w-full max-w-md items-start justify-start space-x-3">
                    <FormField
                      control={form.control}
                      name="startDatetime"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground",
                                  )}
                                >
                                  <CalendarIcon className="mr-2 h-4 w-4 opacity-70" />
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Start Date</span>
                                  )}
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < todayDate}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {form.watch("type") == "EVENT_DRIVEN" && (
                      <FormField
                        control={form.control}
                        name="endDatetime"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      "w-full pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground",
                                    )}
                                  >
                                    <CalendarIcon className="mr-2 h-4 w-4 opacity-70" />
                                    {field.value ? (
                                      format(field.value, "PPP")
                                    ) : (
                                      <span>End Date</span>
                                    )}
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={field.value ?? undefined}
                                  onSelect={field.onChange}
                                  disabled={(date) => date < todayDate}
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                </div>
              )}

              {/* Budget Field */}
              {form.watch("type") == "EVENT_DRIVEN" && (
                <div className="w-full">
                  <FormLabel className="mb-2 block text-lg font-medium">
                    Total Budget
                  </FormLabel>
                  <MoneyInput
                    form={form}
                    name="totalBudget"
                    placeholder="Enter campaign budget"
                    className="w-full max-w-md"
                  />
                </div>
              )}

              {form.watch("type") == "EVERGREEN" && (
                <div className="w-full">
                  <FormLabel className="mb-2 block text-lg font-medium">
                    Monthly Budget
                  </FormLabel>
                  <MoneyInput
                    form={form}
                    name="monthlyBudget"
                    placeholder="Enter campaign budget"
                    className="w-full max-w-md"
                  />
                </div>
              )}

              {/* Ad Format Field */}
              <div className="w-full">
                <FormLabel className="mb-2 block text-lg font-medium">
                  Ad Format
                </FormLabel>
                <FormField
                  control={form.control}
                  name="adFormat"
                  render={({ field }) => (
                    <FormItem className="w-full max-w-md">
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="grid grid-cols-1 gap-3 sm:grid-cols-2"
                      >
                        {/* Single Image */}
                        <FormItem className="space-y-0">
                          <label className="text-sm" htmlFor="single-image">
                            <Card
                              className={cn(
                                "relative max-w-sm cursor-pointer border-2 transition-all",
                                field.value === "SINGLE_IMAGE"
                                  ? "border-primary bg-accent/20"
                                  : "border-border hover:border-muted-foreground/50",
                              )}
                            >
                              <div className="flex items-center space-x-3 p-3">
                                <FormControl>
                                  <RadioGroupItem
                                    value="SINGLE_IMAGE"
                                    id="single-image"
                                    className="bg-background"
                                  />
                                </FormControl>
                                <div className="flex cursor-pointer items-center font-medium">
                                  <Image className="mr-1 h-4 w-4 text-primary" />
                                  Single Image
                                </div>
                              </div>
                            </Card>
                          </label>
                        </FormItem>

                        {/* Video */}
                        <FormItem className="space-y-0">
                          <label className="text-sm" htmlFor="video">
                            <Card
                              className={cn(
                                "relative max-w-sm cursor-pointer border-2 transition-all",
                                field.value === "VIDEO"
                                  ? "border-primary bg-accent/20"
                                  : "border-border hover:border-muted-foreground/50",
                              )}
                            >
                              <div className="flex items-center space-x-3 p-3">
                                <FormControl>
                                  <RadioGroupItem
                                    value="VIDEO"
                                    id="video"
                                    className="bg-background"
                                  />
                                </FormControl>
                                <div className="flex cursor-pointer items-center font-medium">
                                  <Video className="mr-1 h-4 w-4 text-primary" />
                                  Video
                                </div>
                              </div>
                            </Card>
                          </label>
                        </FormItem>

                        {/* Conversation */}
                        <FormItem className="space-y-0">
                          <FormControl>
                            <Card
                              className={cn(
                                "relative cursor-pointer border-2 transition-all",
                                field.value === "SPONSORED_INMAIL"
                                  ? "border-primary bg-accent/20"
                                  : "border-border hover:border-muted-foreground/50",
                                form.watch("objectiveType") !==
                                  "LEAD_GENERATION" &&
                                  "cursor-not-allowed opacity-50 hover:border-border",
                              )}
                            >
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center space-x-3 p-3">
                                      <RadioGroupItem
                                        value="SPONSORED_INMAIL"
                                        id="sponsored-inmail"
                                        className="bg-background"
                                        disabled={
                                          form.watch("objectiveType") !==
                                          "LEAD_GENERATION"
                                        }
                                      />
                                      <FormLabel
                                        htmlFor="sponsored-inmail"
                                        className={cn(
                                          "flex cursor-pointer items-center font-medium",
                                          form.watch("objectiveType") !==
                                            "LEAD_GENERATION" &&
                                            "cursor-not-allowed",
                                        )}
                                      >
                                        <MessageSquare className="mr-1 h-4 w-4 text-primary" />
                                        Conversation
                                      </FormLabel>
                                    </div>
                                  </TooltipTrigger>
                                  {form.watch("objectiveType") !==
                                    "LEAD_GENERATION" && (
                                    <TooltipContent className="animation-in max-w-xs rounded-md border bg-secondary p-3 text-sm text-secondary-foreground shadow-md fade-in-50 zoom-in-95">
                                      <p>
                                        Conversation ads are only available with
                                        Lead Generation goal
                                      </p>
                                    </TooltipContent>
                                  )}
                                </Tooltip>
                              </TooltipProvider>
                            </Card>
                          </FormControl>
                        </FormItem>

                        {/* Document Ads */}
                        <FormItem className="space-y-0">
                          <label className="text-sm" htmlFor="document">
                            <Card
                              className={cn(
                                "relative cursor-pointer border-2 transition-all",
                                field.value === "DOCUMENT"
                                  ? "border-primary bg-accent/20"
                                  : "border-border hover:border-muted-foreground/50",
                              )}
                            >
                              <div className="flex items-center space-x-3 p-3">
                                <FormControl>
                                  <RadioGroupItem
                                    value="DOCUMENT"
                                    id="document"
                                    className="bg-background"
                                  />
                                </FormControl>
                                <div className="flex cursor-pointer items-center font-medium">
                                  <FileText className="mr-1 h-4 w-4 text-primary" />
                                  Document Ads
                                </div>
                              </div>
                            </Card>
                          </label>
                        </FormItem>
                      </RadioGroup>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormLabel
                className={cn(
                  "mb-0 block text-lg font-medium text-muted-foreground",
                  form.watch("objectiveType") === "LEAD_GENERATION" &&
                    "mb-2 text-black",
                )}
              >
                Destination
              </FormLabel>
              {form.watch("objectiveType") === "LEAD_GENERATION" && (
                <p className="!mt-0 pt-0 text-sm text-muted-foreground">
                  Send users to a Lead Generation Form or a URL after they click
                  on your ad.
                </p>
              )}
              {/* Lead Gen Form Dialog */}
              {form.watch("objectiveType") === "LEAD_GENERATION" && (
                <div className="uration-300 w-full  pt-2 animate-in fade-in">
                  <Label
                    className={cn(
                      "mb-1",
                      destinationUrl ? "text-muted-foreground" : "text-black",
                    )}
                  >
                    Lead Gen Form
                  </Label>
                  <LeadGenFormsDialog
                    setSelectedFormId={setSelectedFormId}
                    selectedFormId={selectedFormId}
                    disabled={!!destinationUrl}
                    adAccountId={adAccount}
                  />
                </div>
              )}

              {form.watch("adFormat") === "SPONSORED_INMAIL" && (
                <div className="flex w-full flex-col">
                  <Label
                    className={cn(
                      "mb-1",
                      selectedFormId ? "text-muted-foreground" : "text-black",
                    )}
                  >
                    Destination URL
                  </Label>
                  <Input
                    value={destinationUrl === null ? "" : destinationUrl}
                    onChange={(e) => {
                      const value = e.target.value;
                      setDestinationUrl(value);
                    }}
                    disabled={selectedFormId !== null}
                    placeholder="Enter a CTA Destination URL"
                    aria-label="Destination URL"
                    className={cn(
                      "w-full",
                      !!destinationUrl &&
                        "border-2 border-primary bg-accent/20",
                    )}
                  ></Input>
                  {destinationUrl &&
                    !/^https?:\/\/.+/i.test(destinationUrl) && (
                      <p className="mt-2 text-sm text-destructive">
                        URL must start with http:// or https://
                      </p>
                    )}
                </div>
              )}
            </div>
          </div>

          {/* Fade effect at the bottom */}
          <div
            className={`pointer-events-none absolute bottom-16 left-0 right-0 h-24 transition-opacity duration-300 ${
              showFade ? "opacity-100" : "opacity-0"
            }`}
            style={{
              background:
                "linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,1))",
            }}
          ></div>

          {/* Submit button */}
          <div className="sticky bottom-0 left-0 right-0 z-10 flex h-16 w-full items-center justify-end border-t bg-background p-4">
            <Button
              type="submit"
              disabled={
                isSubmitting ||
                (form.watch("objectiveType") === "LEAD_GENERATION" &&
                  !selectedFormId &&
                  (!destinationUrl || !/^https?:\/\/.+/i.test(destinationUrl)))
              }
              size="default"
            >
              {isSubmitting && (
                <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
              )}
              Next <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

/*
adFormat: mysqlEnum("ad_format", [
    "simgle_image",
    "carousel_image",
    "spotlight",
    "document_ads",
  ]).notNull(),

*/
