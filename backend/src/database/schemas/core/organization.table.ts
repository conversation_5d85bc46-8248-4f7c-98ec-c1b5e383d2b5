import { varchar, boolean, integer } from "drizzle-orm/pg-core";

import { createdAtCol, idCol, updatedAtCol } from "../../commonDbCols";
import { coreSchema } from "../schemas";

export const organizationTable = coreSchema.table("organization", {
  id: idCol,
  name: varchar("name", { length: 255 }).notNull(),
  createdAt: createdAtCol,
  updatedAt: updatedAtCol,
  stripeCustomerId: varchar("stripe_customer_id", { length: 255 }),
  slackNotificationWebhookUrl: varchar("slack_notification_webhook_url", { length: 500 }),
  slackNotificationsEnabled: boolean("slack_notifications_enabled").default(true),
  slackNotificationDayOfWeek: integer("slack_notification_day_of_week").default(1), // 1 = Monday, 7 = Sunday, 8 = Everyday (for hourly/daily)
  slackNotificationFrequency: varchar("slack_notification_frequency", { length: 20 }).default("weekly"), // 'hourly', 'daily', 'weekly', 'biweekly', 'monthly'
  slackLeadNotificationsEnabled: boolean("slack_lead_notifications_enabled").default(true), // Enable/disable 15-minute lead notifications
});
