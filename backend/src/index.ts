import "./env";

import * as crypto from "crypto";
import { resolve } from "path";
import { clerkMiddleware } from "@hono/clerk-auth";
import { serve } from "@hono/node-server";
import { trpcServer } from "@hono/trpc-server";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { stream, streamSSE, streamText } from "hono/streaming";
import { serve as inngestServe } from "inngest/hono";

import { segmentService } from "@kalos/core/services/segments";
import { accountHanlders } from "@kalos/database/handlers/account";
import { contactHandlers } from "@kalos/database/handlers/contact";
import { crmUserHandlers } from "@kalos/database/handlers/crmUser";
import { jobFunctionHandlers } from "@kalos/database/handlers/jobFunction";
import { jobSeniorityHandlers } from "@kalos/database/handlers/jobSeniority";
import { opportunityHandlers } from "@kalos/database/handlers/opportunity";
import { opportunityPrimaryContactHandlers } from "@kalos/database/handlers/opportunityPrimaryContact";
import { segmentHandlers } from "@kalos/database/handlers/segment";
import { segmentValuePropHandlers } from "@kalos/database/handlers/segmentValueProp";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { organization } from "../../packages/database/src/schema/organization";
import { inngest } from "../../services/advertising/src/inngest/client";
import { db } from "./database/db";
import { stageTable } from "./database/schemas/advertising/stage.table";
import { jobFunctionTable } from "./database/schemas/core/jobFunction.table";
import { jobSeniorityTable } from "./database/schemas/core/jobSeniority.table";
import { abTestWorkers } from "./modules/advertising/abTest/public/workers";
import { AbTestFacadeService } from "./modules/advertising/application/services/abTest/abTest.facade.service";
import { MapKalosDataToLinkedInFacetEntitiesService } from "./modules/advertising/application/services/mapKalosDataToLinkedInFacetEntities.service";
import { StreamRefinedAdSegmentSocialPostBaseCopyUseCase } from "./modules/advertising/application/useCase/adSegmentSocialPostBaseCopy/streamRefinedAdSegmentSocialPostBaseCopy.useCase";
import { GetOrGenerateConversationMessageCopyForCreationFlowUseCase } from "./modules/advertising/application/useCase/conversationMessageCopy/getOrGenerateConversationMessageCopyForCreationFlow.useCase";
import { GetOrGenerateConversationMessageCopyForUpdateFlowUseCase } from "./modules/advertising/application/useCase/conversationMessageCopy/getOrGenerateConversationMessageCopyForUpdateFlow.useCase";
import { GetCopyVariantStreamingUseCase } from "./modules/advertising/application/useCase/copyVariant/getCopyVariantStreaming.useCase";
import { GetSpendInTimeRnageUseCase } from "./modules/advertising/application/useCase/linkedInCampaignGroup/getSpendInTimeRange.useCase";
import { GetOrGenerateSocialPostCopyForCreationFlowUseCase } from "./modules/advertising/application/useCase/socialPostCopy/getOrGenerateSocialPostCopyForCreationFlow.useCase";
import { linkedInAdProgramCreativeSchema } from "./modules/advertising/domain/entites/linkedInAdProgramCreative";
import { AdAudienceService } from "./modules/advertising/domain/services/adAudience.service";
import { AdCreativeService } from "./modules/advertising/domain/services/adCreative.service";
import { AdSegmentService } from "./modules/advertising/domain/services/adSegment.service";
import { AdSegmentValuePropService } from "./modules/advertising/domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "./modules/advertising/domain/services/conversationCallToActionCopy.service";
import { ConversationCopySerivce } from "./modules/advertising/domain/services/conversationCopy.service";
import { ConversationMessageCopyService } from "./modules/advertising/domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "./modules/advertising/domain/services/conversationSubjectCopy.service";
import { ExampleSocialPostService } from "./modules/advertising/domain/services/exampleSocialPost.service";
import { LinkedInAdAccountService } from "./modules/advertising/domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "./modules/advertising/domain/services/linkedInAdProgram.service";
import { LinkedInAdProgramAdCreativeService } from "./modules/advertising/domain/services/linkedInAdProgramAdCreative.service";
import { LinkedInAdSegmentSocialPostBaseCopyService } from "./modules/advertising/domain/services/linkedInAdSegmentSocialPostBaseCopy.service";
import { LinkedInCampaignService } from "./modules/advertising/domain/services/linkedInCampaign.service";
import { LinkedInCampaignGroupService } from "./modules/advertising/domain/services/linkedInCampaignGroup.service";
import { LinkedInConversationBaseCopyService } from "./modules/advertising/domain/services/linkedInConversationBaseCopy.service";
import { LinkedInPostService } from "./modules/advertising/domain/services/linkedInPost.service";
import { LinkedInSponsoredCreativeService } from "./modules/advertising/domain/services/linkedInSponsoredCreative.service";
import { PositioningService } from "./modules/advertising/domain/services/positioning.service";
import { SegmentAudienceTargetPrefabService } from "./modules/advertising/domain/services/segmentAudiencePrefab.service";
import { SocialPostAdCopyService } from "./modules/advertising/domain/services/socialPostAdCopy.service";
import { SocialPostStyleGuideService } from "./modules/advertising/domain/services/socialPostStyleGuide.service";
import { AdCreativeRepository } from "./modules/advertising/infrastructure/repositories/adCreative.repository";
import { AdSegmentValuePropRepository } from "./modules/advertising/infrastructure/repositories/adSegmentValueProp.repository";
import { CaseStudyRepository } from "./modules/advertising/infrastructure/repositories/caseStudy.repository";
import { ConversationCallToActionCopyRepository } from "./modules/advertising/infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationCopyRepository } from "./modules/advertising/infrastructure/repositories/conversationCopy.repository";
import { ConversationMessageCopyRepository } from "./modules/advertising/infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "./modules/advertising/infrastructure/repositories/conversationSubjectCopy.repository";
import { ExampleSocialPostRepository } from "./modules/advertising/infrastructure/repositories/exampleSocialPost.repository";
import { LinkedInAdAccountRepository } from "./modules/advertising/infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "./modules/advertising/infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "./modules/advertising/infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdProgramCreativeRepository } from "./modules/advertising/infrastructure/repositories/linkedInAdProgramCreative.repository";
import { LinkedInAdSegmentRepository } from "./modules/advertising/infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInAdSegmentConversationBaseCopyRepository } from "./modules/advertising/infrastructure/repositories/linkedInAdSegmentConversationPostBaseCopy.repository";
import { LinkedInAdSegmentSocialPostBaseCopyRepository } from "./modules/advertising/infrastructure/repositories/linkedInAdSegmentSocialPostBaseCopy.repository";
import { LinkedInCampaignRepository } from "./modules/advertising/infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInCampaignGroupRepository } from "./modules/advertising/infrastructure/repositories/linkedInCampaignGroup.repository";
import { LinkedInPostRepository } from "./modules/advertising/infrastructure/repositories/linkedInPost.repository";
import { LinkedInSponsoredCreativeRepository } from "./modules/advertising/infrastructure/repositories/linkedInSponsoredCreative.repository";
import { PositioningRepository } from "./modules/advertising/infrastructure/repositories/positioning.repository";
import { SegmentAudiencePrefabRepository } from "./modules/advertising/infrastructure/repositories/segmentAudienceTargetPrefabRepository";
import { SocialPostCallToActionCopyRepository } from "./modules/advertising/infrastructure/repositories/socialPostCallToActionCopy.repository";
import { SocialPostCopyRepository } from "./modules/advertising/infrastructure/repositories/socialPostCopy.repository";
import { SocialPostStyleGuideRepository } from "./modules/advertising/infrastructure/repositories/socialPostStyleGuide.repository";
import { AdCreativeS3StorageService } from "./modules/advertising/infrastructure/services/adCreativeS3Storage.service";
import { AdvertisingLlmCompletionsService } from "./modules/advertising/infrastructure/services/advertisingLlmCompletions.service";
import { LinkedInService } from "./modules/advertising/infrastructure/services/linkedIn.service";
import { VectorizedLinkedInFacetEntityStorageService } from "./modules/advertising/infrastructure/services/vectorizedLinkedInFacetEntityStorage.service";
import { linkedInLeadFormWebhook } from "./modules/advertising/interfaceAdapters/inngestFunctions";
import { billingCron } from "./modules/advertising/interfaceAdapters/inngestFunctions/billingCron.inngestFunction";
import { billingProcessBatch } from "./modules/advertising/interfaceAdapters/inngestFunctions/billingprocessBarch.inngestFunction";
import { campaignGroupSync } from "./modules/advertising/interfaceAdapters/inngestFunctions/campaignGroupStatusSync.inngestFunction";
import { createAdSegmentBaseConversationCopy } from "./modules/advertising/interfaceAdapters/inngestFunctions/createAdSegmentBaseConversation.inngestFunction";
import { createAdSegmentBaseSocialPostCopy } from "./modules/advertising/interfaceAdapters/inngestFunctions/createAdSegmentBaseSocialPost.inngestFunction";
import { createAudienceInngestFunction } from "./modules/advertising/interfaceAdapters/inngestFunctions/createAudience.inngestFunction";
import { createNewVarients } from "./modules/advertising/interfaceAdapters/inngestFunctions/createNewVarients.inngestFunction";
import { crmImportCron } from "./modules/advertising/interfaceAdapters/inngestFunctions/crmImportCron.inngestFunction";
import { processOrgCrmImport } from "./modules/advertising/interfaceAdapters/inngestFunctions/crmProcessOrgImport.inngestFunction";
import { deployAdProgram } from "./modules/advertising/interfaceAdapters/inngestFunctions/deployAdProgram.inngestFunction";
import { endAbTest } from "./modules/advertising/interfaceAdapters/inngestFunctions/endAbTest.inngestFunction";
import { endAbTestRound } from "./modules/advertising/interfaceAdapters/inngestFunctions/endAbTestRound.inngestFunction";
import { endAbTestRoundDay } from "./modules/advertising/interfaceAdapters/inngestFunctions/endAbTestRoundDay.inngestFunction";
import { endStage } from "./modules/advertising/interfaceAdapters/inngestFunctions/endStage.inngestFunction";
import { linkedInLeadIngestionScript } from "./modules/advertising/interfaceAdapters/inngestFunctions/linkedInLeadIngestionScript.inngestFunction";
import { scheduledLeadNotifications } from "./modules/advertising/interfaceAdapters/inngestFunctions/leadNotifications.inngestFunction";
import { manualBiddingEvent } from "./modules/advertising/interfaceAdapters/inngestFunctions/manualBiddingEvent.inngestFunction";
import { pollAdStatus } from "./modules/advertising/interfaceAdapters/inngestFunctions/pollAdStatus";
import { provisionAbTestRoundDayFunction } from "./modules/advertising/interfaceAdapters/inngestFunctions/provisionAbTestRoundDay.inngestFunction";
import { startAbTest } from "./modules/advertising/interfaceAdapters/inngestFunctions/runAbTest.inngestFunction";
import { runAbTestRound } from "./modules/advertising/interfaceAdapters/inngestFunctions/runAbTestRound.inngestFunction";
import { runAbTestRoundDay } from "./modules/advertising/interfaceAdapters/inngestFunctions/runAbTestRoundDay.inngestFunction";
import { runStage } from "./modules/advertising/interfaceAdapters/inngestFunctions/runStage.inngestFunction";
import { waitForAbTestRoundDay } from "./modules/advertising/interfaceAdapters/inngestFunctions/waitForAbTestRoundDay";
import { waitForNextManualBiddingEvent } from "./modules/advertising/interfaceAdapters/inngestFunctions/waitForNextManualBiddingEvent.inngestFunction";
import { dailySlackNotifications } from "./modules/advertising/interfaceAdapters/inngestFunctions/dailySlackNotifications.inngestFunction";
import { aggregateErrorLogs } from "./modules/shared/infrastructure/inngestFunctions/aggregateErrorLogs.inngestFunction";
import { linkedinOAuthRoutes } from "./modules/advertising/interfaceAdapters/routes/oauth/linkedin.routes";
import { advertisingInngestClient } from "./modules/advertising/utils/advertisingInngestClient";
import { advertisingLangfuseClient } from "./modules/advertising/utils/advertisingLangfuseClient";
import { linkedInUrnToId } from "./modules/advertising/utils/linkedInUrnUtils";
import { Segment } from "./modules/core/domain/entites/segment";
import { segmentValueProp } from "./modules/core/domain/entites/segmentValudProp";
import { SegmentService } from "./modules/core/domain/services/segment.service";
import { SegmentValuePropService } from "./modules/core/domain/services/segmentValueProp.service";
import { JobFunction } from "./modules/core/domain/valueObjects/jobFunction";
import { JobSeniority } from "./modules/core/domain/valueObjects/jobSenitory";
import { OrganizationRepository } from "./modules/core/infrastructure/repositories/organization.repository";
import { segmentRepository } from "./modules/core/infrastructure/repositories/segment.repository";
import { SegmentValuePropRepository } from "./modules/core/infrastructure/repositories/segmentValueProp.repository";
import { LangfusePromptStorageService } from "./modules/core/infrastructure/services/langfusePromptStorage.service";
import { OpenAiCompletionService } from "./modules/core/infrastructure/services/openAiCompletion.service";
import { OpenAiEmbeddingService } from "./modules/core/infrastructure/services/openAiEmbedding.service";
import { PromptExecutionService } from "./modules/core/infrastructure/services/promptExecutionService.service";
import { TransactionManagerService } from "./modules/core/infrastructure/services/transcationManager.service";
import { UtilsLlmCompletionsService } from "./modules/core/infrastructure/services/utilsLlmCompletions.service";
import { utilsLangfuseClient } from "./modules/core/utils/langfuseClients";
import { createUuid } from "./modules/core/utils/uuid";
import { Account } from "./modules/crm/domain/entities/account";
import { Contact } from "./modules/crm/domain/entities/contact";
import { CrmUser } from "./modules/crm/domain/entities/crmUser";
import { Opportunity } from "./modules/crm/domain/entities/opportunity";
import { OpportunityPrimaryContact } from "./modules/crm/domain/entities/opportunityPrimaryContact";
import { accountService } from "./modules/crm/domain/services/account.service";
import { AvomaCredentialsService } from "./modules/crm/domain/services/avomaCredentials.service";
import { contactService } from "./modules/crm/domain/services/contact.service";
import { crmUserService } from "./modules/crm/domain/services/crmUser.service";
import { HubspotCredentialsService } from "./modules/crm/domain/services/hubspotCredentials.service";
import { opportunityService } from "./modules/crm/domain/services/opportunity.service";
import { HubspotProvider } from "./modules/crm/infrastructure/externalCrms/hubspot.externalCrm.provider";
import { accountRepository } from "./modules/crm/infrastructure/repositories/account.repository";
import { AvomaCredentialsRepository } from "./modules/crm/infrastructure/repositories/avomaCredentials.repository";
import { contactRepository } from "./modules/crm/infrastructure/repositories/contact.repository";
import { crmUserRepository } from "./modules/crm/infrastructure/repositories/crmUser.repository";
import { HubspotCredentialsRepository } from "./modules/crm/infrastructure/repositories/hubspotCredentials.repository";
import { opportunityRepository } from "./modules/crm/infrastructure/repositories/opportunity.repository";
import { opportunityPrimaryContactRepository } from "./modules/crm/infrastructure/repositories/opportunityPrimaryContact.repository";
import { AvomaSalesCallProvider } from "./modules/crm/infrastructure/salesCallProviders/avoma.salesCall.provider";
import { backendRouter } from "./trpc/router/trpcRouter";
import { createTRPCContext } from "./trpc/trpc";

const app = new Hono();

app.use(
  "/*",
  cors({
    origin: "*",
  }),
);

app.use(clerkMiddleware());

app.get("/", (c) => {
  return c.text("Hello Hono!");
});

app.use(
  "/trpc/*",
  trpcServer({
    router: backendRouter,
    createContext: createTRPCContext,
    onError:
      process.env.NODE_ENV === "development"
        ? ({ path, error }) => {
            console.error(
              `❌ tRPC failed on ${path ?? "<no-path>"}: ${error.message}`,
            );
          }
        : ({ path, error }) => {
            console.error(
              `❌ tRPC failed on ${path ?? "<no-path>"}: \n ${error.message}\n ${error.stack}-----------}`,
            );
          },
  }),
);

const port = 8000;
console.log(`Server is running on http://localhost:${port}`);

app.on(
  ["GET", "PUT", "POST"],
  "/inngest/ads",
  inngestServe({
    client: advertisingInngestClient,
    functions: [
      createAudienceInngestFunction,
      crmImportCron,
      processOrgCrmImport,
      createAdSegmentBaseSocialPostCopy,
      pollAdStatus,
      provisionAbTestRoundDayFunction,
      linkedInLeadFormWebhook,
      linkedInLeadIngestionScript,
      scheduledLeadNotifications,
      waitForAbTestRoundDay,
      endAbTest,
      createAdSegmentBaseConversationCopy,
      runStage,
      endStage,
      waitForNextManualBiddingEvent,
      deployAdProgram,
      billingCron,
      billingProcessBatch,
      campaignGroupSync,
      createNewVarients,
      manualBiddingEvent,
      dailySlackNotifications,
      aggregateErrorLogs,
      ...abTestWorkers,
    ],
  }),
);

app.get("/tstDrizzle", async (c) => {
  try {
    await db.insert(stageTable).values({
      id: "0195fcb1-7bbd-7d56-b10a-e5f6191d9b4a",
      linkedInAdSegmentid: "0195fc9e-9152-721b-9018-bc06d717d170",
      index: 0,
      stageType: "AD_PROGRAM",
      status: "RUNNING",
    });
  } catch (error) {
    const errObject = error as {
      code: string;
      detail: string;
      schema: string;
      table: string;
      constraint: string;
      file: string;
      line: string;
      routine: string;
      length: number;
      name: string;
      severity: string;
    };

    /*

  const "error": {
    "length": 220,
    "name": "error",
    "severity": "ERROR",
    "code": "23505",
    "detail": "Key (id)=(0195fcb1-7bbd-7d56-b10a-e5f6191d9b4a) already exists.",
    "schema": "advertising",
    "table": "stage",
    "constraint": "stage_pkey",
    "file": "nbtinsert.c",
    "line": "666",
    "routine": "_bt_check_unique"
  }

  */

    return c.json({
      error: errObject,
    });
  }
});

app.post("/stream", (c) => {
  return stream(c, async (stream) => {
    // Write a process to be executed when aborted.

    console.log("STARTING CONTROLLER");
    const socialPostAdCopyService = new SocialPostAdCopyService(
      new SocialPostCopyRepository(),
    );
    const adSegmentValuePropService = new AdSegmentValuePropService(
      new AdSegmentValuePropRepository(),
    );

    const llmCompletionService = new OpenAiCompletionService();
    const langfusePromptStorageService = new LangfusePromptStorageService(
      advertisingLangfuseClient,
    );

    const promptExecutionService = new PromptExecutionService(
      langfusePromptStorageService,
      llmCompletionService,
    );
    const advertisingLlmCompletionsService =
      new AdvertisingLlmCompletionsService(promptExecutionService);

    const adSegmentRepository = new LinkedInAdSegmentRepository();
    const positioningRepository = new PositioningRepository();
    const exampleSocialPostRepository = new ExampleSocialPostRepository();
    const socialPostStyleGuideRepository = new SocialPostStyleGuideRepository();
    const caseStudyRepository = new CaseStudyRepository();
    const linkedInAdSegmentSocialPostBaseCopyService =
      new LinkedInAdSegmentSocialPostBaseCopyService(
        new LinkedInAdSegmentSocialPostBaseCopyRepository(),
      );

    const adSegmentService = new AdSegmentService(adSegmentRepository);
    const segmentService = new SegmentService(segmentRepository);
    const positioningService = new PositioningService(positioningRepository);
    const socialPostStyleGuideService = new SocialPostStyleGuideService(
      socialPostStyleGuideRepository,
    );
    const exampleSocialPostService = new ExampleSocialPostService(
      exampleSocialPostRepository,
    );

    /*
       private readonly socialPostAdCopyService: SocialPostAdCopyService,
  private readonly adSegmentValuePropService: AdSegmentValuePropService,
  private readonly llmCompletions: ILLMCompletionService,
  private readonly promptStorage: IPromptStorageService,
  private readonly advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService,
  private readonly asegmentService: AdSegmentService,
  private readonly segmentService: SegmentService,
  private readonly positioningService: PositioningService,
  private readonly styleGuideService: SocialPostStyleGuideService,
  private readonly exampleSocialPostService: ExampleSocialPostService,
  private readonly organizationId: number,
  private readonly adSegmentSocialPostBaseCopyService: LinkedInAdSegmentSocialPostBaseCopyService,

      */

    const adProgramService = new LinkedInAdProgramService(
      new LinkedInAdProgramRepository(),
    );

    const body = await c.req.json();
    const organizationId = body.organizationId;
    const socialPostCallToActionCopyRepository =
      new SocialPostCallToActionCopyRepository();
    const useCase = new GetOrGenerateSocialPostCopyForCreationFlowUseCase({
      socialPostAdCopyService: socialPostAdCopyService,
      adSegmentValuePropService: adSegmentValuePropService,
      llmCompletions: llmCompletionService,
      promptStorage: langfusePromptStorageService,
      advertisingLlmCompletionsService: advertisingLlmCompletionsService,
      adSegmentService: adSegmentService,
      segmentService: segmentService,
      positioningService: positioningService,
      styleGuideService: socialPostStyleGuideService,
      exampleSocialPostService: exampleSocialPostService,
      adSegmentSocialPostBaseCopyService:
        linkedInAdSegmentSocialPostBaseCopyService,
      organizationId: organizationId,
      adProgramService: adProgramService,
      socialPostCallToActionCopyRepository:
        socialPostCallToActionCopyRepository,
    });

    const res = useCase.execute({
      linkedInAdSegmentValuePropId: body.valuePropId,
      socialPostCopyType: "standard",
    });

    for await (const each of res) {
      stream.write(`${JSON.stringify(each)}!JSON_LINE_END!`);
    }
  });
});

app.post("/convostream", (c) => {
  return stream(c, async (stream) => {
    // Write a process to be executed when aborted.

    console.log("STARTING CONTROLLER");
    const socialPostAdCopyService = new SocialPostAdCopyService(
      new SocialPostCopyRepository(),
    );
    const adSegmentValuePropService = new AdSegmentValuePropService(
      new AdSegmentValuePropRepository(),
    );

    const llmCompletionService = new OpenAiCompletionService();
    const langfusePromptStorageService = new LangfusePromptStorageService(
      advertisingLangfuseClient,
    );

    const promptExecutionService = new PromptExecutionService(
      langfusePromptStorageService,
      llmCompletionService,
    );
    const advertisingLlmCompletionsService =
      new AdvertisingLlmCompletionsService(promptExecutionService);

    const adSegmentRepository = new LinkedInAdSegmentRepository();
    const positioningRepository = new PositioningRepository();
    const exampleSocialPostRepository = new ExampleSocialPostRepository();
    const socialPostStyleGuideRepository = new SocialPostStyleGuideRepository();
    const caseStudyRepository = new CaseStudyRepository();
    const linkedInAdSegmentSocialPostBaseCopyService =
      new LinkedInAdSegmentSocialPostBaseCopyService(
        new LinkedInAdSegmentSocialPostBaseCopyRepository(),
      );

    const adSegmentService = new AdSegmentService(adSegmentRepository);
    const segmentService = new SegmentService(segmentRepository);
    const positioningService = new PositioningService(positioningRepository);
    const socialPostStyleGuideService = new SocialPostStyleGuideService(
      socialPostStyleGuideRepository,
    );

    const conversationBaseCopyService = new LinkedInConversationBaseCopyService(
      new LinkedInAdSegmentConversationBaseCopyRepository(),
    );

    /*
       private readonly socialPostAdCopyService: SocialPostAdCopyService,
  private readonly adSegmentValuePropService: AdSegmentValuePropService,
  private readonly llmCompletions: ILLMCompletionService,
  private readonly promptStorage: IPromptStorageService,
  private readonly advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService,
  private readonly asegmentService: AdSegmentService,
  private readonly segmentService: SegmentService,
  private readonly positioningService: PositioningService,
  private readonly styleGuideService: SocialPostStyleGuideService,
  private readonly exampleSocialPostService: ExampleSocialPostService,
  private readonly organizationId: number,
  private readonly adSegmentSocialPostBaseCopyService: LinkedInAdSegmentSocialPostBaseCopyService,

      */

    const body = await c.req.json();
    const organizationId = body.organizationId;
    const conversationMessageCopyService = new ConversationMessageCopyService(
      new ConversationMessageCopyRepository(),
    );

    const conversationSubjectCopyService = new ConversationSubjectCopyService(
      new ConversationSubjectCopyRepository(),
    );
    const conversationCallToActionCopyService =
      new ConversationCallToActionCopyService(
        new ConversationCallToActionCopyRepository(),
      );

    const adProgramService = new LinkedInAdProgramService(
      new LinkedInAdProgramRepository(),
    );

    const useCase =
      new GetOrGenerateConversationMessageCopyForCreationFlowUseCase({
        adSegmentValuePropService: adSegmentValuePropService,
        llmCompletions: llmCompletionService,
        promptStorage: langfusePromptStorageService,
        advertisingLlmCompletionsService: advertisingLlmCompletionsService,
        adSegmentService: adSegmentService,
        segmentService: segmentService,
        positioningService: positioningService,
        conversationMessageCopyService: conversationMessageCopyService,
        conversationBaseCopyService: conversationBaseCopyService,
        conversationSubjectCopyService: conversationSubjectCopyService,
        conversationCallToActionCopyService:
          conversationCallToActionCopyService,
        organizationId: organizationId,
        adProgramService: adProgramService,
      });

    console.log(body);
    const res = useCase.execute({
      linkedInAdSegmentValuePropId: body.valuePropId,
      conversationMessageCopyType: "standard",
      conversationSubjectCopyType: body.subjectType as
        | "HARD_OFFER_TRIAL"
        | "SOFT_OFFER_AUDIT"
        | "NO_OFFER_INTRIGUE"
        | "NO_OFFER_VALUE_PROPOSITION"
        | "HARD_OFFER_COMNPANY_GEAR",
      conversationCallToActionCopyType: "standard",
      leadGenFormUrn: body.leadGenFormUrn,
      injectedSubject: body.injectedSubject,
      destinationUrl: body.destinationUrl,
    });

    for await (const each of res) {
      stream.write(`${JSON.stringify(each)}!JSON_LINE_END!`);
    }
  });
});

app.post("/stream-refined", (c) => {
  return stream(c, async (stream) => {
    const body = await c.req.json();
    const baseCopy = body.baseCopy;
    const feedback = body.feedback;
    const config = body.config;
    if (!baseCopy || !feedback) {
      throw new Error("No base copy or feedback");
    }

    const useCase = new StreamRefinedAdSegmentSocialPostBaseCopyUseCase(
      new AdvertisingLlmCompletionsService(
        new PromptExecutionService(
          new LangfusePromptStorageService(advertisingLangfuseClient),
          new OpenAiCompletionService(),
        ),
      ),
      new LangfusePromptStorageService(advertisingLangfuseClient),
      new OpenAiCompletionService(),
    );

    const res = useCase.execute({ baseCopy, feedback, config });

    for await (const each of res) {
      stream.write(each);
    }
  });
});

app.post("/stream-copy-variant", async (c) => {
  console.log("STARTING CONTROLLER");
  console.log(c.req.json());
  console.log("SFKDSHJFLKJHDSLKFH");
  return stream(c, async (stream) => {
    const body = await c.req.json();
    console.log("BODY", body);
    const organizationId = body.organizationId;

    // standardCopy can be empty string as seen in the client code

    // Create necessary services
    const llmCompletionService = new OpenAiCompletionService();
    const langfusePromptStorageService = new LangfusePromptStorageService(
      advertisingLangfuseClient,
    );
    const promptExecutionService = new PromptExecutionService(
      langfusePromptStorageService,
      llmCompletionService,
    );
    const advertisingLlmCompletionsService =
      new AdvertisingLlmCompletionsService(promptExecutionService);

    // Instantiate the use case
    const useCase = new GetCopyVariantStreamingUseCase({
      organizationId,
      advertisingLlmCompletionsService,
    });

    // Execute and stream the response
    const streamResult = useCase.executeStream(body);

    for await (const chunk of streamResult) {
      stream.write(`${JSON.stringify(chunk)}!JSON_LINE_END!`);
    }
  });
});

serve({
  fetch: app.fetch,
  port,
});

async function updatePrefabs() {
  const prefabs = new SegmentAudienceTargetPrefabService(
    new SegmentAudiencePrefabRepository(),
  );

  const transactionManager = new TransactionManagerService();

  return prefabs.getForSegment("0193fc90-060c-7443-95ef-c95199110544");
}

app.on(["POST", "GET"], "/webhooks/linkedin-leads", async (c) => {
  console.log(
    "/webhooks/linkedin-leads Request Received. Method:",
    c.req.method,
  );
  if (c.req.method === "GET") {
    const challenge = c.req.query("challengeCode") as string;

    if (!challenge) {
      return c.json(
        {
          message: "No challenge code",
        },
        400,
      );
    }

    const hmac = crypto.createHmac(
      "sha256",
      process.env.LINKEDIN_CLIENT_SECRET as string,
    );
    hmac.update(challenge);
    const responseHash = hmac.digest("hex");

    console.log("challenge");
    console.log(challenge);
    console.log("responseHash");
    console.log(responseHash);
    return c.json({
      challengeCode: challenge,
      challengeResponse: responseHash,
    });
  }

  if (c.req.method === "POST") {
    console.log("/webhooks/linkedin-leads POST Processing");

    const signatureHeader = c.req.header("X-LI-Signature");
    const rawBodyArrayBuffer = await c.req.raw.arrayBuffer();
    const rawBodyBuffer = Buffer.from(rawBodyArrayBuffer);
    console.log("Body buffer created");
    const body = JSON.parse(rawBodyBuffer.toString("utf-8"));

    await advertisingInngestClient.send({
      name: "linkedin/lead.updated",
      data: body,
      sig: signatureHeader,
    });

    if (!signatureHeader) {
      console.log("webhooks/linkedin-leads No signature header");
      return c.json(
        {
          message: "No signature header",
        },
        401,
      );
    }

    const hmac = crypto.createHmac(
      "sha256",
      process.env.LINKEDIN_CLIENT_SECRET as string,
    );

    hmac.update("hmacsha256=" + rawBodyBuffer);
    const signature = hmac.digest("hex");

    console.log("webhooks/linkedin-leads signature c.req.raw.body");
    console.log(signature);
    console.log("webhooks/linkedin-leads signatureHeader c.req.raw.body");
    console.log(signatureHeader);

    if (signature !== signatureHeader) {
      console.log("webhooks/linkedin-leads signature header doesn't match");
      return c.json(
        {
          message: "Invalid signature",
        },
        401,
      );
    }

    return c.json({
      message: "Webhook received",
    });
  }
});

app.get("/vp", async (c) => {
  const segments = await segmentRepository.getAllForOrganization(3);
  const svc = new SegmentValuePropRepository();
  const transactionManager = new TransactionManagerService();
  await transactionManager.startTransaction(async (tx) => {
    for (const eachSegment of segments) {
      const valueProps = await segmentValuePropHandlers.select.many(
        eachSegment.id,
      );
      console.log(valueProps.length);
      for (const eachValueProp of valueProps) {
        await svc.createValueProp(
          segmentValueProp.parse({
            id: createUuid(),
            segmentId: eachValueProp.segmentId,
            name: eachValueProp.valueProp,
          }),
          tx,
        );
      }
    }
  });
});

app.get("/cleanvp", async (c) => {
  await cleanVp(3);
  return c.text("done");
});

async function cleanVp(organizationId: number) {
  const segments =
    await segmentRepository.getAllForOrganization(organizationId);

  const segmentValuePropRepository = new SegmentValuePropRepository();
  for (const eachSegment of segments) {
    const valueProps = await segmentValuePropRepository.getValuePropsForSegment(
      eachSegment.id,
    );
    console.log(valueProps.length);
    for (const eachValueProp of valueProps) {
      await segmentValuePropRepository.deleteOne(eachValueProp.id);
    }
  }
}

app.get("/fix", async (c) => {
  const urn = "urn:li:sponsoredCampaignGroup:*********";
  const client = await getLinkedInApiClientFromOrganizationId(3);
  if (!client) {
    throw new Error("No client");
  }
  const adAccountRepository = new LinkedInAdAccountRepository();
  const adAccounts = await adAccountRepository.getForOrganization(3);
  const adAccount = adAccounts[0];
  if (!adAccount) {
    throw new Error("No ad account");
  }

  const res = await client.getCampaignGroup(
    linkedInUrnToId(adAccount.linkedInAdAccountUrn),
    linkedInUrnToId(urn),
  );

  console.log(res);
});

app.get("/senders", async (c) => {
  const client = await getLinkedInApiClientFromOrganizationId(2);
  if (!client) {
    throw new Error("No client");
  }

  const adAccountRepository = new LinkedInAdAccountRepository();
  const adAccounts = await adAccountRepository.getForOrganization(2);
  const adAccount = adAccounts[0];
  if (!adAccount) {
    throw new Error("No ad account");
  }
  const res = await client.getSenders(adAccount.linkedInAdAccountUrn);
  return c.json(res);
});

app.get("/avoma", async (c) => {
  const avomaCredentialsService = new AvomaCredentialsService(
    new AvomaCredentialsRepository(),
  );
  const avomaCredentials = await avomaCredentialsService.getAvomaCredentials(4);
  if (!avomaCredentials) {
    throw new Error("No avoma credentials found");
  }
  const avomaSalesCallProvider = new AvomaSalesCallProvider(avomaCredentials);
  // get meeting
  const meeting = await avomaSalesCallProvider.getMeeting(
    "c58b1684-27f0-4287-b5b5-8d4dae9cb2fc",
  );
});

app.get("/update-contacts", async (c) => {
  const organizationId = 10;
  const transactionManager = new TransactionManagerService();

  const contactGenerator =
    await opportunityPrimaryContactRepository.getForOrganization(
      organizationId,
      100,
    );
  const jobFunctions = await db.select().from(jobFunctionTable);
  const jobSeniorities = await db.select().from(jobSeniorityTable);
  let missedCount = 0;

  for await (const contactBatch of contactGenerator) {
    const withNoFunctionOrSenitority = contactBatch.filter(
      (each) => !each.jobFunctionId && !each.jobSeniorityId,
    );
    for (const eachContact of withNoFunctionOrSenitority) {
      const res = await fetch("http://localhost:9000/jobTitle", {
        method: "POST",
        body: JSON.stringify({
          job_title: eachContact.title,
        }),
        headers: { "Content-Type": "application/json" },
      });
      if (res.status > 299) {
        continue;
      }
      try {
        const data = (await res.json()) as {
          job_function: string;
          job_seniority: string;
        };
        console.log(data);
        const jobFunction =
          jobFunctions.find((jf) => jf.name == data.job_function)?.id ?? 12;
        const jobSeniority =
          jobSeniorities.find((js) => js.name == data.job_seniority)?.id ?? 6;
        if (!jobFunction || !jobSeniority) {
          console.log("MISSED");
          missedCount++;
          continue;
        }

        await contactRepository.updateJobFunctionAndSeniority({
          contactId: eachContact.contactId,
          jobFunction: data.job_function as any,
          jobSeniority: data.job_seniority as any,
        });
        console.log(
          `${eachContact.contactId} - ${eachContact.title} - ${data.job_function} - ${data.job_seniority}`,
        );
      } catch (e) {
        console.log(e);
        missedCount++;
        continue;
      }
    }
    continue;
  }
  return c.json({ missedCount });
});

app.get("/schema", async (c) => {
  const organizationId = 5;
  const hubspotCredentialsService = new HubspotCredentialsService(
    new HubspotCredentialsRepository(),
  );

  const credentials =
    await hubspotCredentialsService.getHubspotCredentials(organizationId);
  if (!credentials) {
    throw new Error("No hubspot credentials found");
  }

  const hubspotProvider = await HubspotProvider.createFactory(credentials);

  const schema = await hubspotProvider.getSchema("opportunity");

  return c.json(schema);
});

app.get("/do-thing", async (c) => {
  const organizationId = 5;
  const hubspotCredentialsService = new HubspotCredentialsService(
    new HubspotCredentialsRepository(),
  );
  const credentials =
    await hubspotCredentialsService.getHubspotCredentials(organizationId);
  if (!credentials) {
    throw new Error("No hubspot credentials found");
  }
  const hubspotProvider = await HubspotProvider.createFactory(credentials);
  const res = await hubspotProvider.doThing();
  return c.json(res);
});

app.get("/audience-count", async (c) => {
  const client = await getLinkedInApiClientFromOrganizationId(2);
  const adAudienceRepository = new LinkedInAdAudienceRepository();
  const adAudienceService = new AdAudienceService(adAudienceRepository);
  const adSegmentRepository = new LinkedInAdSegmentRepository();
  const adSegmentService = new AdSegmentService(adSegmentRepository);

  const adAudience = await adAudienceService.getOne(
    "01950ac9-f26f-7099-9f1a-c1b150919d57",
  );
  if (!adAudience) {
    throw new Error("No ad segment found");
  }
  if (!client) {
    throw new Error("No linkedin client found");
  }
  const linkedInService = new LinkedInService(client);
  const res = await linkedInService.getAudienceCount({
    audienceTargets: adAudience.audienceTargetCriteria,
  });
  return c.json(res);
});

app.get("/test", async (c) => {
  const client = await getLinkedInApiClientFromOrganizationId(2);
  if (!client) {
    throw new Error("No linkedin client found");
  }
  const message = await client.getSponsoredConversationMessage(
    "urn:li:sponsoredConversation:6178146",
    "urn:li:sponsoredMessageContent:(urn:li:sponsoredConversation:6178146,6638276)",
  );
  const conversation = await client.getSponsoredConversation(
    "urn:li:sponsoredConversation:6178146",
  );

  const res = await client.updateSponsorConversationFirstMessage({
    conversationUrn: "urn:li:sponsoredConversation:6178146",
    firstMessageContentUrn:
      "urn:li:sponsoredMessageContent:(urn:li:sponsoredConversation:6178146,6638276)",
  });
  return c.json({ message, conversation });
});

app.get("/suggested-bidding", async (c) => {
  const client = await getLinkedInApiClientFromOrganizationId(2);
  if (!client) {
    throw new Error("No linkedin client found");
  }
  const linkedInService = new LinkedInService(client);
  const campaign = await client.getCampaign(*********, *********);
  const res = await linkedInService.getSuggestedBidding({
    adAccountUrn: campaign.account,
    bidType: campaign.costType as any,
    objectiveType: campaign.objectiveType as any,
    campaignType: campaign.type as any,
    audienceTargets: {
      mode: "linkedIn",
      targetingCriteria: campaign.targetingCriteria,
    },
    optimizationTargetType: campaign.optimizationTargetType,
  });

  await linkedInService.updateCampaignBid({
    adAccountUrn: campaign.account,
    campaignUrn: "urn:li:sponsoredCampaign:*********",
    bid: Number(campaign.unitCost.amount) + 1,
  });

  return c.json(res);
});

app.get("/spend-in-time-range", async (c) => {
  const client = await getLinkedInApiClientFromOrganizationId(2);
  if (!client) {
    throw new Error("No linkedin client found");
  }

  const linkedInService = new LinkedInService(client);
  const useCase = new GetSpendInTimeRnageUseCase({
    linkedInService,
    linkedInCampaignGroupRepository: new LinkedInCampaignGroupRepository(),
  });
  const res = await useCase.execute({
    organizationId: 2,
    timeRange: { startDate: new Date("2025-03-01"), endDate: new Date() },
  });
  return c.json(res);
});

app.get("/ab-test-testing", async (c) => {
  const abTestFacadeService = AbTestFacadeService.createFactory(
    "conversationSubject",
  );
  const res = await abTestFacadeService.getData(
    "019567b6-c7de-7c9a-8d25-a118955c8015",
  );
  return c.json(res);
});

app.get("/ab-test-testing2", async (c) => {
  const abTestFacadeService = AbTestFacadeService.createFactory("creative");

  const adSegmentRepository = new LinkedInAdSegmentRepository();
  const adSegmentService = new AdSegmentService(adSegmentRepository);
  const adSegment = await adSegmentService.getOne(
    "01958108-015c-74c6-964b-c3b0ccd3c0bb",
  );
  if (!adSegment) {
    throw new Error("No ad segment found");
  }
  const adProgramRepository = new LinkedInAdProgramRepository();
  const adProgramService = new LinkedInAdProgramService(adProgramRepository);
  const adProgram = await adProgramService.getOne(
    adSegment.linkedInAdProgramId,
  );
  if (!adProgram) {
    throw new Error("No ad program found");
  }

  const res = await abTestFacadeService.getWouldBeRoundsAds(
    "01958117-6223-7030-8528-a3beb46aa6d5",
    adProgram,
    adSegment,
  );
  return c.json(res);
});

app.route("/api/oauth2/linkedin/callback", linkedinOAuthRoutes);

app.get("/test-manual-bidding", async (c) => {
  const client = await getLinkedInApiClientFromOrganizationId(6);
  if (!client) {
    throw new Error("No linkedin client found");
  }
  const linkedInService = new LinkedInService(client);
  const campaign = await linkedInService.getCampaign({
    campaignUrn: "urn:li:sponsoredCampaign:*********",
    adAccountUrn: "urn:li:sponsoredAccount:*********",
  });
  console.log("STUFF", {
    adAccountUrn: campaign.account,
    bidType: campaign.costType as any,
    objectiveType: campaign.objectiveType as any,
    campaignType: campaign.type as any,
    audienceTargets: {
      mode: "linkedIn",
      targetingCriteria: campaign.targetingCriteria,
    },
    optimizationTargetType: campaign.optimizationTargetType,
  });
  const dailyBudget = Number(campaign.dailyBudget?.amount);
  const res = await linkedInService.getSuggestedBidding({
    adAccountUrn: campaign.account,
    bidType: campaign.costType as any,
    objectiveType: campaign.objectiveType as any,
    campaignType: campaign.type as any,
    audienceTargets: {
      mode: "linkedIn",
      targetingCriteria: campaign.targetingCriteria,
    },
    optimizationTargetType: campaign.optimizationTargetType,
    dailyBudget: dailyBudget,
    currencyCode: campaign.dailyBudget?.currencyCode,
  });

  return c.json(res);
});

app.get("/migrate411data", async (c) => {
  const organizationRepository = new OrganizationRepository();
  const linkedInAdProgramRepository = new LinkedInAdProgramRepository();
  const adSegmentRepository = new LinkedInAdSegmentRepository();
  const campaignGroupRepository = new LinkedInCampaignGroupRepository();
  const adSegmentValuePropRepository = new AdSegmentValuePropRepository();
  const socialPostCopyRepository = new SocialPostCopyRepository();
  const socialPostCallToActionRepository =
    new SocialPostCallToActionCopyRepository();
  const conversationSubjectRepository = new ConversationSubjectCopyRepository();
  const conversationCallToActionRepository =
    new ConversationCallToActionCopyRepository();
  const conversationMessageRepository = new ConversationMessageCopyRepository();

  const organizations = await organizationRepository.getAll({
    offset: 0,
    limit: 10000,
  });
  const transactionManager = new TransactionManagerService();
  await transactionManager.startTransaction(async (tx) => {
    for (const organization of organizations) {
      const adPrograms = await linkedInAdProgramRepository.getForOrganization(
        organization.organizationId,
      );
      for (const adProgram of adPrograms) {
        const adSegments = await adSegmentRepository.getManyForAdProgram(
          adProgram.id,
        );
        for (const adSegment of adSegments) {
          const campaignGroup =
            await campaignGroupRepository.getOneByAdSegmentId(adSegment.id);
          if (campaignGroup) {
            const adSegmentValueProps =
              await adSegmentValuePropRepository.getManyForAdSegment(
                adSegment.id,
                "DRAFT",
                tx,
              );
            await adSegmentValuePropRepository.updateManyToActive(
              adSegmentValueProps.map((each) => each.id),
              tx,
            );
            for (const adSegmentValueProp of adSegmentValueProps) {
              const socialPostCopies =
                await socialPostCopyRepository.getAllForLAdSegmentValueProp(
                  adSegmentValueProp.id,
                  "DRAFT",
                  tx,
                );
              await socialPostCopyRepository.updateManyToActive(
                socialPostCopies.map((each) => each.id),
                tx,
              );

              const socialPostCallToActionCopies =
                await socialPostCallToActionRepository.getManyForLinkedInAdSegmentValueProp(
                  {
                    linkedInAdSegmentValuePropId: adSegmentValueProp.id,
                    status: "DRAFT",
                  },
                  tx,
                );
              await socialPostCallToActionRepository.updateManyToActive(
                socialPostCallToActionCopies.map((each) => each.id),
                tx,
              );

              const conversationSubjectCopies =
                await conversationSubjectRepository.getAllForValueProp(
                  adSegmentValueProp.id,
                  "DRAFT",
                  tx,
                );
              await conversationSubjectRepository.updateManyToActive(
                {
                  ids: conversationSubjectCopies.map((each) => each.id),
                },
                tx,
              );

              const conversationMessageCopies =
                await conversationMessageRepository.getAllForValueProp(
                  adSegmentValueProp.id,
                  "DRAFT",
                  tx,
                );
              await conversationMessageRepository.updateManyToActive(
                {
                  ids: conversationMessageCopies.map((each) => each.id),
                },
                tx,
              );

              const conversationCallToActionCopies =
                await conversationCallToActionRepository.getAllForValueProp(
                  adSegmentValueProp.id,
                  "DRAFT",
                  tx,
                );
              await conversationCallToActionRepository.updateManyToActive(
                {
                  ids: conversationCallToActionCopies.map((each) => each.id),
                },
                tx,
              );
            }
          }
        }
      }
    }
  });
  return c.json({ success: true });
});
