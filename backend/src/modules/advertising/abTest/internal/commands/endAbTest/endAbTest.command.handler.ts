import { err, ok, Result } from "neverthrow";

import { AbTestDomain } from "../../domain/abTest.domain";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { EndAbTestCommand } from "./endAbTest.command.interface";

interface EndAbTestError {
  type:
    | "AB_TEST_NOT_FOUND"
    | "AB_TEST_NOT_IN_PROGRESS"
    | "AB_TEST_ALREADY_ENDED"
    | "NOT_ALL_AB_TEST_ROUNDS_COMPLETED";
}

export class EndAbTestCommandHandler {
  constructor(
    private readonly ctx: {
      abTestRepository: IAbTestRepository;
      abTestRoundRepository: IAbTestRoundRepository;
    },
  ) {}

  async execute(
    command: EndAbTestCommand,
  ): Promise<Result<void, EndAbTestError>> {
    const abTest = await this.ctx.abTestRepository.getOne(
      command.abTestId,
      command.abTestType,
    );
    if (!abTest) {
      return err({ type: "AB_TEST_NOT_FOUND" });
    }

    if (abTest.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_NOT_IN_PROGRESS" });
    }

    const abTestRounds = await this.ctx.abTestRoundRepository.getAllForAbTest(
      command.abTestId,
      command.abTestType,
      command.tx,
    );

    const completedAbTest = AbTestDomain.complete({
      abTest: abTest,
      abTestRoundsForAbTest: abTestRounds,
    });
    if (completedAbTest.isErr()) {
      return err(completedAbTest.error);
    }

    await this.ctx.abTestRepository.updateOne(
      completedAbTest.value,
      command.tx,
    );

    return ok();
  }
}
