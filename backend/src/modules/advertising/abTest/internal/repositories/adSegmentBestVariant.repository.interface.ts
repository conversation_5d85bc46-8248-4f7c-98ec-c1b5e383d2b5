import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTestType } from "../domain/abTestType.valueObject";
import { AdSegmentBestVariant } from "../domain/adSegmentBestVarient.entity";

export interface IAdSegmentBestVariantRepository {
  upsertOne(
    adSegmentBestVariant: AdSegmentBestVariant,
    tx?: ITransaction,
  ): Promise<void>;
  getOne(
    adSegmentId: string,
    type: AbTestType,
    tx?: ITransaction,
  ): Promise<AdSegmentBestVariant | null>;
}
