import { z } from "zod";

import { linkedInAudienceTargetCriteriaSchema } from "../../../../domain/valueObjects/linkedinAudienceTargeting/linkedinTargetAudienceCriteria";

export const getAudienceCountsDtoRequestSchema = z.object({
  targetingCriteria: linkedInAudienceTargetCriteriaSchema,
  adAccountUrn: z.string().optional(),
});

export type GetAudienceCountsDtoRequest = z.infer<
  typeof getAudienceCountsDtoRequestSchema
>;
