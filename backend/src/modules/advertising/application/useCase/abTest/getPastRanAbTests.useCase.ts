import { adCreative } from "../../../../../../../packages/database/src/schema/adCreative";
import { linkedInAd } from "../../../../../../../packages/database/src/schema/linkedInAd";
import { AbTest } from "../../../domain/entites/abTest";
import { AdSegmentValueProp } from "../../../domain/entites/adSegmentValueProp";
import { AdCreativeService } from "../../../domain/services/adCreative.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramAdCreativeService } from "../../../domain/services/linkedInAdProgramAdCreative.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SocialPostCallToActionCopyService } from "../../../domain/services/socialPostCallToActionCopy.service";
import { GetPastRanAbTestsDtoSchema } from "../../dtos/controllerDtos/abTest/getPastRanAbTests.dto";
import { GetUpcomingRunningAbTestsDto } from "../../dtos/controllerDtos/abTest/getUpcomingRunningAbTests.dto";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTestFacadeService } from "../../services/abTest/abTest.facade.service";

export interface UpcomingAbTest {
  stageId: string;
  stageType:
    | "audienceTest"
    | "valuePropTest"
    | "creativeTest"
    | "adCopyTest"
    | "conversationSubjectTest"
    | "socialPostBodyCopyTest"
    | "socialPostCallToActionTest"
    | "conversationCallToActionTest";
  valueProps?: AdSegmentValueProp[];
  creatives?: {
    id: string;
    fileName: string;
    fileType: string;
    presignedUrl: string;
  }[];
  conversationSubjects?: {}[];
  sponsoredCreativeId: string;
  sponsoredCreativeUrn: string;
  adVarients: Record<string, string>;
  rounds?: Record<string, string>;
  status: string;
  type: string;
}

export class GetPastRanAbTestsUseCase {
  constructor(
    private readonly ctx: {
      stageRepository: IStageRepository;
      adSegmentValuePropService: AdSegmentValuePropService;
      adProgramAdCreativeService: LinkedInAdProgramAdCreativeService;
      adCreativeService: AdCreativeService;
      socialPostCopyService: SocialPostAdCopyService;
      socialPostCallToActionCopyService: SocialPostCallToActionCopyService;
      conversationCopyService: ConversationSubjectCopyService;
      conversationMessageCopyService: ConversationMessageCopyService;
      conversationCallToActionCopyService: ConversationCallToActionCopyService;
    },
  ) {}

  async execute(dto: GetPastRanAbTestsDtoSchema) {
    try {
      // Validate adSegmentId
      if (!dto.adSegmentId) {
        return []; // Return empty array if no adSegmentId provided
      }

      const stages = await this.ctx.stageRepository.getStagesForAdSegment(
        dto.adSegmentId,
      );

      if (!stages || stages.length === 0) {
        return []; // Return empty array if no stages found
      }

      const res: UpcomingAbTest[] = [];
      const creativeUrnMap = new Map<string, boolean>();

      for (const [index, stage] of stages.entries()) {
        if (stage.status !== "FINISHED") {
          continue;
        }

        let abTest: AbTest["type"] | undefined = undefined;

        if (stage.stageType == "audienceTest") {
          abTest = "audience";
        } else if (stage.stageType == "valuePropTest") {
          abTest = "valueProp";
          const valueProps =
            await this.ctx.adSegmentValuePropService.getManyForAdSegment(
              stage.adSegmentId,
              "ACTIVE", // TODO-BR CHANGE to ACTIVE
            );
        } else if (stage.stageType == "creativeTest") {
          abTest = "creative";
        } else if (stage.stageType == "conversationSubjectTest") {
          abTest = "conversationSubject";
        } else if (stage.stageType == "adCopyTest") {
          abTest = "conversationSubject";
        } else if (stage.stageType == "socialPostBodyCopyTest") {
          abTest = "conversationSubject";
        } else if (stage.stageType === "socialPostCallToActionTest") {
          abTest = "socialPostCallToAction";
        } else if (stage.stageType === "conversationCallToActionTest") {
          abTest = "conversationCallToAction";
        } else {
          console.error(`Invalid stage type: ${stage.stageType}`);
          continue; // Skip this stage and continue with the next one
        }

        try {
          //For Sponsored_Conversation or sponsored_inmail, don't get mid-campaign data (valuePropTest)

          const abTestFacadeService = AbTestFacadeService.createFactory(abTest);
          const adsAndRoundsData = await abTestFacadeService.getAdsForStageId(
            stage.id,
            creativeUrnMap,
          );

          if (!adsAndRoundsData) {
            return {};
          }

          for (const ad of adsAndRoundsData?.ads) {
            if (creativeUrnMap.has(ad.sponsoredCreativeUrn)) {
              continue;
            }
            creativeUrnMap.set(ad.sponsoredCreativeUrn, true);

            if (stage.stageType === "creativeTest") {
              const adCreativeId = ad.adVarients.adCreativeId;

              if (adCreativeId) {
                const adProgramAdCreative =
                  await this.ctx.adProgramAdCreativeService.getOne({
                    id: adCreativeId,
                  });

                if (!adProgramAdCreative) {
                  res.push({
                    stageId: stage.id,
                    stageType: stage.stageType,
                    sponsoredCreativeId: ad.sponsoredCreativeId,
                    sponsoredCreativeUrn: ad.sponsoredCreativeUrn,
                    adVarients: { ...ad.adVarients },
                    status: stage.status,
                    type: abTest,
                  });
                  continue;
                }

                const adCreativeData =
                  await this.ctx.adCreativeService.getOneWithDownloadPreSignedUrlAndMetadata(
                    adProgramAdCreative?.adCreativeId,
                  );

                if (!adCreativeData) {
                  res.push({
                    stageId: stage.id,
                    stageType: stage.stageType,
                    sponsoredCreativeId: ad.sponsoredCreativeId,
                    sponsoredCreativeUrn: ad.sponsoredCreativeUrn,
                    adVarients: { ...ad.adVarients },
                    status: stage.status,
                    type: abTest,
                  });
                  continue;
                }

                (ad.adVarients as Record<string, any>).adCreativeMetadata =
                  adCreativeData;

                res.push({
                  stageId: stage.id,
                  stageType: stage.stageType,
                  sponsoredCreativeId: ad.sponsoredCreativeId,
                  sponsoredCreativeUrn: ad.sponsoredCreativeUrn,
                  adVarients: { ...ad.adVarients },
                  status: stage.status,
                  type: abTest,
                });
              }
            } else {
              res.push({
                stageId: stage.id,
                stageType: stage.stageType,
                sponsoredCreativeId: ad.sponsoredCreativeId,
                sponsoredCreativeUrn: ad.sponsoredCreativeUrn,
                adVarients: { ...ad.adVarients },
                status: stage.status,
                type: abTest,
              });
            }
          }
        } catch (error) {
          console.error(`Error fetching data for stage ${stage.id}:`, error);
          // Continue to the next stage instead of failing the entire request
        }
      }
      return res;
    } catch (error) {
      console.error("Error in getUpcomingRunningAbTests:", error);
      return []; // Return empty array in case of errors
    }
  }
}
