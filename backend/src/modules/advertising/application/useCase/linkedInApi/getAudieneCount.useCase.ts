import { AdAudienceService } from "../../../domain/services/adAudience.service";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { GetAudienceCountsDtoRequest } from "../../dtos/controllerDtos/linkedInApi/getAudienceCounts.dto";

export class GetAudienceCountUseCase {
  constructor(private readonly linkedInService: LinkedInService) { }

  async execute(input: GetAudienceCountsDtoRequest) {
    const audienceCount = await this.linkedInService.getAudienceCount({
      audienceTargets: input.targetingCriteria,
      adAccountUrn: input.adAccountUrn,
    });
    return audienceCount;
  }
}
