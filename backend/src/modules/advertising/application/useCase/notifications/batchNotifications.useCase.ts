import { SendNotificationUseCase } from "./sendNotification.useCase";
import { IOrganizationRepository } from "../../../../core/application/interfaces/repositories/organization.repository.interface";

export class BatchNotificationsUseCase {
  constructor(
    private organizationRepository: IOrganizationRepository,
    private sendNotificationUseCase: SendNotificationUseCase
  ) {}

  async execute(): Promise<{ processed: number; succeeded: number; failed: number }> {
    const now = new Date();
    const currentDayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentHour = now.getHours();
    
    console.log(`Processing notifications for ${now.toISOString().split('T')[0]}, day ${currentDayOfWeek} (0=Sunday, 1=Monday, etc.), hour ${currentHour}`);

    // Get all organizations
    const allOrganizations = await this.organizationRepository.selectAll();
    
    // Filter organizations that have notifications enabled and webhook configured
    const eligibleOrganizations = allOrganizations.filter(org => {
      // Must have webhook URL and notifications enabled
      if (!org.slackNotificationWebhookUrl || !org.slackNotificationsEnabled) {
        return false;
      }

      // Check if organization is due for notification based on frequency
      if (!this.isOrganizationDueForNotification(org.slackNotificationFrequency, now)) {
        console.log(`Skipping organization ${org.organizationId} - not due for ${org.slackNotificationFrequency} notification today`);
        return false;
      }

      // Check day of week requirement (not applicable for hourly/daily frequencies if set to "everyday")
      const orgDayOfWeek = org.slackNotificationDayOfWeek || 1; // Default to Monday if not set
      
      if (orgDayOfWeek === 8) {
        // Everyday option - applies to hourly and daily frequencies
        if (org.slackNotificationFrequency !== "hourly" && org.slackNotificationFrequency !== "daily") {
          console.log(`Skipping organization ${org.organizationId} - "everyday" option only valid for hourly/daily frequencies`);
          return false;
        }
        // No day restriction for everyday option
      } else {
        // Specific day requirement
        // Convert organization's day setting (1=Monday, 7=Sunday) to JavaScript's day format (0=Sunday, 1=Monday)
        const jsOrgDayOfWeek = orgDayOfWeek === 7 ? 0 : orgDayOfWeek; // Convert Sunday from 7 to 0
        
        if (currentDayOfWeek !== jsOrgDayOfWeek) {
          console.log(`Skipping organization ${org.organizationId} - their notification day is ${orgDayOfWeek} but today is ${currentDayOfWeek}`);
          return false;
        }
      }
      
      return true;
    });

    console.log(`Found ${eligibleOrganizations.length} eligible organizations out of ${allOrganizations.length} total`);
    
    let processed = 0;
    let succeeded = 0;
    let failed = 0;

    // Process each eligible organization
    for (const organization of eligibleOrganizations) {
      processed++;
      
      try {
        // Calculate the appropriate date range based on frequency
        const { weekStartDate, weekEndDate } = this.calculateDateRange(organization.slackNotificationFrequency, now);
        
        const result = await this.sendNotificationUseCase.execute({
          organizationId: organization.organizationId,
          weekStartDate,
          weekEndDate,
        });

        if (result.success) {
          succeeded++;
        } else {
          failed++;
          console.error(`Failed to send notification for organization ${organization.organizationId}: ${result.error}`);
        }
      } catch (error) {
        failed++;
        console.error(`Unexpected error processing organization ${organization.organizationId}:`, error);
      }

      // Add a small delay to avoid overwhelming Slack API
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`Batch processing complete. Processed: ${processed}, Succeeded: ${succeeded}, Failed: ${failed}`);
    
    return { processed, succeeded, failed };
  }

  private isOrganizationDueForNotification(frequency: "hourly" | "daily" | "weekly" | "biweekly" | "monthly", currentDate: Date): boolean {
    const today = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
    const currentHour = currentDate.getHours();
    
    switch (frequency) {
      case "hourly":
        // Hourly notifications run every hour
        return true;
        
      case "daily":
        // Daily notifications run once per day (at the scheduled time)
        return true;
        
      case "weekly":
        // Weekly notifications run every week on the configured day
        return true;
        
      case "biweekly":
        // Biweekly notifications run every 2 weeks
        // Use week number to determine if this is an even or odd week
        const weekNumber = this.getWeekNumber(today);
        return weekNumber % 2 === 0; // Run on even weeks
        
      case "monthly":
        // Monthly notifications run on the configured day of the first week of each month
        const dayOfMonth = today.getDate();
        return dayOfMonth <= 7; // First week of the month
        
      default:
        return false;
    }
  }

  private calculateDateRange(frequency: "hourly" | "daily" | "weekly" | "biweekly" | "monthly", currentDate: Date): { weekStartDate: Date; weekEndDate: Date } {
    const now = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
    
    switch (frequency) {
      case "hourly":
        // Calculate last hour's data
        const hourEndDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(), currentDate.getHours() - 1, 59, 59); // End of previous hour
        const hourStartDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(), currentDate.getHours() - 1, 0, 0); // Start of previous hour
        
        return { weekStartDate: hourStartDate, weekEndDate: hourEndDate };
        
      case "daily":
        // Calculate yesterday's data
        const dailyEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59); // End of yesterday
        const dailyStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0); // Start of yesterday
        
        return { weekStartDate: dailyStartDate, weekEndDate: dailyEndDate };
        
      case "weekly":
        // Calculate last 7 days from the selected date
        const weekEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1); // Yesterday
        const weekStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7); // 7 days ago
        
        return { weekStartDate, weekEndDate };
        
      case "biweekly":
        // Calculate previous 2 weeks date range
        const biweeklyEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1); // Yesterday
        const biweeklyStartDate = new Date(biweeklyEndDate.getFullYear(), biweeklyEndDate.getMonth(), biweeklyEndDate.getDate() - 13); // 14 days ago
        
        return { weekStartDate: biweeklyStartDate, weekEndDate: biweeklyEndDate };
        
      case "monthly":
        // Calculate previous month's date range
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1); // First day of last month
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0); // Last day of last month
        
        return { weekStartDate: lastMonth, weekEndDate: lastMonthEnd };
        
      default:
        // Fallback to weekly
        const fallbackEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        const fallbackStartDate = new Date(fallbackEndDate.getFullYear(), fallbackEndDate.getMonth(), fallbackEndDate.getDate() - 6);
        return { weekStartDate: fallbackStartDate, weekEndDate: fallbackEndDate };
    }
  }

  private getWeekNumber(date: Date): number {
    // Get the week number of the year
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }
} 