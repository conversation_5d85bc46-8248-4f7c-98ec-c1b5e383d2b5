import { and, eq } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import {
  audienceTestTable,
  conversationCallToActionTestTable,
  conversationMessageCopyTestTable,
  conversationSubjectTestTable,
  creativeTestTable,
  socialPostBodyCopyTestTable,
  socialPostCallToActionTestTable,
  valuePropTestTable,
} from "../../../../database/schemas/advertising/abTests/abTestTables";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IAbTestRepository } from "../../application/interfaces/infrastructure/repositories/abTest.repository.interface";
import { AbTest } from "../../domain/entites/abTest";
import { AbTestRound } from "../../domain/entites/abTestRound";
import { AbTestRoundDay } from "../../domain/entites/abTestRoundDay";
import { abTestRoundTableRegistry } from "./abTestRound.repository";
import { abTestRoundDayRegistry } from "./abTestRoundDay.repository";

export const abTestTableRegistry = {
  audience: audienceTestTable,
  valueProp: valuePropTestTable,
  creative: creativeTestTable,
  conversationSubject: conversationSubjectTestTable,
  socialPostBodyCopy: socialPostBodyCopyTestTable,
  conversationMessageCopy: conversationMessageCopyTestTable,
  conversationCallToAction: conversationCallToActionTestTable,
  socialPostCallToAction: socialPostCallToActionTestTable,
};

export class AbTestRepository implements IAbTestRepository {
  async createOne(input: AbTest, tx?: Transaction): Promise<AbTest> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[input.type];
    await invoker.insert(abTestTable).values(input);
    return input;
  }
  async getOne(
    stageId: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTest | null> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[type];
    const res = await invoker
      .select()
      .from(abTestTable)
      .where(eq(abTestTable.stageId, stageId));
    if (!res[0]) {
      return null;
    }

    if (res[0].status !== "COMPLETED") {
      return AbTest({
        stageId: res[0].stageId,
        type: type,
        status: "CANCELLED",
      });
    } else {
      if (!res[0].winnerId) {
        throw new Error("WinnerId is null");
      }
      return AbTest({
        stageId: res[0].stageId,
        type: type,
        status: res[0].status,
        winnerId: res[0].winnerId,
      });
    }
  }

  async updateOne(input: AbTest, tx?: Transaction): Promise<AbTest> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[input.type];
    if (input.status === "COMPLETED") {
      await invoker
        .update(abTestTable)
        .set({
          status: input.status,
          winnerId: input.winnerId,
        })
        .where(eq(abTestTable.stageId, input.stageId));
    } else {
      await invoker
        .update(abTestTable)
        .set({
          status: input.status,
        })
        .where(eq(abTestTable.stageId, input.stageId));
    }
    return input;
  }

  async getOneWithRoundAndDays(
    stageId: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<{
    stageId: string;
    type: AbTest["type"];
    status: AbTest["status"];
    rounds: {
      id: string;
      abTestId: string;
      currentBestId: string;
      contenderId: string;
      roundIndex: number;
      status: AbTestRound["status"];
      winner?: "CURRENT_BEST" | "CONTENDER" | null;
      roundDays: {
        id: string;
        abTestRoundId: string;
        dayIndex: number;
        deploymentConfigId: string;
        status: AbTestRoundDay["status"];
        winner?: "CURRENT_BEST" | "CONTENDER" | null;
        currentBestResult?: number | null;
        contenderResult?: number | null;
      }[];
    }[];
  }> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[type];
    const abTestRoundTable = abTestRoundTableRegistry[type];
    const abTestRoundDayTable = abTestRoundDayRegistry[type];
    const res = await invoker
      .select({
        abTest: abTestTable,
        abTestRound: abTestRoundTable,
        abTestRoundDay: abTestRoundDayTable,
      })
      .from(abTestTable)
      .innerJoin(
        abTestRoundTable,
        eq(abTestTable.stageId, abTestRoundTable.abTestId),
      )
      .innerJoin(
        abTestRoundDayTable,
        eq(abTestRoundTable.id, abTestRoundDayTable.abTestRoundId),
      )
      .where(eq(abTestTable.stageId, stageId));

    console.log("RES", res);

    // Transform the flat results into nested structure
    const transformedResult = res.reduce(
      (acc, row) => {
        if (!acc.stageId) {
          // Initialize the base object with AB test properties
          acc.stageId = row.abTest.stageId;
          acc.type = type;
          acc.status = "CANCELLED";
          acc.rounds = [];
        }

        // Find or create round
        let round = acc.rounds.find((r) => r.id === row.abTestRound.id);
        if (!round) {
          round = {
            id: row.abTestRound.id,
            abTestId: row.abTestRound.abTestId,
            currentBestId: row.abTestRound.currentBestId,
            contenderId: row.abTestRound.contenderId,
            roundIndex: row.abTestRound.roundIndex,
            status: "CANCELLED",
            winner: row.abTestRound.winner,
            roundDays: [],
          };
          acc.rounds.push(round);
        }

        // Add round day
        round.roundDays.push({
          id: row.abTestRoundDay.id,
          abTestRoundId: row.abTestRoundDay.abTestRoundId,
          dayIndex: row.abTestRoundDay.dayIndex,
          deploymentConfigId: row.abTestRoundDay.deploymentConfigId,
          status: "CANCELLED",
          winner: "CONTENDER",
          currentBestResult: row.abTestRoundDay.currentBestResult,
          contenderResult: row.abTestRoundDay.contenderResult,
        });

        return acc;
      },
      {} as {
        stageId: string;
        type: AbTest["type"];
        status: AbTest["status"];
        rounds: {
          id: string;
          abTestId: string;
          currentBestId: string;
          contenderId: string;
          roundIndex: number;
          status: AbTestRound["status"];
          winner?: "CURRENT_BEST" | "CONTENDER" | null;
          roundDays: {
            id: string;
            abTestRoundId: string;
            dayIndex: number;
            deploymentConfigId: string;
            status: AbTestRoundDay["status"];
            winner?: "CURRENT_BEST" | "CONTENDER" | null;
            currentBestResult?: number | null;
            contenderResult?: number | null;
          }[];
        }[];
      },
    );

    return transformedResult;
  }

  async getByStageId(
    stageId: string,
    type: AbTest["type"],
    tx?: ITransaction,
  ): Promise<AbTest | null> {
    return this.getOne(stageId, type, tx as any);
  }

  async getOneWithRounds(
    stageId: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<{
    stageId: string;
    type: AbTest["type"];
    status: AbTest["status"];
    rounds: {
      id: string;
      abTestId: string;
      currentBestId: string;
      contenderId: string;
      roundIndex: number;
      status: AbTestRound["status"];
      winner?: "CURRENT_BEST" | "CONTENDER" | null;
    }[];
  }> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[type];
    const abTestRoundTable = abTestRoundTableRegistry[type];
    const abTestRoundDayTable = abTestRoundDayRegistry[type];
    const res = await invoker
      .select({
        abTest: abTestTable,
        abTestRound: abTestRoundTable,
      })
      .from(abTestTable)
      .innerJoin(
        abTestRoundTable,
        eq(abTestTable.stageId, abTestRoundTable.abTestId),
      )
      .where(eq(abTestTable.stageId, stageId));

    console.log("RES", res);

    // Transform the flat results into nested structure
    const transformedResult = res.reduce(
      (acc, row) => {
        if (!acc.stageId) {
          // Initialize the base object with AB test properties
          acc.stageId = row.abTest.stageId;
          acc.type = type;
          acc.status = "CANCELLED";
          acc.rounds = [];
        }

        // Find or create round
        let round = acc.rounds.find((r) => r.id === row.abTestRound.id);
        if (!round) {
          round = {
            id: row.abTestRound.id,
            abTestId: row.abTestRound.abTestId,
            currentBestId: row.abTestRound.currentBestId,
            contenderId: row.abTestRound.contenderId,
            roundIndex: row.abTestRound.roundIndex,
            status: "CANCELLED",
            winner: row.abTestRound.winner,
          };
          acc.rounds.push(round);
        }

        // Add round day

        return acc;
      },
      {} as {
        stageId: string;
        type: AbTest["type"];
        status: AbTest["status"];
        rounds: {
          id: string;
          abTestId: string;
          currentBestId: string;
          contenderId: string;
          roundIndex: number;
          status: AbTestRound["status"];
          winner?: "CURRENT_BEST" | "CONTENDER" | null;
        }[];
      },
    );

    return transformedResult;
  }
}
