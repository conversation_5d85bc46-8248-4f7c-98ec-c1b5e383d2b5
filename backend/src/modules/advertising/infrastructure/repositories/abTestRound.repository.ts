import { and, eq } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import {
  audienceTestRoundTable,
  conversationCallToActionTestRoundTable,
  conversationMessageCopyTestRoundTable,
  conversationSubjectTestRoundTable,
  creativeTestRoundTable,
  socialPostBodyCopyTestRoundTable,
  socialPostCallToActionTestRoundTable,
  valuePropTestRoundTable,
} from "../../../../database/schemas/advertising/abTests/abTestTables";
import { linkedInAdFormatTable } from "../../../../database/schemas/advertising/linkedInAdFormat.table";
import { linkedInAdProgramTable } from "../../../../database/schemas/advertising/linkedInAdProgram.table";
import { linkedInAdSegmentTable } from "../../../../database/schemas/advertising/linkedInAdSegment.table";
import { stageTable } from "../../../../database/schemas/advertising/stage.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IAbTestRoundRepository } from "../../application/interfaces/infrastructure/repositories/abTestRound.repository.interface";
import { AbTest } from "../../domain/entites/abTest";
import { AbTestRound } from "../../domain/entites/abTestRound";
import { AdProgram } from "../../domain/entites/adProgram";
import { AdSegment } from "../../domain/entites/adSegment";
import { Stage } from "../../domain/entites/stage";
import { abTestTableRegistry } from "./abTest.repository";

export const abTestRoundTableRegistry = {
  audience: audienceTestRoundTable,
  valueProp: valuePropTestRoundTable,
  creative: creativeTestRoundTable,
  conversationSubject: conversationSubjectTestRoundTable,
  socialPostBodyCopy: socialPostBodyCopyTestRoundTable,
  conversationMessageCopy: conversationMessageCopyTestRoundTable,
  conversationCallToAction: conversationCallToActionTestRoundTable,
  socialPostCallToAction: socialPostCallToActionTestRoundTable,
};

export class AbTestRoundRepository implements IAbTestRoundRepository {
  async getAdProgramAndAdSegmentForAbTestRound(
    id: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<{
    adProgram: AdProgram;
    adSegment: AdSegment;
  } | null> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestRoundTableRegistry[type];
    const abTestTable = abTestTableRegistry[type];
    const res = await invoker
      .select()
      .from(abTestRoundTable)
      .where(eq(abTestRoundTable.id, id))
      .innerJoin(
        abTestTable,
        eq(abTestTable.stageId, abTestRoundTable.abTestId),
      )
      .innerJoin(stageTable, eq(stageTable.id, abTestTable.stageId))
      .innerJoin(
        linkedInAdSegmentTable,
        eq(linkedInAdSegmentTable.id, stageTable.linkedInAdSegmentid),
      )
      .innerJoin(
        linkedInAdProgramTable,
        eq(
          linkedInAdProgramTable.id,
          linkedInAdSegmentTable.linkedInAdProgramId,
        ),
      )
      .innerJoin(
        linkedInAdFormatTable,
        eq(linkedInAdFormatTable.id, linkedInAdProgramTable.adFormatId),
      );
    if (!res[0]) {
      return null;
    }
    return {
      adProgram: AdProgram({
        id: res[0].linked_in_ad_program.id,
        title: res[0].linked_in_ad_program.title,
        linkedInAdAccountId: res[0].linked_in_ad_program.linkedInAdAccountId,
        startDatetime: res[0].linked_in_ad_program.startDatetime,
        endDatetime: res[0].linked_in_ad_program.endDatetime,
        totalBudget: res[0].linked_in_ad_program.totalBudget,
        monthlyBudget: res[0].linked_in_ad_program.monthlyBudget,
        objectiveType: res[0].linked_in_ad_program.objectiveType,
        adFormat: {
          type: res[0].linked_in_ad_format.type as any,
          format: res[0].linked_in_ad_format.format as any,
        },
        leadGenForm: res[0].linked_in_ad_program.leadGenFormUrn,
        destinationUrl: res[0].linked_in_ad_program.destinationUrl,
        status: res[0].linked_in_ad_program.status ?? "ARCHIVED_DRAFT",
        type: res[0].linked_in_ad_program.type ?? "EVENT_DRIVEN",
      }),
      adSegment: AdSegment({
        id: res[0].linked_in_ad_segment.id,
        linkedInAdProgramId: res[0].linked_in_ad_segment.linkedInAdProgramId,
        segmentId: res[0].linked_in_ad_segment.segmentId,
        ready: res[0].linked_in_ad_segment.ready,
      }),
    };
  }
  async getRoundsForAbTest(
    abTestId: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRound[]> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestRoundTableRegistry[type];
    const res = await invoker
      .select()
      .from(abTestRoundTable)
      .where(eq(abTestRoundTable.abTestId, abTestId));
    const rounds: AbTestRound[] = [];
    for (const round of res) {
      if (round.status === "COMPLETED") {
        if (!round.winner) {
          throw new Error("Winner is null when round is completed");
        }
        rounds.push(
          AbTestRound({
            id: round.id,
            abTestId: round.abTestId,
            status: round.status,
            roundIndex: round.roundIndex,
            currentBestId: round.currentBestId,
            contenderId: round.contenderId,
            winner: round.winner,
          }),
        );
      } else {
        rounds.push(
          AbTestRound({
            id: round.id,
            abTestId: round.abTestId,
            status: "FAILED",
            roundIndex: round.roundIndex,
            currentBestId: round.currentBestId,
            contenderId: round.contenderId,
          }),
        );
      }
    }
    return rounds;
  }
  async createMany(
    input: AbTestRound[],
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRound[]> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestRoundTableRegistry[type];
    await invoker.insert(abTestRoundTable).values(input);
    return input;
  }
  async createOne(
    input: AbTestRound,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRound> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestRoundTableRegistry[type];
    await invoker.insert(abTestRoundTable).values(input);
    return input;
  }

  async getOne(
    id: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRound | null> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestRoundTableRegistry[type];
    const res = await invoker
      .select()
      .from(abTestRoundTable)
      .where(eq(abTestRoundTable.id, id));
    if (!res[0]) {
      return null;
    }

    if (res[0].status === "COMPLETED") {
      if (!res[0].winner) {
        throw new Error("Winner is null");
      }
      return AbTestRound({
        id: res[0].id,
        abTestId: res[0].abTestId,
        status: res[0].status,
        roundIndex: res[0].roundIndex,
        currentBestId: res[0].currentBestId,
        contenderId: res[0].contenderId,
        winner: res[0].winner,
      });
    }
    return AbTestRound({
      id: res[0].id,
      abTestId: res[0].abTestId,
      status: "FAILED",
      roundIndex: res[0].roundIndex,
      currentBestId: res[0].currentBestId,
      contenderId: res[0].contenderId,
    });
  }

  async updateOne(
    input: AbTestRound,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRound> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestRoundTableRegistry[type];
    await invoker
      .update(abTestRoundTable)
      .set(input)
      .where(eq(abTestRoundTable.id, input.id));
    return input;
  }

  async updateCurrentBestForNonStartedRounds(
    abTestId: string,
    currentBestId: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestRoundTableRegistry[type];
    await invoker
      .update(abTestRoundTable)
      .set({ currentBestId })
      .where(
        and(
          eq(abTestRoundTable.abTestId, abTestId),
          eq(abTestRoundTable.status, "NOT_STARTED"),
        ),
      );
  }
}
