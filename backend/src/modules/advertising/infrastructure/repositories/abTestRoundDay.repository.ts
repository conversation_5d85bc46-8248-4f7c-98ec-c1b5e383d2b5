import { eq } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import {
  audienceTestRoundDayTable,
  conversationCallToActionTestRoundDayTable,
  conversationMessageCopyTestRoundDayTable,
  conversationSubjectTestRoundDayTable,
  creativeTestRoundDayTable,
  socialPostBodyCopyTestRoundDayTable,
  socialPostCallToActionTestRoundDayTable,
  valuePropTestRoundDayTable,
} from "../../../../database/schemas/advertising/abTests/abTestTables";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IAbTestRoundDayRepository } from "../../application/interfaces/infrastructure/repositories/abTestRoundDay.repository.interface";
import { AbTest } from "../../domain/entites/abTest";
import { AbTestRoundDay } from "../../domain/entites/abTestRoundDay";

export const abTestRoundDayRegistry = {
  audience: audienceTestRoundDayTable,
  valueProp: valuePropTestRoundDayTable,
  creative: creativeTestRoundDayTable,
  conversationSubject: conversationSubjectTestRoundDayTable,
  socialPostBodyCopy: socialPostBodyCopyTestRoundDayTable,
  conversationMessageCopy: conversationMessageCopyTestRoundDayTable,
  conversationCallToAction: conversationCallToActionTestRoundDayTable,
  socialPostCallToAction: socialPostCallToActionTestRoundDayTable,
};

export class AbTestRoundDayRepository implements IAbTestRoundDayRepository {
  async getForRound(
    abTestRoundId: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRoundDay[]> {
    const invoker = tx ?? db;
    const abTestRoundDayTable = abTestRoundDayRegistry[type];
    const res = await invoker
      .select()
      .from(abTestRoundDayTable)
      .where(eq(abTestRoundDayTable.abTestRoundId, abTestRoundId));

    const abTestRoundDays: AbTestRoundDay[] = [];

    for (const row of res) {
      if (row.status === "COMPLETED") {
        if (
          !row.winner ||
          row.currentBestResult == null ||
          row.contenderResult == null
        ) {
          throw new Error("Winner is required for completed ab test round day");
        }
        abTestRoundDays.push(
          AbTestRoundDay({
            id: row.id,
            status: row.status,
            winner: "CONTENDER",
            abTestRoundId: row.abTestRoundId,
            dayIndex: row.dayIndex,
            currentBestResult: row.currentBestResult,
            contenderResult: row.contenderResult,
            deploymentConfigId: row.deploymentConfigId,
          }),
        );
      } else {
        abTestRoundDays.push(
          AbTestRoundDay({
            id: row.id,
            status: "CANCELLED",
            abTestRoundId: row.abTestRoundId,
            dayIndex: row.dayIndex,
            deploymentConfigId: row.deploymentConfigId,
          }),
        );
      }
    }

    return abTestRoundDays;
  }
  async createOne(
    input: AbTestRoundDay,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRoundDay> {
    const invoker = tx ?? db;
    const abTestRoundDayTable = abTestRoundDayRegistry[type];
    await invoker.insert(abTestRoundDayTable).values(input);
    return input;
  }
  async updateOne(
    input: AbTestRoundDay,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRoundDay> {
    const invoker = tx ?? db;
    const abTestRoundDayTable = abTestRoundDayRegistry[type];
    await invoker
      .update(abTestRoundDayTable)
      .set(input)
      .where(eq(abTestRoundDayTable.id, input.id));
    return input;
  }
  async getOne(
    id: string,
    type: AbTest["type"],
    tx?: Transaction,
  ): Promise<AbTestRoundDay | null> {
    const invoker = tx ?? db;
    const abTestRoundDayTable = abTestRoundDayRegistry[type];
    const res = await invoker
      .select()
      .from(abTestRoundDayTable)
      .where(eq(abTestRoundDayTable.id, id));
    if (!res[0]) {
      return null;
    }
    console.log(res[0]);
    if (res[0].status === "COMPLETED") {
      if (
        !res[0].winner ||
        res[0].currentBestResult == null ||
        res[0].contenderResult == null
      ) {
        throw new Error("Winner is required for completed ab test round day");
      }
      return AbTestRoundDay({
        id: res[0].id,
        status: res[0].status,
        winner: "CONTENDER",
        abTestRoundId: res[0].abTestRoundId,
        dayIndex: res[0].dayIndex,
        currentBestResult: res[0].currentBestResult,
        contenderResult: res[0].contenderResult,
        deploymentConfigId: res[0].deploymentConfigId,
      });
    }
    return AbTestRoundDay({
      id: res[0].id,
      status: "CANCELLED",
      abTestRoundId: res[0].abTestRoundId,
      dayIndex: res[0].dayIndex,
      deploymentConfigId: res[0].deploymentConfigId,
    });
  }
}
