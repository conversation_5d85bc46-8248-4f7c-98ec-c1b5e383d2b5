import {
  and,
  count,
  desc,
  eq,
  gte,
  ilike,
  inArray,
  like,
  lte,
  sql,
} from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { linkedInAdAccountTable } from "../../../../database/schemas/advertising/linkedInAdAccount.table";
import { linkedInLeadFormTable } from "../../../../database/schemas/advertising/linkedInLeadForm.table";
import { linkedInLeadFormLeadTable } from "../../../../database/schemas/advertising/linkedInLeadFormLead.table";
import { contactTable } from "../../../../database/schemas/crm/contact.table";
import { opportunityTable } from "../../../../database/schemas/crm/opportunity.table";
import { ILinkedInLeadFormLeadRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInLeadFormLead.repository.interface";
import { LinkedInLeadFormAndLead } from "../../domain/entites/leadFormAndLead";
import { LinkedInLeadAndOpportunity } from "../../domain/entites/linkedInLeadAndOpportunity";
import { LinkedInLeadFormLead } from "../../domain/entites/linkedInLeadFormLead";

export class LinkedInLeadFormLeadRepository
  implements ILinkedInLeadFormLeadRepositoryInterface
{
  async createOne(
    input: LinkedInLeadFormLead,
    tx?: Transaction,
  ): Promise<string> {
    const queryDb = tx ?? db;
    const values = {
      id: input.id,
      linkedInLeadFormId: input.linkedInLeadFormId,
      linkedInLeadFormResponseId: input.linkedInLeadFormResponseId,
      linkedinCampaignUrn: input.linkedinCampaignUrn || null,
      linkedInAdAccountId: input.linkedInAdAccountId,
      firstName: input.firstName || null,
      lastName: input.lastName || null,
      phoneNumber: input.phoneNumber || null,
      email: input.email || null,
      city: input.city || null,
      state: input.state || null,
      country: input.country || null,
      zipCode: input.zipCode || null,
      companyName: input.companyName || null,
      jobTitle: input.jobTitle || null,
      jobFunction: input.jobFunction || null,
      industry: input.industry || null,
      companySize: input.companySize || null,
      seniority: input.seniority || null,
      degree: input.degree || null,
      fieldOfStudy: input.fieldOfStudy || null,
      school: input.school || null,
      startDate: input.startDate?.toISOString().split("T")[0] || null,
      graduationDate: input.graduationDate?.toISOString().split("T")[0] || null,
      gender: input.gender || null,
      workEmail: input.workEmail || null,
      linkedinProfileLink: input.linkedinProfileLink || null,
      workPhoneNumber: input.workPhoneNumber || null,
      leadCreatedAt: input.leadCreatedAt,
      leadType: input.leadType || null,
      testLead: input.testLead || false,
    };

    const res = await queryDb.insert(linkedInLeadFormLeadTable).values(values);

    if (res.rowCount === 0) {
      throw new Error("Failed to create LinkedIn Lead Form Lead");
    }

    return input.id;
  }

  async getAllByLeadFormId(
    leadFormId: string,
    tx?: Transaction,
  ): Promise<LinkedInLeadFormLead[]> {
    const queryDb = tx ?? db;

    const rows = await queryDb
      .select()
      .from(linkedInLeadFormLeadTable)
      .where(eq(linkedInLeadFormLeadTable.linkedInLeadFormId, leadFormId));

    return rows.map((row) => ({
      id: row.id,
      linkedInLeadFormId: row.linkedInLeadFormId,
      linkedInLeadFormResponseId: row.linkedInLeadFormResponseId,
      linkedinCampaignUrn: row.linkedinCampaignUrn || null,
      linkedInAdAccountId: row.linkedInAdAccountId,
      firstName: row.firstName || null,
      lastName: row.lastName || null,
      phoneNumber: row.phoneNumber || null,
      email: row.email || null,
      city: row.city || null,
      state: row.state || null,
      country: row.country || null,
      zipCode: row.zipCode || null,
      companyName: row.companyName || null,
      jobTitle: row.jobTitle || null,
      jobFunction: row.jobFunction || null,
      seniority: row.seniority || null,
      companySize: row.companySize || null,
      industry: row.industry || null,
      degree: row.degree || null,
      fieldOfStudy: row.fieldOfStudy || null,
      school: row.school || null,
      startDate: row.startDate ? new Date(row.startDate) : null,
      graduationDate: row.graduationDate ? new Date(row.graduationDate) : null,
      gender: row.gender || null,
      workEmail: row.workEmail || null,
      linkedinProfileLink: row.linkedinProfileLink || null,
      workPhoneNumber: row.workPhoneNumber || null,
      leadCreatedAt: row.leadCreatedAt,
      leadType: row.leadType || null,
      testLead: row.testLead || false,
    }));
  }

  async getOneByLinkedInLeadFormResponseId(
    linkedInLeadFormResponseId: string,
    tx?: Transaction,
  ): Promise<LinkedInLeadFormLead | null> {
    const queryDb = tx ?? db;

    const queryRow = await queryDb
      .select()
      .from(linkedInLeadFormLeadTable)
      .where(
        eq(
          linkedInLeadFormLeadTable.linkedInLeadFormResponseId,
          linkedInLeadFormResponseId,
        ),
      )
      .limit(1);

    if (!queryRow) return null;

    const row = queryRow[0];

    if (!row) return null;

    return {
      id: row.id,
      linkedInLeadFormId: row.linkedInLeadFormId,
      linkedInLeadFormResponseId: row.linkedInLeadFormResponseId,
      linkedinCampaignUrn: row.linkedinCampaignUrn || null,
      linkedInAdAccountId: row.linkedInAdAccountId,
      firstName: row.firstName || null,
      lastName: row.lastName || null,
      phoneNumber: row.phoneNumber || null,
      email: row.email || null,
      city: row.city || null,
      state: row.state || null,
      country: row.country || null,
      zipCode: row.zipCode || null,
      companyName: row.companyName || null,
      jobTitle: row.jobTitle || null,
      jobFunction: row.jobFunction || null,
      seniority: row.seniority || null,
      companySize: row.companySize || null,
      industry: row.industry || null,
      degree: row.degree || null,
      fieldOfStudy: row.fieldOfStudy || null,
      school: row.school || null,
      startDate: row.startDate ? new Date(row.startDate) : null,
      graduationDate: row.graduationDate ? new Date(row.graduationDate) : null,
      gender: row.gender || null,
      workEmail: row.workEmail || null,
      linkedinProfileLink: row.linkedinProfileLink || null,
      workPhoneNumber: row.workPhoneNumber || null,
      leadCreatedAt: row.leadCreatedAt,
      leadType: row.leadType || null,
      testLead: row.testLead || false,
    };
  }

  async getOneByNameAndLeadCreatedAt(
    firstName: string,
    lastName: string,
    leadCreatedAt: Date,
    tx?: Transaction,
  ): Promise<LinkedInLeadFormLead | null> {
    const queryDb = tx ?? db;

    const rows = await queryDb
      .select()
      .from(linkedInLeadFormLeadTable)
      .where(
        and(
          eq(linkedInLeadFormLeadTable.firstName, firstName),
          eq(linkedInLeadFormLeadTable.lastName, lastName),
          eq(linkedInLeadFormLeadTable.leadCreatedAt, leadCreatedAt),
        ),
      );

    if (!rows.length) return null;

    const row = rows[0];

    if (!row) return null;

    return {
      id: row.id,
      linkedInLeadFormId: row.linkedInLeadFormId,
      linkedInLeadFormResponseId: row.linkedInLeadFormResponseId,
      linkedinCampaignUrn: row.linkedinCampaignUrn || null,
      linkedInAdAccountId: row.linkedInAdAccountId,
      firstName: row.firstName || null,
      lastName: row.lastName || null,
      phoneNumber: row.phoneNumber || null,
      email: row.email || null,
      city: row.city || null,
      state: row.state || null,
      country: row.country || null,
      zipCode: row.zipCode || null,
      companyName: row.companyName || null,
      jobTitle: row.jobTitle || null,
      jobFunction: row.jobFunction || null,
      seniority: row.seniority || null,
      companySize: row.companySize || null,
      industry: row.industry || null,
      degree: row.degree || null,
      fieldOfStudy: row.fieldOfStudy || null,
      school: row.school || null,
      startDate: row.startDate ? new Date(row.startDate) : null,
      graduationDate: row.graduationDate ? new Date(row.graduationDate) : null,
      gender: row.gender || null,
      workEmail: row.workEmail || null,
      linkedinProfileLink: row.linkedinProfileLink || null,
      workPhoneNumber: row.workPhoneNumber || null,
      leadCreatedAt: row.leadCreatedAt,
      leadType: row.leadType || null,
      testLead: row.testLead || false,
    };
  }

  async getOneById(
    id: string,
    tx?: Transaction,
  ): Promise<LinkedInLeadFormLead | null> {
    const queryDb = tx ?? db;

    const rows = await queryDb
      .select()
      .from(linkedInLeadFormLeadTable)
      .where(eq(linkedInLeadFormLeadTable.id, id))
      .limit(1);

    if (!rows.length) return null;

    const row = rows[0];

    if (!row) return null;

    return {
      id: row.id,
      linkedInLeadFormId: row.linkedInLeadFormId,
      linkedInLeadFormResponseId: row.linkedInLeadFormResponseId,
      linkedinCampaignUrn: row.linkedinCampaignUrn || null,
      linkedInAdAccountId: row.linkedInAdAccountId,
      firstName: row.firstName || null,
      lastName: row.lastName || null,
      phoneNumber: row.phoneNumber || null,
      email: row.email || null,
      city: row.city || null,
      state: row.state || null,
      country: row.country || null,
      zipCode: row.zipCode || null,
      companyName: row.companyName || null,
      jobTitle: row.jobTitle || null,
      jobFunction: row.jobFunction || null,
      seniority: row.seniority || null,
      companySize: row.companySize || null,
      industry: row.industry || null,
      degree: row.degree || null,
      fieldOfStudy: row.fieldOfStudy || null,
      school: row.school || null,
      startDate: row.startDate ? new Date(row.startDate) : null,
      graduationDate: row.graduationDate ? new Date(row.graduationDate) : null,
      gender: row.gender || null,
      workEmail: row.workEmail || null,
      linkedinProfileLink: row.linkedinProfileLink || null,
      workPhoneNumber: row.workPhoneNumber || null,
      leadCreatedAt: row.leadCreatedAt,
      leadType: row.leadType || null,
      testLead: row.testLead || false,
    };
  }

  async getAllByAccountId(
    linkedInAccountId: string,
    tx?: Transaction,
  ): Promise<{}> {
    const queryDb = tx ?? db;

    const rows = await queryDb
      .select()
      .from(linkedInLeadFormLeadTable)
      .where(
        eq(linkedInLeadFormLeadTable.linkedInAdAccountId, linkedInAccountId),
      );

    return rows;
  }

  async getAllLeadsForKFormsByAccountId(
    linkedInAdAccountId: string,
    startDateFilter?: Date,
    endDateFilter?: Date,
    tx?: Transaction,
  ): Promise<LinkedInLeadFormAndLead[]> {
    const queryDb = tx ?? db;

    const linkedinAdAccountRow = await queryDb
      .select()
      .from(linkedInAdAccountTable)
      .where(eq(linkedInAdAccountTable.id, linkedInAdAccountId))
      .limit(1);

    if (!linkedinAdAccountRow) {
      throw new Error("LinkedIn Ad Account not found");
    }

    const leadFormIdMatch = eq(
      linkedInLeadFormTable.linkedInAdAccountId,
      linkedInAdAccountId,
    );
    const leadFormTableMatch = eq(
      linkedInLeadFormTable.id,
      linkedInLeadFormLeadTable.linkedInLeadFormId,
    );

    let filters;

    if (startDateFilter && endDateFilter) {
      filters = and(
        gte(linkedInLeadFormLeadTable.leadCreatedAt, startDateFilter),
        lte(linkedInLeadFormLeadTable.leadCreatedAt, endDateFilter),
        leadFormIdMatch,
        leadFormTableMatch,
      );
    } else {
      filters = and(leadFormIdMatch, leadFormTableMatch);
    }

    const leadColumns = {
      id: linkedInLeadFormLeadTable.id,
      linkedInLeadFormId: linkedInLeadFormLeadTable.linkedInLeadFormId,
      linkedinCampaignUrn: linkedInLeadFormLeadTable.linkedinCampaignUrn,
      linkedInAdAccountId: linkedInLeadFormLeadTable.linkedInAdAccountId,
      leadCreatedAt: linkedInLeadFormLeadTable.leadCreatedAt,
      firstName: linkedInLeadFormLeadTable.firstName,
      lastName: linkedInLeadFormLeadTable.lastName,
      email: linkedInLeadFormLeadTable.email,
      workEmail: linkedInLeadFormLeadTable.workEmail,
      companyName: linkedInLeadFormLeadTable.companyName,
      jobTitle: linkedInLeadFormLeadTable.jobTitle,
      jobFunction: linkedInLeadFormLeadTable.jobFunction,
      seniority: linkedInLeadFormLeadTable.seniority,
      linkedinProfileLink: linkedInLeadFormLeadTable.linkedinProfileLink,
    };

    const rows = await queryDb
      .select({
        leadFormLeads: leadColumns,
        leadForm: {
          id: linkedInLeadFormTable.id,
          name: linkedInLeadFormTable.name,
          linkedInAdAccountId: linkedInLeadFormTable.linkedInAdAccountId,
        },
      })
      .from(linkedInLeadFormLeadTable)
      .innerJoin(linkedInLeadFormTable, filters)
      .where(ilike(linkedInLeadFormTable.name, "K-%"))
      .orderBy(desc(linkedInLeadFormLeadTable.leadCreatedAt));

    return rows;
  }

  // TODO: Move this to a repository file with both opportunity and lead
  async getAllLeadsAndOpportunitiesByAccountId(
    linkedInAdAccountId: string,
    startDateFilter?: Date,
    endDateFilter?: Date,
    tx?: Transaction,
  ): Promise<LinkedInLeadAndOpportunity[]> {
    const queryDb = tx ?? db;

    // First, get the LinkedIn Ad Account to validate it exists
    const linkedInAdAccountRow = await queryDb
      .select()
      .from(linkedInAdAccountTable)
      .where(eq(linkedInAdAccountTable.id, linkedInAdAccountId))
      .limit(1);

    if (!linkedInAdAccountRow) {
      throw new Error("LinkedIn Ad Account not found");
    }

    const organizationId = linkedInAdAccountRow[0]?.organizationId;

    if (!organizationId) {
      throw new Error("Organization ID not found");
    }

    // Columns to select from query
    const leadColumns = {
      id: linkedInLeadFormLeadTable.id,
      linkedInLeadFormId: linkedInLeadFormLeadTable.linkedInLeadFormId,
      linkedinCampaignUrn: linkedInLeadFormLeadTable.linkedinCampaignUrn,
      linkedInAdAccountId: linkedInLeadFormLeadTable.linkedInAdAccountId,
      leadCreatedAt: linkedInLeadFormLeadTable.leadCreatedAt,
      firstName: linkedInLeadFormLeadTable.firstName,
      lastName: linkedInLeadFormLeadTable.lastName,
      email: linkedInLeadFormLeadTable.email,
      workEmail: linkedInLeadFormLeadTable.workEmail,
      companyName: linkedInLeadFormLeadTable.companyName,
      jobTitle: linkedInLeadFormLeadTable.jobTitle,
      jobFunction: linkedInLeadFormLeadTable.jobFunction,
      seniority: linkedInLeadFormLeadTable.seniority,
      linkedinProfileLink: linkedInLeadFormLeadTable.linkedinProfileLink,
    };

    const opportunityColumns = {
      id: opportunityTable.id,
      name: opportunityTable.name,
      amount: opportunityTable.annualContractValue,
      crmCreatedDate: opportunityTable.crmCreatedDate,
    };

    const contactColumns = {
      id: contactTable.id,
      email: contactTable.email,
      organizationId: contactTable.organizationId,
    };

    // Contact Table where statements
    const emailDomainMatch = eq(
      sql`SPLIT_PART(${linkedInLeadFormLeadTable.workEmail}, '@', 2)`,
      sql`SPLIT_PART(${contactTable.email}, '@', 2)`,
    );

    const organizationMatch = eq(contactTable.organizationId, organizationId);

    // Opportunity Table where statements
    const opportunityCreatedDateMatch = gte(
      opportunityTable.crmCreatedDate,
      linkedInLeadFormLeadTable.leadCreatedAt,
    );

    let opportunityDateFilters;
    if (startDateFilter && endDateFilter) {
      opportunityDateFilters = and(
        gte(opportunityTable.crmCreatedDate, startDateFilter),
        lte(opportunityTable.crmCreatedDate, endDateFilter),
      );
    } else {
      opportunityDateFilters = sql`1=1`;
    }
    const organizationIdMatch =
      organizationId !== undefined
        ? eq(contactTable.organizationId, organizationId)
        : sql`1=0`;
    const accountIdMatch = eq(
      opportunityTable.accountId,
      contactTable.accountId,
    );

    // Lead Form Table where statements
    const leadFormIdMatch = eq(
      linkedInLeadFormTable.linkedInAdAccountId,
      linkedInAdAccountId,
    );
    const leadFormTableMatch = eq(
      linkedInLeadFormTable.id,
      linkedInLeadFormLeadTable.linkedInLeadFormId,
    );

    const matchedOpportunities = await queryDb
      .selectDistinctOn([opportunityTable.id], {
        lead: leadColumns,
        opportunity: opportunityColumns,
        contact: contactColumns,
      })
      .from(linkedInLeadFormLeadTable)
      .innerJoin(contactTable, and(emailDomainMatch, organizationMatch))
      .innerJoin(
        opportunityTable,
        and(
          opportunityCreatedDateMatch,
          organizationIdMatch,
          accountIdMatch,
          opportunityDateFilters,
        ),
      )
      .innerJoin(
        linkedInLeadFormTable,
        and(leadFormIdMatch, leadFormTableMatch),
      )
      .where(ilike(linkedInLeadFormTable.name, "K-%"))
      .orderBy(
        opportunityTable.id,
        desc(linkedInLeadFormLeadTable.leadCreatedAt),
      );

    return matchedOpportunities;
  }

  async getLeadsAndPipelinesStatsByAccountId(
    linkedInAdAccountId: string,
    startDateFilter?: Date,
    endDateFilter?: Date,
    tx?: Transaction,
  ): Promise<{
    totalLeads: number;
    totalPipelineValue: number;
    totalPipelineInfluence: number;
  }> {
    const queryDb = tx ?? db;

    const linkedInAdAccountRow = await queryDb
      .select()
      .from(linkedInAdAccountTable)
      .where(eq(linkedInAdAccountTable.id, linkedInAdAccountId))
      .limit(1);

    if (!linkedInAdAccountRow) {
      throw new Error("LinkedIn Ad Account not found");
    }

    const organizationId = linkedInAdAccountRow[0]?.organizationId;

    const totalLeads = await queryDb
      .select({ count: count() })
      .from(linkedInLeadFormLeadTable)
      .innerJoin(
        linkedInLeadFormTable,
        and(
          eq(linkedInLeadFormTable.linkedInAdAccountId, linkedInAdAccountId),
          eq(
            linkedInLeadFormTable.id,
            linkedInLeadFormLeadTable.linkedInLeadFormId,
          ),
          startDateFilter && endDateFilter
            ? and(
                gte(linkedInLeadFormLeadTable.leadCreatedAt, startDateFilter),
                lte(linkedInLeadFormLeadTable.leadCreatedAt, endDateFilter),
              )
            : sql`1=1`,
        ),
      )
      .where(ilike(linkedInLeadFormTable.name, "K-%"));

    // Contact Table where statements
    const contactTableWhereStatements = and(
      organizationId !== undefined
        ? eq(contactTable.organizationId, organizationId)
        : sql`1=0`, // If organizationId is undefined, make the condition false
      eq(
        sql`SPLIT_PART(${linkedInLeadFormLeadTable.workEmail}, '@', 2)`,
        sql`SPLIT_PART(${contactTable.email}, '@', 2)`,
      ),
    );

    let opportunityWhereStatements;

    if (startDateFilter && endDateFilter) {
      opportunityWhereStatements = and(
        gte(
          opportunityTable.crmCreatedDate,
          linkedInLeadFormLeadTable.leadCreatedAt,
        ),
        gte(linkedInLeadFormLeadTable.leadCreatedAt, startDateFilter),
        lte(opportunityTable.crmCreatedDate, endDateFilter),
        organizationId !== undefined
          ? eq(opportunityTable.organizationId, organizationId)
          : sql`1=0`, // If organizationId is undefined, make the condition false
        eq(opportunityTable.accountId, contactTable.accountId),
      );
    } else {
      opportunityWhereStatements = and(
        gte(
          opportunityTable.crmCreatedDate,
          linkedInLeadFormLeadTable.leadCreatedAt,
        ),
        organizationId !== undefined
          ? eq(opportunityTable.organizationId, organizationId)
          : sql`1=0`, // If organizationId is undefined, make the condition false
        eq(opportunityTable.accountId, contactTable.accountId),
      );
    }

    const leadFormWhereStatements = and(
      eq(linkedInLeadFormTable.linkedInAdAccountId, linkedInAdAccountId),
      eq(
        linkedInLeadFormTable.id,
        linkedInLeadFormLeadTable.linkedInLeadFormId,
      ),
    );

    // don't need pipeline yet
    // const createdOpportunities = await queryDb
    //   .selectDistinctOn([opportunityTable.id], {
    //     opportunity: {
    //       id: opportunityTable.id,
    //     },
    //     value: opportunityTable.annualContractValue,
    //   })
    //   .from(linkedInLeadFormLeadTable)
    //   .innerJoin(contactTable, contactTableWhereStatements)
    //   .innerJoin(opportunityTable, opportunityWhereStatements)
    //   .innerJoin(linkedInLeadFormTable, leadFormWhereStatements)
    //   .where(ilike(linkedInLeadFormTable.name, "K-%"))
    //   .orderBy(opportunityTable.id);

    // const pipelineSum = createdOpportunities.reduce(
    //   (acc, curr) => acc + (curr.value || 0),
    //   0,
    // );

    // Influenced Opportunities
    // let iOpportunityWhereStatements;

    // if (startDateFilter && endDateFilter) {
    //   iOpportunityWhereStatements = and(
    //     gte(
    //       linkedInLeadFormLeadTable.leadCreatedAt,
    //       sql`${opportunityTable.crmCreatedDate} - INTERVAL '90 days'`,
    //     ),
    //     lte(
    //       linkedInLeadFormLeadTable.leadCreatedAt,
    //       opportunityTable.crmCreatedDate,
    //     ),
    //     gte(opportunityTable.crmCreatedDate, startDateFilter),
    //     lte(opportunityTable.crmCreatedDate, endDateFilter),
    //     organizationId !== undefined
    //       ? eq(opportunityTable.organizationId, organizationId)
    //       : sql`1=0`, // If organizationId is undefined, make the condition false
    //   );
    // } else {
    //   iOpportunityWhereStatements = and(
    //     gte(
    //       linkedInLeadFormLeadTable.leadCreatedAt,
    //       sql`${opportunityTable.crmCreatedDate} - INTERVAL '90 days'`,
    //     ),
    //     lte(
    //       linkedInLeadFormLeadTable.leadCreatedAt,
    //       opportunityTable.crmCreatedDate,
    //     ),
    //     organizationId !== undefined
    //       ? eq(opportunityTable.organizationId, organizationId)
    //       : sql`1=0`, // If organizationId is undefined, make the condition false
    //   );
    // }

    // const iLeadFormWhereStatements = and(
    //   eq(linkedInLeadFormTable.linkedInAdAccountId, linkedInAdAccountId),
    //   eq(
    //     linkedInLeadFormTable.id,
    //     linkedInLeadFormLeadTable.linkedInLeadFormId,
    //   ),
    // );

    // TODO: don't need influenced yet
    // const influencedOpportunities = await queryDb
    //   .selectDistinctOn([opportunityTable.id], {
    //     opportunity: {
    //       id: opportunityTable.id,
    //     },
    //     value: opportunityTable.annualContractValue,
    //   })
    //   .from(linkedInLeadFormLeadTable)
    //   .innerJoin(opportunityTable, iOpportunityWhereStatements)
    //   .innerJoin(linkedInLeadFormTable, iLeadFormWhereStatements)
    //   .where(ilike(linkedInLeadFormTable.name, "K-%"))
    //   .orderBy(opportunityTable.id);

    // const influencedSum = influencedOpportunities.reduce(
    //   (acc, curr) => acc + (curr.value || 0),
    //   0,
    // );

    return {
      totalLeads: totalLeads[0]?.count || 0,
      totalPipelineValue: 0,
      totalPipelineInfluence: 0,
    };
  }
  // Add to LinkedInLeadFormLeadRepository.ts
  async createMany(
    leads: LinkedInLeadFormLead[],
    tx?: Transaction,
  ): Promise<string[]> {
    if (leads.length === 0) return [];
    const queryDb = tx ?? db;
    console.log(
      `[LinkedInLeadFormLeadRepository] Batch inserting ${leads.length} leads`,
    );

    // Prepare values for bulk insert
    const insertData = leads.map((lead) => ({
      id: lead.id,
      linkedInLeadFormId: lead.linkedInLeadFormId,
      linkedInLeadFormResponseId: lead.linkedInLeadFormResponseId,
      linkedinCampaignUrn: lead.linkedinCampaignUrn,
      linkedInAdAccountId: lead.linkedInAdAccountId,
      firstName: lead.firstName,
      lastName: lead.lastName,
      email: lead.email,
      phoneNumber: lead.phoneNumber,
      city: lead.city,
      state: lead.state,
      country: lead.country,
      zipCode: lead.zipCode,
      companyName: lead.companyName,
      jobTitle: lead.jobTitle,
      jobFunction: lead.jobFunction,
      industry: lead.industry,
      seniority: lead.seniority,
      companySize: lead.companySize,
      degree: lead.degree,
      fieldOfStudy: lead.fieldOfStudy,
      school: lead.school,
      startDate: lead.startDate?.toISOString().split("T")[0] || null,
      graduationDate: lead.graduationDate?.toISOString().split("T")[0] || null,
      gender: lead.gender,
      workEmail: lead.workEmail,
      linkedinProfileLink: lead.linkedinProfileLink,
      workPhoneNumber: lead.workPhoneNumber,
      leadType: lead.leadType,
      testLead: lead.testLead,
      leadCreatedAt: lead.leadCreatedAt,
    }));

    // Perform bulk insert
    const result = await queryDb
      .insert(linkedInLeadFormLeadTable)
      .values(insertData);

    console.log(
      `[LinkedInLeadFormLeadRepository] Successfully inserted ${leads.length} leads`,
    );

    // Return the IDs of inserted leads
    return leads.map((lead) => lead.id);
  }

  // Add a method to check existing leads by response IDs
  async getExistingLeadsByResponseIds(
    responseIds: string[],
  ): Promise<{ linkedInLeadFormResponseId: string; id: string }[]> {
    if (responseIds.length === 0) return [];

    console.log(
      `[LinkedInLeadFormLeadRepository] Checking existence of ${responseIds.length} lead response IDs`,
    );

    const result = await db
      .select({
        id: linkedInLeadFormLeadTable.id,
        linkedInLeadFormResponseId:
          linkedInLeadFormLeadTable.linkedInLeadFormResponseId,
      })
      .from(linkedInLeadFormLeadTable)
      .where(
        inArray(
          linkedInLeadFormLeadTable.linkedInLeadFormResponseId,
          responseIds,
        ),
      );

    console.log(
      `[LinkedInLeadFormLeadRepository] Found ${result.length} existing leads`,
    );

    return result;
  }
}
