import { start } from "repl";
import { Readable } from "stream";

import type { LinkedInApiClient } from "@kalos/linkedin-api";

import type { ILinkedInService } from "../../application/interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";
import {
  GetLinkedInApiAdAccountRequestDto,
  GetLinkedInApiAdAccountResponseDto,
  getLinkedInApiAdAccountResponseDtoSchema,
} from "../../application/dtos/serviceDtos/thirdPartyApi/linkedInApi/adAccount/getLinkedInApiAdAccount.dto";
import {
  GetLinkedInApiAdAccountIdsResponseDto,
  getLinkedInApiAdAccountIdsResponseDtoSchema,
} from "../../application/dtos/serviceDtos/thirdPartyApi/linkedInApi/adAccount/getLinkedInApiAdAccountIds.dto";
import {
  GetLinkedInApiAdAccountsResponseDto,
  getLinkedInApiAdAccountsResponseDtoSchema,
} from "../../application/dtos/serviceDtos/thirdPartyApi/linkedInApi/adAccount/getLinkedInApiAdAccounts.dto";
import {
  GetLinkedInApiLeadGenFormResponseByIdResponseDto,
  GetLinkedInApiLeadGenFormResponsesForAccountRequestDto,
  GetLinkedInApiLeadGenFormResponsesForAccountResponseDto,
} from "../../application/dtos/serviceDtos/thirdPartyApi/linkedInApi/leadGenForm/getLinkedInApiLeadGenFormResponsesForAccount.dto";
import {
  GetLinkedInApiLeadGenFormByUrnRequestDto,
  GetLinkedInApiLeadGenFormByUrnResponseDto,
  GetLinkedInApiLeadGenFormsForAccountRequestDto,
  GetLinkedInApiLeadGenFormsForAccountResponseDto,
} from "../../application/dtos/serviceDtos/thirdPartyApi/linkedInApi/leadGenForm/getLinkedInApiLeadGenFormsForAccount.dto";
import { LinkedInAudienceTargetCriteria } from "../../domain/valueObjects/linkedinAudienceTargeting/linkedinTargetAudienceCriteria";
import {
  idToLinkedInUrn,
  linkedInUrnToId,
  linkedInVersionedUrnToId,
} from "../../utils/linkedInUrnUtils";
import { addExcludedEmployersToTargeting } from "../../utils/addExcludedEmployers";

// Module-level interfaces
export interface Exclude {
  or: Or;
}

export interface TargetingCriteria {
  include: Include;
  exclude?: Exclude;
}

export interface Include {
  and: And[];
}

export interface And {
  or: Or;
}

export type Or = Record<string, string[]>;

export class LinkedInService implements ILinkedInService {
  constructor(private readonly linkedInClient: LinkedInApiClient) { }

  private async getLaunchingOrganization(adAccountUrn: string): Promise<{
    organizationUrn: string;
    organizationName: string;
  } | undefined> {
    const adAccount = await this.getLinkedInAdAccount({ adAccountUrn });

    if (adAccount?.organizationUrn && adAccount?.adAccountName) {
      const launchingOrganization = {
        organizationUrn: adAccount.organizationUrn,
        organizationName: adAccount.adAccountName
      };
      console.log(`🎯 Launching organization: ${adAccount.adAccountName} (${adAccount.organizationUrn})`);
      return launchingOrganization;
    } else {
      console.log('⚠️ No organization URN found for this ad account');
      return undefined;
    }
  }

  async createLeadGenConversation(data: {
    adAccountUrn: string;
    leadGenFormUrn: string;
    senderUrn: string;
    campaignUrn: string;
    body: string;
    subject: string;
    leadGenButtonText: string;
  }): Promise<string> {
    const res = await this.linkedInClient.createLeadGenConversation(data);
    return res;
  }

  async createDestinationUrlConversation(data: {
    adAccountUrn: string;
    senderUrn: string;
    campaignUrn: string;
    body: string;
    subject: string;
    leadGenButtonText: string;
    destinationUrl: string;
  }): Promise<string> {
    const res =
      await this.linkedInClient.createDestinationUrlConversation(data);
    return res;
  }
  async getSenders(adAccountUrn: string): Promise<
    {
      urn: string;
      firstName: string;
      lastName: string;
    }[]
  > {
    const adAccountId = linkedInUrnToId(adAccountUrn);
    const apiRes = await this.linkedInClient.getSenders(adAccountId);
    return apiRes;
  }

  async uploadImage(input: {
    linkedInOrganizationUrn: string;
    body: Readable;
    type: "image" | "video";
    fileSizeBytes: number;
  }): Promise<string> {
    const linkedInOrganizationId = linkedInUrnToId(
      input.linkedInOrganizationUrn,
    );
    const imageUrn = await this.linkedInClient.uploadImage({
      linkedInOrganizationId: linkedInOrganizationId,
      body: input.body,
      type: input.type,
      fileSizeBytes: input.fileSizeBytes,
    });
    return imageUrn;
  }

  async uploadDocument(input: {
    linkedInOrganizationUrn: string;
    body: Readable;
    type: "document";
    fileSizeBytes: number;
  }): Promise<string | null> {
    const linkedInOrganizationId = linkedInUrnToId(
      input.linkedInOrganizationUrn,
    );
    const documentUrn = await this.linkedInClient.uploadDocument({
      linkedInOrganizationId: linkedInOrganizationId,
      body: input.body,
      type: input.type,
      fileSizeBytes: input.fileSizeBytes,
    });
    return documentUrn;
  }

  async createCampaignGroup(input: {
    adAccountUrn: string;
    name: string;
    startDate: Date;
    endDate?: Date;
    status: "ACTIVE" | "DRAFT";
    objectiveType:
    | "BRAND_AWARENESS"
    | "ENGAGEMENT"
    | "JOB_APPLICANTS"
    | "LEAD_GENERATION"
    | "WEBSITE_CONVERSIONS"
    | "WEBSITE_VISITS"
    | "VIDEO_VIEWS"
    | null;
  }): Promise<string> {
    const adAccountId = parseInt(linkedInUrnToId(input.adAccountUrn));
    if (isNaN(adAccountId)) {
      throw `Invalid ad account ID: ${adAccountId}`;
    }

    const apiRes = await this.linkedInClient.createCampaignGroup({
      adAccount: adAccountId.toString(),
      name: input.name,
      startDate: input.startDate,
      endDate: input.endDate,
      status: input.status,
      objectiveType: input.objectiveType,
    });

    return apiRes;
  }

  async createCampaign(input: {
    adAccountUrn: string;
    campaignGroupUrn: string;
    budgetType: "DAILY" | "TOTAL";
    budget: number;
    name: string;
    startDate: Date;
    endDate?: Date;
    audienceTargets: LinkedInAudienceTargetCriteria;
    unitCost: number;
    manualBidding?: boolean;
    currencyCode?: string;
    objectiveType:
    | "BRAND_AWARENESS"
    | "ENGAGEMENT"
    | "LEAD_GENERATION"
    | "WEBSITE_CONVERSIONS"
    | "WEBSITE_VISITS"
    | "VIDEO_VIEWS";
    type?: "SPONSORED_UPDATES" | "SPONSORED_INMAILS";
  }): Promise<string> {
    const adAccountId = parseInt(linkedInUrnToId(input.adAccountUrn));
    if (isNaN(adAccountId)) {
      throw `Invalid ad account ID: ${adAccountId}`;
    }

    const launchingOrganization = await this.getLaunchingOrganization(input.adAccountUrn);

    const audienceTargetsWithExclusions = addExcludedEmployersToTargeting(
      input.audienceTargets,
      launchingOrganization
    );

    interface TargetingCriteria {
      include: Include;
      exclude?: Exclude;
    }

    // Initialize targeting criteria with the correct structure
    const targetingCriteria: TargetingCriteria = {
      include: {
        and: [],
      },
      exclude: !audienceTargetsWithExclusions.exclude
        ? undefined
        : {
          or: {},
        },
    };

    for (const each of audienceTargetsWithExclusions.include.and) {
      const or: Or = {};
      for (const eachFacet of each.or) {
        or[eachFacet.facetUrn] = eachFacet.facetEntites.map(
          (eachEntity) => eachEntity.entityUrn,
        );
      }
      targetingCriteria.include.and.push({
        or: or,
      });
    }

    targetingCriteria.include.and.push({
      or: {
        "urn:li:adTargetingFacet:interfaceLocales": ["urn:li:locale:en_US"],
      },
    });

    if (audienceTargetsWithExclusions.exclude && targetingCriteria.exclude) {
      for (const each of audienceTargetsWithExclusions.exclude.or) {
        targetingCriteria.exclude.or[each.facetUrn] = each.facetEntites.map(
          (eachEntity) => eachEntity.entityUrn,
        );
      }
    }

    const campaignGroupId = linkedInUrnToId(input.campaignGroupUrn);

    const campaignId = await this.linkedInClient.createCampaign({
      adAccount: adAccountId.toString(),
      campaignGroupUrn: campaignGroupId.toString(),
      budgetType: input.budgetType,
      budget: input.budget,
      name: input.name,
      startDate: input.startDate,
      endDate: input.endDate,
      audienceTargets: targetingCriteria,
      unitCost: input.unitCost,
      manualBidding: input.manualBidding,
      currencyCode: "USD",
      objectiveType: input.objectiveType,
      type: input.type,
    });

    const campaignIdNumber = parseInt(campaignId);
    if (isNaN(campaignIdNumber)) {
      throw `Invalid campaign ID: ${campaignId}`;
    }

    return idToLinkedInUrn(campaignIdNumber, "sponsoredCampaign");
  }

  async updateCampaign(input: {
    adAccountUrn: string;
    campaignUrn: string;
    audienceTargets: LinkedInAudienceTargetCriteria;
  }): Promise<unknown> {
    const adAccountId = linkedInUrnToId(input.adAccountUrn);
    const campaignId = linkedInUrnToId(input.campaignUrn);

    const launchingOrganization = await this.getLaunchingOrganization(input.adAccountUrn);

    const audienceTargetsWithExclusions = addExcludedEmployersToTargeting(
      input.audienceTargets,
      launchingOrganization
    );

    interface TargetingCriteria {
      include: Include;
      exclude?: Exclude;
    }
    // Initialize targeting criteria with the correct structure
    const targetingCriteria: TargetingCriteria = {
      include: {
        and: [],
      },
      exclude: !audienceTargetsWithExclusions.exclude
        ? undefined
        : {
          or: {},
        },
    };

    // Process include criteria
    for (const each of audienceTargetsWithExclusions.include.and) {
      const or: Or = {};
      for (const eachFacet of each.or) {
        or[eachFacet.facetUrn] = eachFacet.facetEntites.map(
          (eachEntity) => eachEntity.entityUrn,
        );
      }
      targetingCriteria.include.and.push({
        or: or,
      });
    }

    // Add default locale targeting
    targetingCriteria.include.and.push({
      or: {
        "urn:li:adTargetingFacet:interfaceLocales": ["urn:li:locale:en_US"],
      },
    });

    // Process exclude criteria if it exists
    if (audienceTargetsWithExclusions.exclude && targetingCriteria.exclude) {
      for (const each of audienceTargetsWithExclusions.exclude.or) {
        targetingCriteria.exclude.or[each.facetUrn] = each.facetEntites.map(
          (eachEntity) => eachEntity.entityUrn,
        );
      }
    }

    // Call LinkedIn API with converted targeting criteria
    const res = await this.linkedInClient.updateCampaign({
      adAccountId: adAccountId,
      campaignId: campaignId,
      audienceTargets: targetingCriteria,
    });

    if (!res) {
      throw new Error("Failed to update campaign");
    }

    return res;
  }
  async getLinkedInAdAccount(
    input: GetLinkedInApiAdAccountRequestDto,
  ): Promise<GetLinkedInApiAdAccountResponseDto | null> {
    // Convert URN to ID
    const adAccountId = linkedInUrnToId(input.adAccountUrn);

    // Get ad account from LinkedIn API
    const apiRes = await this.linkedInClient.getAdAccount(adAccountId);

    if (!apiRes.reference.includes("organization")) {
      return null;
    }

    const dto: GetLinkedInApiAdAccountResponseDto = {
      adAccountUrn: input.adAccountUrn,
      organizationUrn: apiRes.reference,
      adAccountName: apiRes.name,
    };

    return getLinkedInApiAdAccountResponseDtoSchema.parse(dto);
  }

  async getLinkedInAdAccountIdsForUser(): Promise<GetLinkedInApiAdAccountIdsResponseDto> {
    // Get ad accounts from LinkedIn API
    const apiRes = await this.linkedInClient.getAdAccountsForUser();

    const dto: GetLinkedInApiAdAccountIdsResponseDto = apiRes.map(
      (each) => each.account,
    );

    return getLinkedInApiAdAccountIdsResponseDtoSchema.parse(dto);
  }

  async getLinkedInAccountsForUser(): Promise<GetLinkedInApiAdAccountsResponseDto> {
    // Get ad account ids for user
    const adAccountIds = await this.getLinkedInAdAccountIdsForUser();
    console.log("adAccountIds", adAccountIds);

    // Get ad account data for user
    const adAccountData = await Promise.all(
      adAccountIds.map((adAccountId) =>
        this.getLinkedInAdAccount({ adAccountUrn: adAccountId }),
      ),
    );

    // Parse ad account data for user
    const dto: GetLinkedInApiAdAccountsResponseDto = [];
    for (const each of adAccountData) {
      if (each) {
        dto.push(each);
      }
    }
    return getLinkedInApiAdAccountsResponseDtoSchema.parse(dto);
  }

  async getLeadGenForms(
    input: GetLinkedInApiLeadGenFormsForAccountRequestDto,
  ): Promise<GetLinkedInApiLeadGenFormsForAccountResponseDto> {
    const adAccountIdAsString = linkedInUrnToId(input.adAccountUrn);
    const adAccountId = parseInt(adAccountIdAsString);
    if (isNaN(adAccountId)) {
      throw `Invalid ad account ID: ${adAccountIdAsString}`;
    }
    const apiRes = await this.linkedInClient.getLeadForms(adAccountId);

    const dto: GetLinkedInApiLeadGenFormsForAccountResponseDto =
      apiRes.elements.map((each) => ({
        ...each,
        leadGenFormUrn: idToLinkedInUrn(each.id, "leadGenForm"),
      }));
    return dto;
  }

  async getLeadFormByUrn(
    input: GetLinkedInApiLeadGenFormByUrnRequestDto,
  ): Promise<GetLinkedInApiLeadGenFormByUrnResponseDto> {
    const formIdAndVersion = linkedInVersionedUrnToId(input.formUrn);
    const formId = formIdAndVersion.id;
    if (!formId) {
      throw `Invalid form ID: ${formIdAndVersion.id}`;
    }
    const apiRes = await this.linkedInClient.getLeadFormById(formId);

    return apiRes;
  }

  async getLeadFormResponseById(input: {
    formLeadResponseId: string;
  }): Promise<GetLinkedInApiLeadGenFormResponseByIdResponseDto> {
    const apiRes = await this.linkedInClient.getFormLeadResponseById(
      input.formLeadResponseId,
    );

    return apiRes;
  }

  async getLeadFormResponses(
    input: GetLinkedInApiLeadGenFormResponsesForAccountRequestDto,
  ): Promise<GetLinkedInApiLeadGenFormResponsesForAccountResponseDto> {
    const adAccountIdAsString = linkedInUrnToId(input.adAccountUrn);

    const adAccountId = parseInt(adAccountIdAsString);
    if (isNaN(adAccountId)) {
      throw `Invalid ad account ID: ${adAccountIdAsString}`;
    }

    const apiRes = await this.linkedInClient.getLeadFormResponses(
      adAccountId.toString(),
      input.versionedLeadGenFormUrn,
    );

    return apiRes;
  }

  async createInlineCreative(data: {
    adAccountUrn: string;
    campaignUrn: string;
    imageUrn: string;
    commentary: string;
    headline: string;
    linkedInOrgId: string;
    destinationUrl?: string;
    adFormUrn?: string;
    adName: string;
  }): Promise<string> {
    const adAccountId = linkedInUrnToId(data.adAccountUrn);

    const campaignId = linkedInUrnToId(data.campaignUrn);

    const linkedInOrgId = linkedInUrnToId(data.linkedInOrgId);

    const adFormUrn = data.adFormUrn
      ? linkedInUrnToId(data.adFormUrn)
      : undefined;

    let apiRes;
    let attempts = 0;
    const maxAttempts = 3;
    const timeout = 10000; // 10 seconds in milliseconds

    while (attempts < maxAttempts) {
      try {
        apiRes = await this.linkedInClient.createInlineCreative({
          adAccount: adAccountId,
          campaign: campaignId,
          imageUrn: data.imageUrn,
          commentary: data.commentary,
          headline: data.headline,
          linkedInOrgId: linkedInOrgId.toString(),
          destinationUrl: data.destinationUrl,
          adFormUrn: adFormUrn,
          adName: data.adName,
        });
        return apiRes.value.creative;
      } catch (error) {
        attempts++;
        if (attempts >= maxAttempts) {
          throw new Error(
            `Failed to create inline creative after ${maxAttempts} attempts: ${error}`,
          );
        }
        // Wait for timeout before retrying
        await new Promise((resolve) => setTimeout(resolve, timeout));
        console.log("Trying to recreate inline creative");
      }
    }
    throw new Error("Failed to create inline creative");
  }

  async getAdTargetingFacets() {
    const apiRes = await this.linkedInClient.getFacets();
    return apiRes;
  }

  async getAdTargetingFacetEntitesByTypehead(input: {
    facetUrn: string;
    query: string;
  }) {
    const apiRes = await this.linkedInClient.getEntitesViaTypeahead(
      input.facetUrn,
      input.query,
    );
    return apiRes;
  }

  async getAdTargetingFacetEntitiesForFacet(input: { facetUrn: string }) {
    const apiRes = await this.linkedInClient.getEntitesViaAdTargetingFacet(
      input.facetUrn,
    );
    return apiRes;
  }

  async getLeadNotifications(input: {
    adAccountUrn: string;
  }): Promise<{ results: {} }> {
    const apiRes = await this.linkedInClient.getLeadNotifications(
      input.adAccountUrn,
    );

    return apiRes as { results: {} };
  }

  async getAdSegments(input: {
    adAccountUrn: string;
    types: "BULK" | "RETARGETING" | "MARKET_AUTOMATION";
    start: number;
    count: number;
  }): Promise<{
    paging: { start: number; count: number; total: number };
    elements: {
      approximateMemberCount: number;
      created: { actor: string; time: number };
      name: string;
      versionTag: string;
      lastModified: { actor: string; time: number };
      id: number;
      type: "BULK" | "RETARGETING" | "MARKET_AUTOMATION";
      account: string;
      status:
      | "BUILDING"
      | "UPDATING"
      | "READY"
      | "FAILED"
      | "ARCHIVED"
      | "EXPIRED";
      description?: string;
    }[];
  }> {
    const adAccountId = linkedInUrnToId(input.adAccountUrn);
    const apiRes = await this.linkedInClient.getSegments(
      adAccountId.toString(),
      input,
    );
    return apiRes;
  }

  async getSponsoredCreativeStatus(input: {
    adAccountUrn: string;
    adUrn: string;
  }): Promise<{
    review: { status: string };
    intendedStatus: "ACTIVE" | "PAUSED" | "DRAFT";
  }> {
    const adAccountId = linkedInUrnToId(input.adAccountUrn);
    const apiRes = await this.linkedInClient.getAdCreative(
      adAccountId.toString(),
      input.adUrn,
    );
    return apiRes;
  }

  async updateCampaignStatus(input: {
    adAccountUrn: string;
    campaignUrn: string;
    status: "ACTIVE" | "PAUSED";
  }) {
    const adAccountId = linkedInUrnToId(input.adAccountUrn);
    const campaignId = linkedInUrnToId(input.campaignUrn);
    await this.linkedInClient.updateCampaignStatus(
      adAccountId.toString(),
      campaignId.toString(),
      input.status,
    );
  }

  async updateSponsoredCreativeStatus(input: {
    adAccountUrn: string;
    adUrn: string;
    status: "ACTIVE" | "PAUSED";
  }) {
    const adAccountId = linkedInUrnToId(input.adAccountUrn);
    await this.linkedInClient.updateAdStatus(
      adAccountId.toString(),
      input.adUrn,
      input.status,
    );
  }

  async getCampaignGroupAnalytics(
    campaignGroupUrns: string[],
    startDate?: Date,
    endDate?: Date,
  ) {
    let newStartDate: Date;
    let newEndDate: Date;

    if (!startDate) {
      newStartDate = new Date();
      newStartDate.setFullYear(newStartDate.getFullYear() - 3);
    } else {
      newStartDate = startDate;
    }

    if (!endDate) {
      newEndDate = new Date();
      // Analyitics are more accurate 1 day behind
      newEndDate.setHours(newEndDate.getHours());
    } else {
      newEndDate = endDate;
    }

    const res = await this.linkedInClient.getCampaignGroupAnalytics(
      campaignGroupUrns,
      newStartDate,
      newEndDate,
    );

    return res;
  }

  async getCampaignGroups(input: {
    linkedInAdAccountId: string;
    campaignGroupUrns: string[];
  }) {
    const res = await this.linkedInClient.getCampaignGroups(
      input.linkedInAdAccountId,
      input.campaignGroupUrns,
    );

    return res as {
      elements: {
        runSchedule: {
          end: number;
          start: number;
        };
        changeAuditStamps: {
          created: {
            time: number;
          };
          lastModified: {
            time: number;
          };
        };
        name: string;
        test: boolean;
        servingStatuses: string[];
        allowedCampaignTypes: string[];
        backfilled: boolean;
        id: number;
        account: string;
        status:
        | "ACTIVE"
        | "ARCHIVED"
        | "CANCELLED"
        | "DRAFT"
        | "PAUSED"
        | "PENDING_DELETION"
        | "REMOVED";
      }[];
    };
  }

  async getAnalyticsForCreatives(input: {
    sponsoredCreativeUrns: string[];
    startDate: Date;
    endDate?: Date;
    timeGranularity: "ALL" | "DAILY" | "MONTHLY" | "YEARLY";
  }): Promise<
    {
      sponsoredCreatieUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      actionClicks: number;
      oneClickLeadFormOpens: number;
      landingPageClicks: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      totalEngagements: number;
    }[]
  > {
    const creativeIds = input.sponsoredCreativeUrns.map((each) =>
      linkedInUrnToId(each),
    );

    const analytics: {
      elements: Awaited<
        ReturnType<LinkedInApiClient["getAnalyticsForCreatives"]>
      >["elements"];
    } = { elements: [] };

    for (let i = 0; i < creativeIds.length; i += 10) {
      const batch = creativeIds.slice(i, i + 10);
      const batchAnalytics = await this.linkedInClient.getAnalyticsForCreatives(
        batch,
        input.startDate,
        input.endDate,
        input.timeGranularity,
      );
      analytics.elements.push(...batchAnalytics.elements);
    }

    const res: {
      sponsoredCreatieUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      actionClicks: number;
      totalEngagements: number;
      oneClickLeadFormOpens: number;
      landingPageClicks: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
    }[] = [];

    for (const each of analytics.elements) {
      const creativeUrn = each.pivotValues[0];
      if (!creativeUrn) {
        throw new Error("Creative URN not found");
      }
      res.push({
        sponsoredCreatieUrn: creativeUrn,
        clicks: each.clicks,
        impressions: each.impressions,
        actionClicks: each.actionClicks,
        oneClickLeadFormOpens: each.oneClickLeadFormOpens,
        costInUsd: Number(each.costInUsd),
        oneClickLeads: each.oneClickLeads,
        videoCompletions: each.videoCompletions,
        videoFirstQuartileCompletions: each.videoFirstQuartileCompletions,
        videoMidpointCompletions: each.videoMidpointCompletions,
        videoThirdQuartileCompletions: each.videoThirdQuartileCompletions,
        videoStarts: each.videoStarts,
        totalEngagements: each.totalEngagements,
        landingPageClicks: each.landingPageClicks,
        externalWebsiteConversions: each.externalWebsiteConversions,
        videoViews: each.videoViews,
        sends: each.sends,
        opens: each.opens,
      });
    }
    for (const each of input.sponsoredCreativeUrns) {
      const foundInRes = res.find((f) => f.sponsoredCreatieUrn === each);
      if (!foundInRes) {
        res.push({
          sponsoredCreatieUrn: each,
          clicks: 0,
          impressions: 0,
          costInUsd: 0,
          actionClicks: 0,
          oneClickLeadFormOpens: 0,
          totalEngagements: 0,
          oneClickLeads: 0,
          externalWebsiteConversions: 0,
          landingPageClicks: 0,
          videoCompletions: 0,
          videoFirstQuartileCompletions: 0,
          videoMidpointCompletions: 0,
          videoThirdQuartileCompletions: 0,
          videoViews: 0,
          videoStarts: 0,
          sends: 0,
          opens: 0,
        });
      }
    }
    return res;
  }

  async getAnalyticsForCampaigns(input: {
    campaignUrns: string[];
    startDate: Date;
    endDate?: Date;
    timeGranularity: "ALL" | "DAILY" | "MONTHLY" | "YEARLY";
  }) {
    const campaignIds = input.campaignUrns.map((each) => linkedInUrnToId(each));

    const analytics: {
      elements: Awaited<
        ReturnType<LinkedInApiClient["getCampaignAnalytics"]>
      >["elements"];
    } = { elements: [] };

    for (let i = 0; i < campaignIds.length; i += 10) {
      const batch = campaignIds.slice(i, i + 10);
      const batchAnalytics = await this.linkedInClient.getCampaignAnalytics(
        batch,
        input.startDate,
        input.endDate,
        input.timeGranularity,
      );
      console.log("batchAnalytics", batchAnalytics);
      analytics.elements.push(...batchAnalytics.elements);
    }

    const res: {
      campaignUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      actionClicks: number;
      totalEngagements: number;
      oneClickLeadFormOpens: number;
      landingPageClicks: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
    }[] = [];

    for (const each of analytics.elements) {
      const campaignUrn = each.pivotValues[0];
      if (!campaignUrn) {
        throw new Error("Campaign URN not found");
      }
      console.log("RES FROM LINKEDIN", res);
      res.push({
        campaignUrn,
        clicks: each.clicks,
        impressions: each.impressions,
        costInUsd: Number(each.costInUsd),
        oneClickLeads: each.oneClickLeads,
        externalWebsiteConversions: each.externalWebsiteConversions,
        videoViews: each.videoViews,
        sends: each.sends,
        opens: each.opens,
        actionClicks: each.actionClicks,
        totalEngagements: each.totalEngagements,
        oneClickLeadFormOpens: each.oneClickLeadFormOpens,
        landingPageClicks: each.landingPageClicks,
        videoCompletions: each.videoCompletions,
        videoFirstQuartileCompletions: each.videoFirstQuartileCompletions,
        videoMidpointCompletions: each.videoMidpointCompletions,
        videoThirdQuartileCompletions: each.videoThirdQuartileCompletions,
        videoStarts: each.videoStarts,
      });
    }

    console.log("INPUT", input.campaignUrns);
    console.log(
      "RES FROM LINKEDIN BEFORE ACCOUNTING FOR ONES WITH NO RES",
      res,
    );
    for (const each of input.campaignUrns) {
      const foundInRes = res.find((f) => f.campaignUrn === each);
      console.log("FOUND IN RES", foundInRes, each);
      if (!foundInRes) {
        res.push({
          campaignUrn: each,
          clicks: 0,
          impressions: 0,
          costInUsd: 0,
          oneClickLeads: 0,
          externalWebsiteConversions: 0,
          videoViews: 0,
          sends: 0,
          opens: 0,
          actionClicks: 0,
          totalEngagements: 0,
          oneClickLeadFormOpens: 0,
          landingPageClicks: 0,
          videoCompletions: 0,
          videoFirstQuartileCompletions: 0,
          videoMidpointCompletions: 0,
          videoThirdQuartileCompletions: 0,
          videoStarts: 0,
        });
      }
    }
    console.log("RES FROM LINKEDIN", res);
    return res;
  }

  async getAudienceCount(input: {
    audienceTargets: LinkedInAudienceTargetCriteria;
    adAccountUrn?: string;
  }): Promise<number> {

    let launchingOrganization;
    if (input.adAccountUrn) {
      launchingOrganization = await this.getLaunchingOrganization(input.adAccountUrn);
    }

    const audienceTargetsWithExclusions = addExcludedEmployersToTargeting(
      input.audienceTargets,
      launchingOrganization
    );

    interface Exclude {
      or: Or;
    }

    interface TargetingCriteria {
      include: Include;
      exclude?: Exclude;
    }

    interface Include {
      and: And[];
    }
    interface And {
      or: Or;
    }
    type Or = Record<string, string[]>;
    const targetingCriteria: TargetingCriteria = {
      include: {
        and: [],
      },
      exclude: !audienceTargetsWithExclusions.exclude
        ? undefined
        : {
          or: {},
        },
    };

    for (const each of audienceTargetsWithExclusions.include.and) {
      const or: Or = {};
      for (const eachFacet of each.or) {
        or[eachFacet.facetUrn] = eachFacet.facetEntites.map(
          (eachEntity) => eachEntity.entityUrn,
        );
      }
      targetingCriteria.include.and.push({
        or: or,
      });
    }

    targetingCriteria.include.and.push({
      or: {
        "urn:li:adTargetingFacet:interfaceLocales": ["urn:li:locale:en_US"],
      },
    });

    if (audienceTargetsWithExclusions.exclude && targetingCriteria.exclude) {
      for (const each of audienceTargetsWithExclusions.exclude.or) {
        targetingCriteria.exclude.or[each.facetUrn] = each.facetEntites.map(
          (eachEntity) => eachEntity.entityUrn,
        );
      }
    }

    const res = await this.linkedInClient.getAudienceCount(targetingCriteria);
    return res;
  }

  async getSuggestedBidding(input: {
    adAccountUrn: string;
    bidType: "CPM" | "CPC" | "CPV";
    objectiveType: "VIDEO_VIEW" | "WEBSITE_VISIT" | "LEAD_GENERATION";
    campaignType: "TEXT_AD" | "SPONSORED_UPDATES" | "SPONSORED_INMAILS";
    audienceTargets:
    | {
      mode: "kalos";
      targets: LinkedInAudienceTargetCriteria;
    }
    | {
      mode: "linkedIn";
      targetingCriteria: TargetingCriteria;
    };
    optimizationTargetType?: string;
    dailyBudget?: number;
    currencyCode?: string;
  }) {
    const adAccountId = linkedInUrnToId(input.adAccountUrn);

    let targetingCriteria: TargetingCriteria;
    if (input.audienceTargets.mode === "kalos") {

      const launchingOrganization = await this.getLaunchingOrganization(input.adAccountUrn);

      const audienceTargetsWithExclusions = addExcludedEmployersToTargeting(
        input.audienceTargets.targets,
        launchingOrganization
      );

      targetingCriteria = {
        include: {
          and: [],
        },
        exclude: !audienceTargetsWithExclusions.exclude
          ? undefined
          : {
            or: {},
          },
      };

      for (const each of audienceTargetsWithExclusions.include.and) {
        const or: Or = {};
        for (const eachFacet of each.or) {
          or[eachFacet.facetUrn] = eachFacet.facetEntites.map(
            (eachEntity) => eachEntity.entityUrn,
          );
        }
        targetingCriteria.include.and.push({
          or: or,
        });
      }

      targetingCriteria.include.and.push({
        or: {
          "urn:li:adTargetingFacet:interfaceLocales": ["urn:li:locale:en_US"],
        },
      });

      if (audienceTargetsWithExclusions.exclude && targetingCriteria.exclude) {
        for (const each of audienceTargetsWithExclusions.exclude.or) {
          targetingCriteria.exclude.or[each.facetUrn] = each.facetEntites.map(
            (eachEntity) => eachEntity.entityUrn,
          );
        }
      }
    } else {
      targetingCriteria = input.audienceTargets.targetingCriteria;
    }

    const res = await this.linkedInClient.getSuggestedBidding({
      adAccountUrn: adAccountId.toString(),
      bidType: input.bidType,
      objectiveType: input.objectiveType,
      campaignType: input.campaignType,
      targetingCriteria: targetingCriteria,
      optimizationTargetType: input.optimizationTargetType,
      dailyBudget: input.dailyBudget,
      currency: input.currencyCode,
    });
    const firstElement = res.elements[0];
    if (!firstElement) {
      throw new Error("No suggested bidding found");
    }
    const maxBid = Number(firstElement.bidLimits.max.amount);
    const minBid = Number(firstElement.bidLimits.min.amount);
    const maxSuggestedBid = Number(firstElement.suggestedBid.max.amount);
    const minSuggestedBid = Number(firstElement.suggestedBid.min.amount);
    const suggestedBid = Number(firstElement.suggestedBid.default.amount);
    const dailyBudgetMax = Number(firstElement.dailyBudgetLimits.max.amount);
    const dailyBudgetMin = Number(firstElement.dailyBudgetLimits.min.amount);
    const dailyBudgetSuggested = Number(
      firstElement.dailyBudgetLimits.default.amount,
    );

    return {
      maxBid,
      minBid,
      maxSuggestedBid,
      minSuggestedBid,
      suggestedBid,
      dailyBudgetMax,
      dailyBudgetMin,
      dailyBudgetSuggested,
    };
  }

  async getCampaign(input: { adAccountUrn: string; campaignUrn: string }) {
    const campaignId = linkedInUrnToId(input.campaignUrn);
    const adAccountId = linkedInUrnToId(input.adAccountUrn);
    const res = await this.linkedInClient.getCampaign(
      Number(adAccountId),
      Number(campaignId),
    );
    return res;
  }

  async updateCampaignBid(input: {
    adAccountUrn: string;
    campaignUrn: string;
    bid: number;
  }) {
    const campaignId = linkedInUrnToId(input.campaignUrn);
    const adAccountId = linkedInUrnToId(input.adAccountUrn);
    await this.linkedInClient.setCampaignUnitCost(
      Number(adAccountId),
      campaignId.toString(),
      input.bid,
    );
  }

  async associateConversationWithCampaign(
    conversationUrn: string,
    campaignUrn: string,
  ) {
    await this.linkedInClient.associateConversationWithCampaign(
      conversationUrn,
      campaignUrn,
    );
  }

  async getConversionsForAdAccount(adAccountUrn: string) {
    const res =
      await this.linkedInClient.getConversionsForAdAccount(adAccountUrn);
    return res;
  }

  async getSpendForCampaignGroups(input: {
    campaignGroupUrns: string[];
    startDate: Date;
    endDate?: Date;
  }): Promise<
    {
      campaignGroupUrn: string;
      costInUsd: number;
    }[]
  > {
    const res = await this.linkedInClient.getCampaignGroupAnalytics(
      input.campaignGroupUrns,
      input.startDate,
      input.endDate ?? new Date(),
    );
    const arr: {
      campaignGroupUrn: string;
      costInUsd: number;
    }[] = [];
    for (const each of res.elements) {
      if (!each.pivotValues[0]) {
        throw new Error("Campaign group URN not found");
      }
      arr.push({
        campaignGroupUrn: each.pivotValues[0],
        costInUsd: Number(each.costInUsd),
      });
    }
    return arr;
  }
}
