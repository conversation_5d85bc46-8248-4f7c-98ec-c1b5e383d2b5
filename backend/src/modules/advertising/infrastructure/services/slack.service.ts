import { SlackNotification } from "../../domain/entites/slackNotification";

interface SlackBlock {
  type: string;
  text?: {
    type: string;
    text: string;
  };
  fields?: Array<{
    type: string;
    text: string;
  }>;
  elements?: Array<{
    type: string;
    text: string;
  }>;
}

interface SlackMessage {
  blocks: SlackBlock[];
}

interface SlackResponse {
  ok: boolean;
  error?: string;
  ts?: string;
}

export class SlackApiService {
  async sendMessage(webhookUrl: string, message: SlackMessage): Promise<SlackResponse> {
    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { ok: false, error: `HTTP ${response.status}: ${errorText}` };
      }

      const responseData = await response.text();
      return { ok: true, ts: responseData };
    } catch (error) {
      return { ok: false, error: `Network error: ${error}` };
    }
  }

  formatPerformanceMessage(notification: SlackNotification): SlackMessage {
    // Determine the period type based on date range
    const daysDiff = Math.ceil((notification.weekEndDate.getTime() - notification.weekStartDate.getTime()) / (1000 * 60 * 60 * 24));
    const hoursDiff = Math.ceil((notification.weekEndDate.getTime() - notification.weekStartDate.getTime()) / (1000 * 60 * 60));
    
    let periodType = "Performance Report";
    let summaryTitle = "Summary";
    let dateRangeText = "";
    
    if (hoursDiff <= 1) {
      periodType = "Hourly Performance Report";
      summaryTitle = "Hourly Summary";
      // Show hour-level granularity for hourly reports
      const startDateTime = notification.weekStartDate.toLocaleString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
      const endDateTime = notification.weekEndDate.toLocaleString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric',
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
      dateRangeText = `*${startDateTime} - ${endDateTime}* (${hoursDiff} hour${hoursDiff !== 1 ? 's' : ''})`;
    } else if (daysDiff <= 1) {
      periodType = "Daily Performance Report";
      summaryTitle = "Daily Summary";
      const startDate = notification.weekStartDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const endDate = notification.weekEndDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      dateRangeText = `*${startDate} - ${endDate}*`;
    } else if (daysDiff <= 7) {
      periodType = "Weekly Performance Report";
      summaryTitle = "Weekly Summary";
      const startDate = notification.weekStartDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const endDate = notification.weekEndDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      dateRangeText = `*${startDate} - ${endDate}*`;
    } else if (daysDiff <= 14) {
      periodType = "Bi-weekly Performance Report";
      summaryTitle = "Bi-weekly Summary";
      const startDate = notification.weekStartDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const endDate = notification.weekEndDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      dateRangeText = `*${startDate} - ${endDate}*`;
    } else if (daysDiff >= 28) {
      periodType = "Monthly Performance Report";
      summaryTitle = "Monthly Summary";
      const startDate = notification.weekStartDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const endDate = notification.weekEndDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      dateRangeText = `*${startDate} - ${endDate}*`;
    }
    
    const blocks: SlackBlock[] = [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `📊 ${periodType}`
        }
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: dateRangeText
        }
      },
      {
        type: "divider"
      }
    ];

    // Summary Section - SIMPLIFIED (removed impressions, clicks, CTR)
    blocks.push({
      type: "section",
      text: {
        type: "mrkdwn",
        text: `*📈 ${summaryTitle}*`
      }
    });

    // Create a more compact summary layout with only core metrics
    const summaryFields = [];
    
    // First row: Spend and Leads
    summaryFields.push({
      type: "mrkdwn",
      text: `*Spend:* $${notification.totalMetrics.totalSpend.toFixed(2)}`
    });
    
    if (notification.totalMetrics.totalLeads) {
      summaryFields.push({
        type: "mrkdwn",
        text: `*Leads:* ${notification.totalMetrics.totalLeads.toLocaleString()}`
      });
    }
    
    // Second row: Only Engagements (removed clicks and impressions)
    if (notification.totalMetrics.totalEngagements) {
      summaryFields.push({
        type: "mrkdwn",
        text: `*Engagements:* ${notification.totalMetrics.totalEngagements.toLocaleString()}`
      });
    }

    blocks.push({
      type: "section",
      fields: summaryFields
    });

    // Campaign Details Section
    if (notification.campaignGroupMetrics.length > 0) {
      blocks.push(
        {
          type: "divider"
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*🎪 Campaign Performance* (${notification.campaignGroupMetrics.length} active campaigns)`
          }
        }
      );

      // Add each campaign as a clean section
      notification.campaignGroupMetrics.forEach((campaign, index) => {
        // Truncate campaign name if too long
        const maxNameLength = 100;
        const campaignName = campaign.campaignGroupName.length > maxNameLength 
          ? `${campaign.campaignGroupName.substring(0, maxNameLength)}...`
          : campaign.campaignGroupName;

        // Build metrics array - SIMPLIFIED (removed clicks and impressions)
        const metricsDisplay: string[] = [];
        if (campaign.leads) metricsDisplay.push(`${campaign.leads} leads`);
        if (campaign.engagements) metricsDisplay.push(`${campaign.engagements.toLocaleString()} engagements`);

        // Create performance summary
        let performanceText = `*${campaignName}*\n`;
        
        if (metricsDisplay.length > 0) {
          performanceText += `${metricsDisplay.join(' • ')}\n`;
        }

        // Add spend
        performanceText += `Spend: $${campaign.spend.toFixed(2)}`;

        // Add learning insights if available (now includes test-based insights)
        if (campaign.learnings && campaign.learnings !== "Performance tracking ongoing") {
          performanceText += `\n*Learnings:* ${campaign.learnings}`;
        }

        blocks.push({
          type: "section",
          text: {
            type: "mrkdwn",
            text: performanceText
          }
        });

        // Add subtle separator between campaigns (except for the last one)
        if (index < notification.campaignGroupMetrics.length - 1) {
          blocks.push({
            type: "context",
            elements: [
              {
                type: "mrkdwn",
                text: "─────────"
              }
            ]
          });
        }
      });
    }

    // Footer
    blocks.push(
      {
        type: "divider"
      },
      {
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: "📅 Generated automatically • Questions? Contact the Kalos team"
          }
        ]
      }
    );

    return { blocks };
  }

  formatAdHocMessage(title: string, content: string): SlackMessage {
    return {
      blocks: [
        {
          type: "header", 
          text: {
            type: "plain_text",
            text: title
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: content
          }
        }
      ]
    };
  }
} 