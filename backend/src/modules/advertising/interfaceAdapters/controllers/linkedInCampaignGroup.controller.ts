import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { organizationRoute } from "../../../../trpc/trpc";
import { getFilteredCampaignGroupsDtoSchema } from "../../application/dtos/controllerDtos/linkedInCampaignGroup/getFilteredCampaignGroups.dto";
import {
  getCampaignGroupAnalyticsDtoSchema,
  getOneCampaignGroupAnalyticsDtoSchema,
} from "../../application/dtos/controllerDtos/linkedInCampaignGroup/linkedinCampaignGroup.dto";
import { GetCampaignGroupBatchAnalytics } from "../../application/useCase/linkedInCampaignGroup/getCampaignGroupBatchAnalytics.useCase";
import { GetOneCampaignGroupAnalytics } from "../../application/useCase/linkedInCampaignGroup/getCampainGroupAnalytics.useCase";
import { GetFilteredCampaignGroups } from "../../application/useCase/linkedInCampaignGroup/getFilteredCampaignGroups.useCase";
import { LinkedInCampaignGroupService } from "../../domain/services/linkedInCampaignGroup.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";

export const linkedInCampaignGroupController = {
  getAnalyticsForBatchCampaignGroups: organizationRoute
    .input(getCampaignGroupAnalyticsDtoSchema)
    .query(async ({ input, ctx }) => {
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error(
          "[linkedInCampaignGroupController] Linkedin Client not found",
        );
      }

      const linkedInService = new LinkedInService(linkedInClient);

      const useCase = new GetCampaignGroupBatchAnalytics({
        linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
        linkedinCampaignGroupRepository: new LinkedInCampaignGroupRepository(),
        linkedInService: linkedInService,
      });

      const res = useCase.execute(input);

      return res;
    }),

  getAnalyticsForOneCampaignGroup: organizationRoute
    .input(getOneCampaignGroupAnalyticsDtoSchema)
    .query(async ({ input, ctx }) => {
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error(
          "[linkedInCampaignGroupController] Linkedin Client not found",
        );
      }
      const linkedInService = new LinkedInService(linkedInClient);
      const useCase = new GetOneCampaignGroupAnalytics({
        linkedinCampaignGroupRepository: new LinkedInCampaignGroupRepository(),
        linkedInService: linkedInService,
      });
      const res = await useCase.execute(input);
      return res;
    }),

  getFilteredCampaigns: organizationRoute
    .input(getFilteredCampaignGroupsDtoSchema)
    .query(async ({ input, ctx }) => {
      const linkedinCampaignGroupRepository =
        new LinkedInCampaignGroupRepository();
      const linkedInCampaignGroupService = new LinkedInCampaignGroupService(
        linkedinCampaignGroupRepository,
      );

      const useCase = new GetFilteredCampaignGroups({
        linkedInCampaignGroupService: linkedInCampaignGroupService,
      });

      const res = await useCase.execute({
        adAccountId: input.adAccountId,
        status: input.statuses,
      });

      return res;
    }),
};
