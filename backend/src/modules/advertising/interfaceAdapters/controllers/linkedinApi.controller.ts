import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  getLinkedInApiClientFromOrganizationId,
  getLinkedInFromUserId,
} from "../../../../../../packages/linkedInApi/src/linkedInClient";
import { organizationRoute } from "../../../../trpc/trpc";
import { getAdTargetingFacetEntitesByTypeheadRequestDto } from "../../application/dtos/controllerDtos/linkedInApi/getAdTargetingFacetEntitesByTypehead.dto";
import { getAdTargetingFacetEntitiesForFacetRequestDto } from "../../application/dtos/controllerDtos/linkedInApi/getAdTargetingFacetEntitiesForFacet.dto";
import { getAudienceCountsDtoRequestSchema } from "../../application/dtos/controllerDtos/linkedInApi/getAudienceCounts.dto";
import { getAudienceSegmentsFromLinkedInApiRequestDto } from "../../application/dtos/controllerDtos/linkedInApi/getAudienceSegmentsFromLinekdInApi.dto";
import { getLeadFormResponsesFromLinkedInApiRequestDtoSchema } from "../../application/dtos/controllerDtos/linkedInApi/getLeadGenFormResponsesFromLinkedInApi.dto";
import { getLeadGenFormsFromLinkedInApiRequestDtoSchema } from "../../application/dtos/controllerDtos/linkedInApi/getLeadGenFormsFromLinkedInApi.dto";
import { GetAdTargetingFacetEntitesByTypeheadUseCase } from "../../application/useCase/linkedInApi/getAdTargetingFacetEntitesByTypehead.useCase";
import { GetAdTargetingFacetEntitiesForFacetUseCase } from "../../application/useCase/linkedInApi/getAdTargetingFacetEntitiesForFacet.useCase";
import { GetAdTargetingFacetsUseCase } from "../../application/useCase/linkedInApi/getAdTargetingFacets.useCase";
import { GetAudienceCountUseCase } from "../../application/useCase/linkedInApi/getAudieneCount.useCase";
import { getLeadFormResponsesFromLinkedInApiUseCase } from "../../application/useCase/linkedInApi/getLeadFormResponsesFromLinkedInApi.useCase";
import { getLeadGenFormsFromLinkedInApiUseCase } from "../../application/useCase/linkedInApi/getLeadGenFormsFromLinkedInApiUseCase";
import { getLeadNotificationsFromLinkedInApiUseCase } from "../../application/useCase/linkedInApi/getLeadNotificationsFromLinkedInApi.useCase";
import { GetLinkedInApiAudienceSegmentsUseCase } from "../../application/useCase/linkedInApi/getLinkedInApiAudienceSegments.useCase";
import { AdAudienceService } from "../../domain/services/adAudience.service";
import { LinkedInAdAccountService } from "../../domain/services/linkedInAdAccount.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";

export const linkedInApiController = {
  getSenders: organizationRoute.query(async ({ ctx }) => {
    const linkedInClient = await getLinkedInFromUserId(ctx.userId);
    if (!linkedInClient) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Failed to get LinkedIn authentication",
      });
    }

    const client = await getLinkedInFromUserId(ctx.userId);
    if (!client) {
      throw new Error("No client");
    }

    const adAccountRepository = new LinkedInAdAccountRepository();
    const adAccounts = await adAccountRepository.getForOrganization(
      ctx.organizationId,
    );
    const adAccount = adAccounts[0];
    if (!adAccount) {
      throw new Error("No ad account");
    }
    const res = await client.getSenders(adAccount.linkedInAdAccountUrn);
    return res;
  }),
  leadGenForms: {
    getForAccount: organizationRoute
      .input(getLeadGenFormsFromLinkedInApiRequestDtoSchema)
      .query(async ({ input, ctx }) => {
        const linkedInClient = await getLinkedInFromUserId(ctx.userId);
        if (!linkedInClient) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Failed to get LinkedIn authentication",
          });
        }
        const linkedInService = new LinkedInService(linkedInClient);

        const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
        const linkedInAdAccountService = new LinkedInAdAccountService(
          linkedInAdAccountRepository,
        );
        return await getLeadGenFormsFromLinkedInApiUseCase(input, {
          linkedInService,
          linkedInAdAccountService,
          userId: ctx.userId,
          organizationId: ctx.organizationId,
        });
      }),
  },
  leadNotifications: {
    getForAccount: organizationRoute
      .input(
        z.object({
          adAccountId: z.string(),
        }),
      )
      .query(async ({ input, ctx }) => {
        const linkedInClient = await getLinkedInFromUserId(ctx.userId);
        if (!linkedInClient) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Failed to get LinkedIn authentication",
          });
        }
        const linkedInService = new LinkedInService(linkedInClient);
        const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
        const linkedInAdAccountService = new LinkedInAdAccountService(
          linkedInAdAccountRepository,
        );
        return await getLeadNotificationsFromLinkedInApiUseCase(input, {
          linkedInService,
          linkedInAdAccountService,
          userId: ctx.userId,
          organizationId: ctx.organizationId,
        });
      }),
  },
  leadFormResponses: {
    getForAccount: organizationRoute
      .input(getLeadFormResponsesFromLinkedInApiRequestDtoSchema)
      .query(async ({ input, ctx }) => {
        const linkedInClient = await getLinkedInFromUserId(ctx.userId);
        if (!linkedInClient) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Failed to get LinkedIn authentication",
          });
        }

        const linkedInService = new LinkedInService(linkedInClient);
        const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
        const linkedInAdAccountService = new LinkedInAdAccountService(
          linkedInAdAccountRepository,
        );

        return await getLeadFormResponsesFromLinkedInApiUseCase(input, {
          linkedInService,
          linkedInAdAccountService,
          userId: ctx.userId,
          organizationId: ctx.organizationId,
        });
      }),
  },

  adTargeting: {
    getAdTargetingFacets: organizationRoute.query(async ({ ctx }) => {
      const linkedInClient = await getLinkedInFromUserId(ctx.userId);
      if (!linkedInClient) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Failed to get LinkedIn authentication",
        });
      }
      const linkedInService = new LinkedInService(linkedInClient);
      const getAdTargetingFacetsUseCase = new GetAdTargetingFacetsUseCase(
        linkedInService,
      );
      return await getAdTargetingFacetsUseCase.execute();
    }),
    getAdTargetingFacetEntitiesForFacet: organizationRoute
      .input(getAdTargetingFacetEntitiesForFacetRequestDto)
      .query(async ({ input, ctx }) => {
        const linkedInClient = await getLinkedInFromUserId(ctx.userId);
        if (!linkedInClient) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Failed to get LinkedIn authentication",
          });
        }
        const linkedInService = new LinkedInService(linkedInClient);
        const getAdTargetingFacetEntitiesForFacetUseCase =
          new GetAdTargetingFacetEntitiesForFacetUseCase(linkedInService);
        return await getAdTargetingFacetEntitiesForFacetUseCase.execute(input);
      }),
    getAdTargetingFacetEntitiesByTypehead: organizationRoute
      .input(getAdTargetingFacetEntitesByTypeheadRequestDto)
      .query(async ({ input, ctx }) => {
        const linkedInClient = await getLinkedInFromUserId(ctx.userId);
        if (!linkedInClient) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Failed to get LinkedIn authentication",
          });
        }
        const linkedInService = new LinkedInService(linkedInClient);
        const getAdTargetingFacetEntitesByTypeheadUseCase =
          new GetAdTargetingFacetEntitesByTypeheadUseCase(linkedInService);
        return await getAdTargetingFacetEntitesByTypeheadUseCase.execute(input);
      }),
    getAudienceSegments: organizationRoute
      .input(getAudienceSegmentsFromLinkedInApiRequestDto)
      .query(async ({ input, ctx }) => {
        const linkedInClient = await getLinkedInFromUserId(ctx.userId);
        if (!linkedInClient) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Failed to get LinkedIn authentication",
          });
        }
        const linkedInService = new LinkedInService(linkedInClient);
        const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
        const linkedInAdAccountService = new LinkedInAdAccountService(
          linkedInAdAccountRepository,
        );
        const getLinkedInApiAudienceSegmentsUseCase =
          new GetLinkedInApiAudienceSegmentsUseCase(
            linkedInService,
            linkedInAdAccountService,
            ctx.organizationId,
          );
        return await getLinkedInApiAudienceSegmentsUseCase.execute(input);
      }),
  },
  getAudienceCount: organizationRoute
    .input(getAudienceCountsDtoRequestSchema)
    .query(async ({ input, ctx }) => {
      const linkedInClient = await getLinkedInFromUserId(ctx.userId);
      if (!linkedInClient) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Failed to get LinkedIn authentication",
        });
      }

      const linkedInService = new LinkedInService(linkedInClient);

      const adAccountRepository = new LinkedInAdAccountRepository();
      const adAccounts = await adAccountRepository.getForOrganization(
        ctx.organizationId,
      );
      const adAccount = adAccounts[0];
      const adAccountUrn = adAccount?.linkedInAdAccountUrn;

      const getAudienceCountUseCase = new GetAudienceCountUseCase(
        linkedInService,
      );

      return await getAudienceCountUseCase.execute({
        ...input,
        adAccountUrn,
      });
    }),
};
