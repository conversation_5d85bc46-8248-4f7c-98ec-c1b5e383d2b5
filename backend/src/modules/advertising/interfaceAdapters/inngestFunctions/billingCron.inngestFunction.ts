import { Organization } from "../../../core/domain/entites/organization";
import { OrganizationRepository } from "../../../core/infrastructure/repositories/organization.repository";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

export const billingCron = advertisingInngestClient.createFunction(
  { id: "billing-cron" },
  [{ event: "billing/all-customers" }, { cron: "0 0 1 * *" }],
  async ({ step, event }) => {
    const batchSize = 50;
    let offset = 0;
    let organizations: Organization[] = [];
    do {
      organizations = await step.run("get-organizations", async () => {
        const organizationRepository = new OrganizationRepository();
        return await organizationRepository.getAll({
          offset: offset,
          limit: batchSize,
        });
      });
      await step.run("send-batches", async () => {
        await advertisingInngestClient.send({
          name: "billing/process-ad-spend-batch",
          data: {
            organizations: organizations
              .filter(
                (organization) =>
                  organization.stripeCustomerId !== undefined &&
                  organization.stripeCustomerId !== null,
              )
              .map((organization) => ({
                organizationId: organization.organizationId,
                stripeCustomerId: organization.stripeCustomerId,
              })),
          },
        });
      });
      offset += batchSize;
    } while (organizations.length === batchSize);
  },
);
