import crypto from "crypto";
import { Inngest } from "inngest";

import {
  getLinkedInApiClientFromOrganizationId,
  getLinkedInFromUserId,
} from "../../../../../../packages/linkedInApi/src/linkedInClient";
import { createUuid } from "../../../core/utils/uuid";
import { LinkedInAdAccountService } from "../../domain/services/linkedInAdAccount.service";
import { LinkedInLeadFormService } from "../../domain/services/linkedInLeadForm.service";
import { LinkedInLeadFormLeadService } from "../../domain/services/linkedInLeadFormLead.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInLeadFormRepository } from "../../infrastructure/repositories/linkedInLeadForm.repository";
import { LinkedInLeadFormLeadRepository } from "../../infrastructure/repositories/linkedInLeadFormLead.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";
import { linkedInUrnToId } from "../../utils/linkedInUrnUtils";

// Define the shape of the LinkedIn lead form submission payload based on LinkedIn docs
interface LinkedInLeadFormSubmission {
  firstName?: string;
  lastName?: string;
  emailAddress?: string;
  phoneNumber?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  company?: string;
  jobTitle?: string;
  jobFunction?: string;
  industry?: string;
  seniority?: string;
  companySize?: string;
  degree?: string;
  fieldOfStudy?: string;
  school?: string;
  startDate?: string;
  graduationDate?: string;
  [key: string]: any; // Allow for additional fields
}

interface LinkedInWebhookData {
  type: string;
  leadGenFormResponse: string;
  leadGenForm: string;
  owner: { organization?: string; sponsoredAccount?: string };
  associatedEntity: { event: string };
  leadType: "SPONSORED" | "EVENT" | "COMPANY" | "ORGANIZATION_PRODUCT";
  leadAction: string;
  occuredAt: number;
}

type PredefinedField =
  | "FIRST_NAME"
  | "LAST_NAME"
  | "EMAIL"
  | "PHONE_NUMBER"
  | "CITY"
  | "STATE"
  | "COUNTRY"
  | "ZIP_CODE"
  | "JOB_TITLE"
  | "JOB_FUNCTION"
  | "SENIORITY" /* add all your field names */;

interface IPredefinedFieldMapping {
  FIRST_NAME: string;
  LAST_NAME: string;
  EMAIL: string;
  PHONE_NUMBER: string;
  CITY: string;
  STATE: string;
  COUNTRY: string;
  ZIP_CODE: string;
  JOB_TITLE: string;
  JOB_FUNCTION: string;
  SENIORITY: string;
  COMPANY_NAME: string;
  COMPANY_SIZE: string;
  INDUSTRY: string;
  DEGREE: string;
  FIELD_OF_STUDY: string;
  SCHOOL: string;
  START_DATE: string;
  GRADUATION_DATE: string;
  GENDER: string;
  WORK_EMAIL: string;
  LINKEDIN_PROFILE_LINK: string;
  WORK_PHONE_NUMBER: string;
}

const predefinedFieldMapping: IPredefinedFieldMapping = {
  FIRST_NAME: "firstName",
  LAST_NAME: "lastName",
  EMAIL: "emailAddress",
  PHONE_NUMBER: "phoneNumber",
  CITY: "city",
  STATE: "state",
  COUNTRY: "country",
  ZIP_CODE: "zipCode",
  JOB_TITLE: "jobTitle",
  JOB_FUNCTION: "jobFunction",
  SENIORITY: "seniority",
  COMPANY_NAME: "company",
  COMPANY_SIZE: "companySize",
  INDUSTRY: "industry",
  DEGREE: "degree",
  FIELD_OF_STUDY: "fieldOfStudy",
  SCHOOL: "school",
  START_DATE: "startDate",
  GRADUATION_DATE: "graduationDate",
  GENDER: "gender",
  WORK_EMAIL: "workEmail",
  LINKEDIN_PROFILE_LINK: "linkedinProfileLink",
  WORK_PHONE_NUMBER: "workPhoneNumber",
};

// Create Inngest function to handle LinkedIn lead form webhooks
export const linkedInLeadFormWebhook = advertisingInngestClient.createFunction(
  {
    id: "lead-form-webhook",
    name: "linkedin/lead-form-webhook",
  },
  { event: "linkedin/lead.updated" },
  async ({ event, step }) => {
    // Extract data from the event
    const payload = event.data as LinkedInWebhookData;

    const linkedInAdAccountService = new LinkedInAdAccountService(
      new LinkedInAdAccountRepository(),
    );
    if (!payload.owner.sponsoredAccount) {
      throw new Error("Sponsored account not found in payload");
    }
    const linkedInAdAccount =
      await linkedInAdAccountService.getOneByLinkedInAdAccountUrn(
        payload.owner.sponsoredAccount,
      );

    if (!linkedInAdAccount) {
      throw new Error("LinkedIn ad account not found");
    }

    console.log(
      "linkedInAdAccount.organizationId",
      linkedInAdAccount.organizationId,
    );
    const client = await getLinkedInApiClientFromOrganizationId(
      linkedInAdAccount.organizationId,
    );
    if (!client) {
      throw new Error("LinkedIn client not found");
    }

    const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();
    const leadFormLeadService = new LinkedInLeadFormLeadService(
      leadFormLeadRepository,
    );

    const leadFormRepository = new LinkedInLeadFormRepository();
    const leadFormService = new LinkedInLeadFormService(leadFormRepository);
    const linkedInService = new LinkedInService(client);

    // Process the lead form submission
    return await step.run("process-lead-responses", async () => {
      // Extract form URN - LinkedIn uses this as a unique identifier
      const formUrn = payload.leadGenForm;
      if (!formUrn) {
        throw new Error("Missing form URN in webhook payload");
      }

      // Find the lead form in our database
      let leadForm = await leadFormService.getOneByLinkedInUrn(formUrn);
      const form = await linkedInService.getLeadFormByUrn({ formUrn }); // fromLinkedin API

      if (!leadForm) {
        console.log("No form found in db, creating one, formUrn", formUrn);
        //TODO retrieve form from linkedin API and create it in our database

        leadForm = await leadFormService.createOne({
          id: createUuid(),
          linkedInAdAccountId: linkedInAdAccount.id,
          leadGenFormUrn: formUrn,
          name: form.name,
          state: form.state,
        });
      }
      // Get specific form responses
      const formId = linkedInUrnToId(payload.leadGenFormResponse);

      console.log("FormId for getLeadformresponsebyid", formId);

      // console.log("LeadFormResponse2", leadFormResponse2);
      const leadFormResponse = await linkedInService.getLeadFormResponseById({
        formLeadResponseId: formId,
      });
      console.log("LeadFormResponse", leadFormResponse);
      const responses = leadFormResponse.formResponse.answers;

      const responseQuestionMapping: Record<number, string> = {};
      console.log("Responses", responses);
      responses.forEach((response) => {
        const questionId = response.questionId;
        const answer = response.answerDetails.textQuestionAnswer;

        if (answer?.answer) {
          responseQuestionMapping[questionId] = answer.answer.trim();
        }
      });

      const leadData: LinkedInLeadFormSubmission = {};

      form.content.questions.forEach((question) => {
        const predefinedField = question.predefinedField;
        const questionId = question.questionId;

        if (predefinedField) {
          const fieldName =
            predefinedFieldMapping[predefinedField as PredefinedField];

          if (fieldName) {
            if (responseQuestionMapping[questionId]) {
              leadData[fieldName] = responseQuestionMapping[questionId];
            } else {
              console.log(
                "No answer found for question",
                questionId,
                fieldName,
              );
            }
          }
        }
      });

      console.log("leadData", leadData);

      const leadCreatedAtEpoch = leadFormResponse.submittedAt;
      const leadCreatedAt = new Date(leadCreatedAtEpoch);

      let campaignUrn =
        leadFormResponse?.leadMetadata?.sponsoredLeadMetadata?.campaign;
      if (!campaignUrn) {
        console.log("No campaign URN found in lead form response");
        campaignUrn = "";
      }

      const foundLead =
        await leadFormLeadService.getOneByLinkedInLeadFormResponseId(
          leadFormResponse.id,
        );

      if (foundLead) {
        console.log("Lead Already Created");
        return {
          success: true,
          leadId: foundLead.id,
          formId: leadForm.id,
          formName: leadForm.name,
        };
      }
      // Create a new lead in our database
      const leadCreated = await leadFormLeadService.createOne({
        id: createUuid(),
        linkedInLeadFormId: leadForm.id,
        linkedInLeadFormResponseId: leadFormResponse.id,
        linkedinCampaignUrn: campaignUrn,
        linkedInAdAccountId: linkedInAdAccount.id,
        // Personal information
        firstName: leadData.firstName || null,
        lastName: leadData.lastName || null,
        email: leadData.emailAddress || null,
        phoneNumber: leadData.phoneNumber || null,

        // Location information
        city: leadData.city || null,
        state: leadData.state || null,
        country: leadData.country || null,
        zipCode: leadData.postalCode || null,

        // Professional information
        companyName: leadData.company || null,
        jobTitle: leadData.jobTitle || null,
        jobFunction: leadData.jobFunction || null,
        industry: leadData.industry || null,
        seniority: leadData.seniority || null,
        companySize: leadData.companySize || null,

        // Education information
        degree: leadData.degree || null,
        fieldOfStudy: leadData.fieldOfStudy || null,
        school: leadData.school || null,
        startDate: leadData.startDate ? new Date(leadData.startDate) : null,
        graduationDate: leadData.graduationDate
          ? new Date(leadData.graduationDate)
          : null,
        gender: leadData.gender || null,
        workEmail: leadData.workEmail || null,
        linkedinProfileLink: leadData.linkedinProfileLink || null,
        workPhoneNumber: leadData.workPhoneNumber || null,
        leadType: payload.leadType,
        testLead: leadFormResponse.testLead,
        // Timestamps
        leadCreatedAt: leadCreatedAt,
      });

      return {
        success: true,
        leadId: leadCreated.id,
        formId: leadForm.id,
        formName: leadForm.name,
      };
    });
  },
);
