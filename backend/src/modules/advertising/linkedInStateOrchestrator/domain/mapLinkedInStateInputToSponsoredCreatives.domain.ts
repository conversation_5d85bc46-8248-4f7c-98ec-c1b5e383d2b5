import { err, ok, Result } from "neverthrow";

import { ConversationCallToActionCopy } from "../../domain/entites/conversationCallToActionCopy";
import { LinkedInPost } from "../../domain/entites/linkedInPost";
import { LinkedInSponsoredCreative } from "../../domain/entites/linkedInSponsoredCreative";
import {
  SponsoredInmailLinkedInStateInput,
  SponsoredPostLinkedInStateInput,
} from "./linkedInStateInput.valueObject";

const mapLinkedInPostsToSponsoredCreatives = (input: {
  linkedInPosts: {
    postId: string;
    campaignId: string;
  }[];
  sponsoredCreatives: LinkedInSponsoredCreative[];
}): Result<
  LinkedInSponsoredCreative[],
  { type: "SOME_SPONSORED_CREATIVES_NOT_FOUND" }
> => {
  const mappedSponsoredCreatives: LinkedInSponsoredCreative[] = [];
  for (const post of input.linkedInPosts) {
    const sponsoredCreative = input.sponsoredCreatives.find(
      (sponsoredCreative) =>
        sponsoredCreative.content.type === "SPONSORED_CONTENT" &&
        sponsoredCreative.content.postId === post.postId &&
        sponsoredCreative.cmapaignId === post.campaignId,
    );
    if (!sponsoredCreative) {
      return err({ type: "SOME_SPONSORED_CREATIVES_NOT_FOUND" });
    }
    mappedSponsoredCreatives.push(sponsoredCreative);
  }
  return ok(mappedSponsoredCreatives);
};

const mapConversationCallToActionCopiesToSponsoredCreatives = (input: {
  conversationCallToActionCopies: {
    conversationCallToActionId: string;
    campaignId: string;
  }[];
  sponsoredCreatives: LinkedInSponsoredCreative[];
}): Result<
  LinkedInSponsoredCreative[],
  { type: "SOME_SPONSORED_CREATIVES_NOT_FOUND" }
> => {
  const mappedSponsoredCreatives: LinkedInSponsoredCreative[] = [];
  for (const conversationCallToActionCopy of input.conversationCallToActionCopies) {
    const sponsoredCreative = input.sponsoredCreatives.find(
      (sponsoredCreative) =>
        sponsoredCreative.content.type === "SPONSORED_INMAIL" &&
        sponsoredCreative.content.conversationCallToActionId ===
          conversationCallToActionCopy.conversationCallToActionId &&
        sponsoredCreative.cmapaignId ===
          conversationCallToActionCopy.campaignId,
    );
    if (!sponsoredCreative) {
      return err({ type: "SOME_SPONSORED_CREATIVES_NOT_FOUND" });
    }
    mappedSponsoredCreatives.push(sponsoredCreative);
  }
  return ok(mappedSponsoredCreatives);
};

const mapSponsoredContentLinkedInStateInputToSponsoredCreatives = (input: {
  linkedInStateInput: SponsoredPostLinkedInStateInput;
  linkedInPosts: LinkedInPost[];
  sponsoredCreatives: LinkedInSponsoredCreative[];
}): Result<
  LinkedInSponsoredCreative[],
  | { type: "SOME_POSTS_NOT_FOUND" }
  | { type: "SOME_SPONSORED_CREATIVES_NOT_FOUND" }
> => {
  const mappedPostsWithCampaignIds: {
    postId: string;
    campaignId: string;
  }[] = [];

  for (const campaign of input.linkedInStateInput.data.campaigns) {
    for (const ad of campaign.ads) {
      const post = input.linkedInPosts.find(
        (post) =>
          (post.content.type === "SINGLE_IMAGE" ||
            post.content.type === "SINGLE_VIDEO" ||
            post.content.type === "DOCUMENT") &&
          post.content.adCreativeId === ad.adCreativeId &&
          post.content.socialPostCopyType === ad.bodyCopyType &&
          post.content.socialPostCallToActionType === ad.callToActionCopyType,
      );

      if (!post) {
        return err({ type: "SOME_POSTS_NOT_FOUND" });
      }

      mappedPostsWithCampaignIds.push({
        postId: post.id,
        campaignId: campaign.campaignId,
      });
    }
  }

  const mappedSponsoredCreatives = mapLinkedInPostsToSponsoredCreatives({
    linkedInPosts: mappedPostsWithCampaignIds,
    sponsoredCreatives: input.sponsoredCreatives,
  });

  if (mappedSponsoredCreatives.isErr()) {
    return err(mappedSponsoredCreatives.error);
  }

  return ok(mappedSponsoredCreatives.value);
};

const mapSponsoredInmailLinkedInStateInputToSponsoredCreatives = (input: {
  linkedInStateInput: SponsoredInmailLinkedInStateInput;
  conversationCallToAction: ConversationCallToActionCopy[];
  sponsoredCreatives: LinkedInSponsoredCreative[];
}): Result<
  LinkedInSponsoredCreative[],
  {
    type:
      | "SOME_CONVERSATION_CALL_TO_ACTIONS_NOT_FOUND"
      | "SOME_SPONSORED_CREATIVES_NOT_FOUND";
  }
> => {
  const mappedConversationCallToActions: {
    conversationCallToActionId: string;
    campaignId: string;
  }[] = [];

  for (const campaign of input.linkedInStateInput.data.campaigns) {
    for (const ad of campaign.ads) {
      const conversationCallToActionCopy = input.conversationCallToAction.find(
        (conversationCallToAction) =>
          conversationCallToAction.valuePropId === ad.valuePropId &&
          conversationCallToAction.subjectType === ad.subjectCopyType &&
          conversationCallToAction.messageType === ad.messageCopyType &&
          conversationCallToAction.type === ad.callToActionCopyType,
      );
      if (!conversationCallToActionCopy) {
        return err({ type: "SOME_CONVERSATION_CALL_TO_ACTIONS_NOT_FOUND" });
      }
      mappedConversationCallToActions.push({
        conversationCallToActionId: conversationCallToActionCopy.id,
        campaignId: campaign.campaignId,
      });
    }
  }

  const mappedSponsoredCreatives =
    mapConversationCallToActionCopiesToSponsoredCreatives({
      conversationCallToActionCopies: mappedConversationCallToActions,
      sponsoredCreatives: input.sponsoredCreatives,
    });

  if (mappedSponsoredCreatives.isErr()) {
    return err(mappedSponsoredCreatives.error);
  }
  return ok(mappedSponsoredCreatives.value);
};

export const mapLinkedInStateInputToSponsoredCreatives = {
  forSponsoredContent:
    mapSponsoredContentLinkedInStateInputToSponsoredCreatives,
  forSponsoredInmail: mapSponsoredInmailLinkedInStateInputToSponsoredCreatives,
};
