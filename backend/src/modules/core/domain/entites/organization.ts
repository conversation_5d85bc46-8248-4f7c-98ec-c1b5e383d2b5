import { z } from "zod";

export const organizationSchema = z.object({
  organizationId: z.number().int().positive(),
  name: z.string().min(1).max(256),
  stripeCustomerId: z.string().min(1).max(256).nullable().optional(),
  createdAt: z.coerce.date().optional(),
  slackNotificationWebhookUrl: z
    .string()
    .transform((val) => (val === "" ? null : val))
    .nullable()
    .refine((val) => val === null || z.string().url().safeParse(val).success, {
      message: "Invalid URL",
    })
    .optional(),
  slackNotificationsEnabled: z.boolean().optional().default(true),
  slackNotificationDayOfWeek: z.number().int().min(1).max(8).optional().default(1), // 1 = Monday, 7 = Sunday, 8 = Everyday
  slackNotificationFrequency: z.enum(["hourly", "daily", "weekly", "biweekly", "monthly"]).optional().default("weekly"),
  slackLeadNotificationsEnabled: z.boolean().optional().default(true), // Enable/disable 15-minute lead notifications
});

export const Organization = (data: z.infer<typeof organizationSchema>) => {
  return organizationSchema.parse(data);
};

export type Organization = z.infer<typeof organizationSchema>;
