import { eq } from "drizzle-orm";

import { db } from "../../../../database/db";
import { organizationTable } from "../../../../database/schemas/core/organization.table";
import { IOrganizationRepository } from "../../application/interfaces/repositories/organization.repository.interface";
import { Organization } from "../../domain/entites/organization";

export class OrganizationRepository implements IOrganizationRepository {
  async selectAll(): Promise<Organization[]> {
    const res = await db.select().from(organizationTable);
    return res.map((row) =>
      Organization({
        organizationId: row.id,
        name: row.name,
        stripeCustomerId: row.stripeCustomerId,
        slackNotificationWebhookUrl: row.slackNotificationWebhookUrl,
        slackNotificationsEnabled: row.slackNotificationsEnabled ?? true,
        slackNotificationDayOfWeek: row.slackNotificationDayOfWeek ?? 1,
        slackNotificationFrequency: (row.slackNotificationFrequency as "hourly" | "daily" | "weekly" | "biweekly" | "monthly") ?? "weekly",
        slackLeadNotificationsEnabled: row.slackLeadNotificationsEnabled ?? true,
      }),
    );
  }
  async getOne(organizationId: number): Promise<Organization | null> {
    const res = await db
      .select()
      .from(organizationTable)
      .where(eq(organizationTable.id, organizationId));
    if (!res[0]) {
      return null;
    }
    return Organization({
      organizationId: res[0].id,
      name: res[0].name,
      stripeCustomerId: res[0].stripeCustomerId,
      createdAt: res[0].createdAt,
      slackNotificationWebhookUrl: res[0].slackNotificationWebhookUrl,
      slackNotificationsEnabled: res[0].slackNotificationsEnabled ?? true,
      slackNotificationDayOfWeek: res[0].slackNotificationDayOfWeek ?? 1,
      slackNotificationFrequency: (res[0].slackNotificationFrequency as "hourly" | "daily" | "weekly" | "biweekly" | "monthly") ?? "weekly",
      slackLeadNotificationsEnabled: res[0].slackLeadNotificationsEnabled ?? true,
    });
  }

  async getAll(pagination: {
    offset: number;
    limit: number;
  }): Promise<Organization[]> {
    const res = await db
      .select()
      .from(organizationTable)
      .limit(pagination.limit)
      .offset(pagination.offset);
    return res.map((row) =>
      Organization({
        organizationId: row.id,
        name: row.name,
        createdAt: row.createdAt,
        stripeCustomerId: row.stripeCustomerId,
        slackNotificationWebhookUrl: row.slackNotificationWebhookUrl,
        slackNotificationsEnabled: row.slackNotificationsEnabled ?? true,
        slackNotificationDayOfWeek: row.slackNotificationDayOfWeek ?? 1,
        slackNotificationFrequency: (row.slackNotificationFrequency as "hourly" | "daily" | "weekly" | "biweekly" | "monthly") ?? "weekly",
        slackLeadNotificationsEnabled: row.slackLeadNotificationsEnabled ?? true,
      }),
    );
  }

  async updateSlackWebhook(
    organizationId: number,
    webhookUrl: string | null,
  ): Promise<void> {
    await db
      .update(organizationTable)
      .set({
        slackNotificationWebhookUrl: webhookUrl,
        updatedAt: new Date(),
      })
      .where(eq(organizationTable.id, organizationId));
  }

  async updateSlackSettings(
    organizationId: number,
    settings: {
      webhookUrl?: string | null;
      enabled?: boolean;
      dayOfWeek?: number;
      frequency?: "hourly" | "daily" | "weekly" | "biweekly" | "monthly";
      leadNotificationsEnabled?: boolean;
    }
  ): Promise<void> {
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (settings.webhookUrl !== undefined) {
      updateData.slackNotificationWebhookUrl = settings.webhookUrl;
    }
    if (settings.enabled !== undefined) {
      updateData.slackNotificationsEnabled = settings.enabled;
    }
    if (settings.dayOfWeek !== undefined) {
      updateData.slackNotificationDayOfWeek = settings.dayOfWeek;
    }
    if (settings.frequency !== undefined) {
      updateData.slackNotificationFrequency = settings.frequency;
    }
    if (settings.leadNotificationsEnabled !== undefined) {
      updateData.slackLeadNotificationsEnabled = settings.leadNotificationsEnabled;
    }

    await db
      .update(organizationTable)
      .set(updateData)
      .where(eq(organizationTable.id, organizationId));
  }
}
