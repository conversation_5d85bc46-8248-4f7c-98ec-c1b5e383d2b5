import { createClerkClient } from "@clerk/backend";
import { z } from "zod";

import { adminRoute, authenticatedRoute } from "../../../../trpc/trpc";
import { LinkedInUserService } from "../../../advertising/domain/services/linkedInUser.service";
import { LinkedInUserRepository } from "../../../advertising/infrastructure/repositories/linkedInUser.repository";
import { SendNotificationUseCase } from "../../../advertising/application/useCase/notifications/sendNotification.useCase";
import { CampaignMetricsAggregationService } from "../../../advertising/infrastructure/services/campaignMetricsAggregation.service";
import { SlackApiService } from "../../../advertising/infrastructure/services/slack.service";
import { LinkedInAdAccountRepository } from "../../../advertising/infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInCampaignGroupRepository } from "../../../advertising/infrastructure/repositories/linkedInCampaignGroup.repository";
import { accountRepository } from "../../../crm/infrastructure/repositories/account.repository";
import { getVerticalsRequestDto } from "../../application/dtos/account/getVerticals.dto";
import { createSegmentDto } from "../../application/dtos/segment/createSegment.dto";
import { editSegmentDto } from "../../application/dtos/segment/editSegment.dto";
import { getSegmentRequestDtoSchema } from "../../application/dtos/segment/getSegment.dto";
import { updateSegmentStatusDto } from "../../application/dtos/segment/updateSegmentStatus.dto";
import { createSegmentValuePropRequestDto } from "../../application/dtos/segmentValueProp/createSegmentValueProp.dto";
import { deleteSegmentValuePropRequestDto } from "../../application/dtos/segmentValueProp/deleteSegmentValueProp.dto";
import { editSegmentValuePropRequestDto } from "../../application/dtos/segmentValueProp/editSegmentValueProp.dto";
import { getSegmentValuePropForSegmentRequestDto } from "../../application/dtos/segmentValueProp/getSegmentValuePropForSegment.dto";
import { addUserRequestDto } from "../../application/dtos/user/addUser.dto";
import { softDeleteUserRequestDto } from "../../application/dtos/user/softDeleteUser.dto";
import { AddUserUseCase } from "../../application/useCases/admin/addUser.useCase";
import { CreateSegmentUseCase } from "../../application/useCases/admin/createSegment.useCase";
import { CreateSegmentValuePropUseCase } from "../../application/useCases/admin/createSegmentValueProp.useCase";
import { DeleteSegmentValuePropUseCase } from "../../application/useCases/admin/deleteSegmentValueProp.useCase";
import { EditSegmentUseCase } from "../../application/useCases/admin/editSegment.useCase";
import { EditSegmentValuePropUseCase } from "../../application/useCases/admin/editSegmentValueProp.useCase";
import { GetAllOrganizationsUseCase } from "../../application/useCases/admin/getAllOrganizations.useCase";
import { GetSegmentAdminUseCase } from "../../application/useCases/admin/getSegmentAdmin.useCase";
import { GetValuePropsForSegmentUseCase } from "../../application/useCases/admin/getValuePropsForSegment.useCase";
import { SwitchOrganizationUseCase } from "../../application/useCases/admin/switchOrganization.useCase";
import { UpdateSegmentStatusUseCase } from "../../application/useCases/admin/updateSegmentStatus.useCase";
import { getSegmentUseCase } from "../../application/useCases/getSegment.useCase";
import { GetSegmentsForOrganizationUseCase } from "../../application/useCases/getSegmentsForOrganizationUseCase";
import { GetVerticalsUseCase } from "../../application/useCases/getVerticals.useCase";
import { SegmentService } from "../../domain/services/segment.service";
import { UserService } from "../../domain/services/user.service";
import { OrganizationRepository } from "../../infrastructure/repositories/organization.repository";
import { segmentRepository } from "../../infrastructure/repositories/segment.repository";
import { SegmentValuePropRepository } from "../../infrastructure/repositories/segmentValueProp.repository";
import { UserRepository } from "../../infrastructure/repositories/user.repository";
import { SendLeadNotificationUseCase } from "../../../advertising/application/useCase/notifications/sendLeadNotification.useCase";
import { LinkedInLeadFormRepository } from "../../../advertising/infrastructure/repositories/linkedInLeadForm.repository";
import { LinkedInLeadFormLeadRepository } from "../../../advertising/infrastructure/repositories/linkedInLeadFormLead.repository";
import { LinkedInLeadFormLeadService } from "../../../advertising/domain/services/linkedInLeadFormLead.service";
import { SoftDeleteUserUseCase } from "../../application/useCases/admin/softDeleteUser.useCase";

export const adminController = {
  getAllOrganizations: authenticatedRoute.query(async ({ ctx }) => {
    const organizationRepository = new OrganizationRepository();
    const useCase = new GetAllOrganizationsUseCase(organizationRepository);
    return useCase.execute();
  }),
  switchOrganization: authenticatedRoute
    .input(
      z.object({
        organizationId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const userRepository = new UserRepository();
      const organizationRepository = new OrganizationRepository();
      const useCase = new SwitchOrganizationUseCase(
        userRepository,
        organizationRepository,
      );
      await useCase.execute(ctx.userId, input.organizationId);
    }),

  getSegmentsForOrganization: adminRoute
    .input(
      z.object({
        organizationId: z.number(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const useCase = new GetSegmentsForOrganizationUseCase(
        new SegmentService(segmentRepository),
      );
      return useCase.execute({ organizationId: input.organizationId });
    }),

  getVerticals: adminRoute
    .input(getVerticalsRequestDto)
    .query(async ({ ctx, input }) => {
      const useCase = new GetVerticalsUseCase(accountRepository);
      return useCase.execute({ organizationId: input.organizationId });
    }),

  createSegment: adminRoute
    .input(createSegmentDto)
    .mutation(async ({ ctx, input }) => {
      const useCase = new CreateSegmentUseCase(segmentRepository);
      return useCase.execute(input);
    }),
  updateSegmentStatus: adminRoute
    .input(updateSegmentStatusDto)
    .mutation(async ({ ctx, input }) => {
      const useCase = new UpdateSegmentStatusUseCase(segmentRepository);
      await useCase.execute(input);
    }),
  editSegment: adminRoute
    .input(editSegmentDto)
    .mutation(async ({ ctx, input }) => {
      const useCase = new EditSegmentUseCase(segmentRepository);
      await useCase.execute(input);
    }),
  getSegment: adminRoute
    .input(getSegmentRequestDtoSchema)
    .query(async ({ ctx, input }) => {
      const useCase = new GetSegmentAdminUseCase(segmentRepository);
      const res = await useCase.execute(input);
      return res;
    }),
  getValuePropsForSegment: adminRoute
    .input(getSegmentValuePropForSegmentRequestDto)
    .query(async ({ ctx, input }) => {
      const useCase = new GetValuePropsForSegmentUseCase(
        new SegmentValuePropRepository(),
      );
      return useCase.execute(input);
    }),
  createSegmentValueProp: adminRoute
    .input(createSegmentValuePropRequestDto)
    .mutation(async ({ ctx, input }) => {
      const useCase = new CreateSegmentValuePropUseCase(
        segmentRepository,
        new SegmentValuePropRepository(),
      );
      return useCase.execute(input);
    }),

  editSegmentValueProp: adminRoute
    .input(editSegmentValuePropRequestDto)
    .mutation(async ({ ctx, input }) => {
      const useCase = new EditSegmentValuePropUseCase(
        new SegmentValuePropRepository(),
      );
      return useCase.execute(input);
    }),
  deleteSegmentValueProp: adminRoute
    .input(deleteSegmentValuePropRequestDto)
    .mutation(async ({ ctx, input }) => {
      const useCase = new DeleteSegmentValuePropUseCase(
        new SegmentValuePropRepository(),
      );
      await useCase.execute(input);
    }),

  addUser: adminRoute
    .input(addUserRequestDto)
    .mutation(async ({ ctx, input }) => {
      const userRepository = new UserRepository();
      const userService = new UserService(userRepository);
      const organizationRepository = new OrganizationRepository();
      const linkedInUserService = new LinkedInUserService({
        linkedInUserRepository: new LinkedInUserRepository(),
      });
      const clerkClient = createClerkClient({
        secretKey: process.env.CLERK_SECRET_KEY,
      });
      const useCase = new AddUserUseCase({
        userService,
        organizationRepository,
        linkedInUserService,
        clerkClient,
      });
      await useCase.execute(input);
    }),

  softDeleteUser: adminRoute
    .input(softDeleteUserRequestDto)
    .mutation(async ({ ctx, input }) => {
      const userRepository = new UserRepository();
      const userService = new UserService(userRepository);
      const organizationRepository = new OrganizationRepository();
      const clerkClient = createClerkClient({
        secretKey: process.env.CLERK_SECRET_KEY,
      });
      const useCase = new SoftDeleteUserUseCase({
        userService,
        organizationRepository,
        clerkClient,
      });
      await useCase.execute(input);
    }),

  getUsersForOrganization: adminRoute
    .input(
      z.object({
        organizationId: z.number(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const userRepository = new UserRepository();
      const users = await userRepository.getUsersForOrganization(
        input.organizationId,
      );
      return users;
    }),

  updateOrganizationSlackWebhook: adminRoute
    .input(
      z.object({
        organizationId: z.number(),
        webhookUrl: z.string().url().optional().nullable(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationRepository = new OrganizationRepository();
      
      // Get current organization to verify it exists
      const organization = await organizationRepository.getOne(input.organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      // Update the organization with new webhook URL
      await organizationRepository.updateSlackWebhook(
        input.organizationId, 
        input.webhookUrl || null
      );

      return {
        success: true,
        slackNotificationWebhookUrl: input.webhookUrl,
        isConfigured: !!input.webhookUrl,
      };
    }),

  updateOrganizationSlackSettings: adminRoute
    .input(
      z.object({
        organizationId: z.number(),
        webhookUrl: z.string().url().optional().nullable(),
        enabled: z.boolean().optional(),
        dayOfWeek: z.number().int().min(1).max(8).optional(),
        frequency: z.enum(["hourly", "daily", "weekly", "biweekly", "monthly"]).optional(),
        leadNotificationsEnabled: z.boolean().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationRepository = new OrganizationRepository();
      
      // Get current organization to verify it exists
      const organization = await organizationRepository.getOne(input.organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      // Update the organization with new settings
      await organizationRepository.updateSlackSettings(input.organizationId, {
        webhookUrl: input.webhookUrl,
        enabled: input.enabled,
        dayOfWeek: input.dayOfWeek,
        frequency: input.frequency,
        leadNotificationsEnabled: input.leadNotificationsEnabled,
      });

      // Get updated organization
      const updatedOrganization = await organizationRepository.getOne(input.organizationId);
      if (!updatedOrganization) {
        throw new Error("Organization not found after update");
      }

      return {
        success: true,
        slackNotificationWebhookUrl: updatedOrganization.slackNotificationWebhookUrl,
        slackNotificationsEnabled: updatedOrganization.slackNotificationsEnabled ?? true,
        slackNotificationDayOfWeek: updatedOrganization.slackNotificationDayOfWeek ?? 1,
        slackNotificationFrequency: updatedOrganization.slackNotificationFrequency ?? "weekly",
        slackLeadNotificationsEnabled: updatedOrganization.slackLeadNotificationsEnabled ?? true,
        isConfigured: !!updatedOrganization.slackNotificationWebhookUrl,
      };
    }),

  getOrganizationSlackSettings: adminRoute
    .input(
      z.object({
        organizationId: z.number(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const organizationRepository = new OrganizationRepository();
      
      const organization = await organizationRepository.getOne(input.organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      return {
        slackNotificationWebhookUrl: organization.slackNotificationWebhookUrl,
        slackNotificationsEnabled: organization.slackNotificationsEnabled ?? true,
        slackNotificationDayOfWeek: organization.slackNotificationDayOfWeek ?? 1,
        slackNotificationFrequency: organization.slackNotificationFrequency ?? "weekly",
        slackLeadNotificationsEnabled: organization.slackLeadNotificationsEnabled ?? true,
        isConfigured: !!organization.slackNotificationWebhookUrl,
      };
    }),

  sendTestWeeklyNotification: adminRoute
    .input(
      z.object({
        organizationId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationRepository = new OrganizationRepository();
      const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
      const linkedInCampaignGroupRepository = new LinkedInCampaignGroupRepository();
      
      const slackApiService = new SlackApiService();
      const campaignMetricsAggregationService = new CampaignMetricsAggregationService(
        linkedInAdAccountRepository,
        linkedInCampaignGroupRepository
      );
      
      const sendNotificationUseCase = new SendNotificationUseCase(
        organizationRepository,
        slackApiService,
        campaignMetricsAggregationService
      );

      // Calculate previous week's date range (Monday to Sunday)
      const now = new Date();
      const currentDayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const daysToSubtract = currentDayOfWeek === 0 ? 6 : currentDayOfWeek - 1; // Get to previous Monday
      
      const weekEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - daysToSubtract - 1); // Previous Sunday
      const weekStartDate = new Date(weekEndDate.getFullYear(), weekEndDate.getMonth(), weekEndDate.getDate() - 6); // Previous Monday

      const result = await sendNotificationUseCase.execute({
        organizationId: input.organizationId,
        weekStartDate,
        weekEndDate,
      });

      return result;
    }),

  testSlackWebhook: adminRoute
    .input(
      z.object({
        organizationId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationRepository = new OrganizationRepository();
      
      // Get current organization
      const organization = await organizationRepository.getOne(input.organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      if (!organization.slackNotificationWebhookUrl) {
        throw new Error("No Slack webhook configured for this organization");
      }

      // Send a test message using SlackApiService
      const slackApiService = new SlackApiService();
      const testMessage = {
        text: `🧪 Test message from Kalos Admin Portal for ${organization.name}`,
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Test notification from Kalos*\n\nThis is a test message to verify your Slack webhook configuration for *${organization.name}*.\n\nIf you're seeing this message, your webhook is working correctly! 🎉`
            }
          }
        ]
      };

      const slackResponse = await slackApiService.sendMessage(
        organization.slackNotificationWebhookUrl,
        testMessage
      );

      if (!slackResponse.ok) {
        throw new Error(`Failed to send test message: ${slackResponse.error}`);
      }

      return {
        success: true,
        message: "Test message sent successfully!"
      };
    }),

  testLeadNotification: adminRoute
    .input(
      z.object({
        organizationId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationRepository = new OrganizationRepository();
      const slackApiService = new SlackApiService();
      const leadFormRepository = new LinkedInLeadFormRepository();
      const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();
      const leadFormLeadService = new LinkedInLeadFormLeadService(leadFormLeadRepository);
      
      // Get current organization
      const organization = await organizationRepository.getOne(input.organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      if (!organization.slackNotificationWebhookUrl) {
        throw new Error("No Slack webhook configured for this organization");
      }

      if (!organization.slackNotificationsEnabled) {
        throw new Error("Slack notifications are disabled for this organization");
      }

      // Use a 2-day window for testing as requested
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - (2 * 24 * 60 * 60 * 1000)); // 2 days ago

      // Get leads from the last 2 days for this organization
      const leads = await leadFormLeadRepository.getLeadsByOrganizationInTimeWindow(
        input.organizationId,
        startDate,
        endDate
      );

      if (leads.length === 0) {
        throw new Error("No leads found in the last 2 days for testing. Please wait for leads to be generated or try a different organization.");
      }

      // Use the most recent lead for testing
      const testLead = leads[0];
      if (!testLead) {
        throw new Error("No test lead available");
      }

      // Create the SendLeadNotificationUseCase and execute it
      const sendLeadNotificationUseCase = new SendLeadNotificationUseCase(
        organizationRepository,
        slackApiService,
        leadFormRepository
      );

      const result = await sendLeadNotificationUseCase.execute(
        { 
          leadId: testLead.id, 
          organizationId: input.organizationId 
        },
        testLead
      );

      if (!result.success) {
        throw new Error(`Failed to send test lead notification: ${result.error}`);
      }

      return {
        success: true,
        message: `Test lead notification sent successfully using lead: ${testLead.firstName || 'Unknown'} ${testLead.lastName || 'Unknown'} from ${testLead.companyName || 'Unknown Company'} (from last 2 days)`,
        leadInfo: {
          name: `${testLead.firstName || 'Unknown'} ${testLead.lastName || 'Unknown'}`,
          company: testLead.companyName || 'Unknown Company',
          email: testLead.email || testLead.workEmail || 'No email',
          createdAt: testLead.leadCreatedAt
        }
      };
    }),
};
