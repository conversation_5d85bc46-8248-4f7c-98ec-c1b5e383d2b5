import { legacyRouterSpec } from "@kalos/api";

import { baseAdvertisingController } from "../../modules/advertising/interfaceAdapters/controllers/_base_.controller";
import { coreControllers } from "../../modules/core/interfaceAdapters/contollers/base.controller";
import { crmController } from "../../modules/crm/interfaceAdapters/controllers/_base_controllers";
import { sharedController } from "../../modules/shared/interfaceAdapters/controllers/base.controller";
import { createTRPCRouter, publicProcedure, t } from "../trpc";

const v2Router = {
  tst: publicProcedure.query(() => {
    return {
      hi: "hi",
    };
  }),
  core: coreControllers,
  ads: baseAdvertisingController,
  crm: crmController,
  shared: sharedController,
};
export const backendRouter = createTRPCRouter({
  v2: v2Router,
  ...legacyRouterSpec,
});

export const callerFactory = t.createCallerFactory(backendRouter);

export type BackendRouter = typeof backendRouter;
