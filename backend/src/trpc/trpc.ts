import type { FetchCreateContextFnOptions } from "@trpc/server/adapters/fetch";
import type { Context } from "hono";
import { getAuth } from "@hono/clerk-auth";
import { initTRPC, TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import superjson from "superjson";
import { ZodError } from "zod";

import { db as oldDb } from "@kalos/database";

import { organizationUser } from "../../../packages/database/src/schema/organizationUser";
import { db } from "../database/db";
import { organizationUserTable } from "../database/schemas/core/organizationUser.table";

// eslint-disable-next-line @typescript-eslint/require-await
const createTRPCContext = (opts: FetchCreateContextFnOptions, c: Context) => {
  const auth = getAuth(c);
  return {
    userId: auth?.userId,
  };
};

const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter: ({ shape, error }) => ({
    ...shape,
    data: {
      ...shape.data,
      zodError: error.cause instanceof ZodError ? error.cause.flatten() : null,
    },
  }),
});

type Router = typeof t.router;

const createTRPCRouter = t.router;

const publicProcedure = t.procedure;

const authenticatedRoute = t.procedure.use(({ ctx, next }) => {
  if (!ctx.userId) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  return next({
    ctx: {
      userId: ctx.userId,
    },
  });
});

export const adminRoute = authenticatedRoute.use(async ({ ctx, next }) => {
  if (!ctx.userId) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  if (
    ctx.userId !== "user_2hpfJ7nV9VwkpT8o4pmV3vkLsd0" &&
    ctx.userId !== "user_2gvKecrxU1ugrxInkMeeqc4KJsp" &&
    ctx.userId !== "user_2xmOIJw6DG9Xs3koIfkJXhsrfT3" &&
    ctx.userId !== "user_2w3C3bW4CfKzD2TSka2dUFYt9pu" &&
    ctx.userId !== "user_2xdvevtuXmoks4qguWUaOSHhmL7" &&
    ctx.userId !== "user_2p2YdEOYsl2oCmgvKrj8AWAf5Td" &&
    ctx.userId !== "user_2xdve9cKqaPnMFF8Fr3rt9etjDG" &&
    ctx.userId !== "user_2yI7bjxJLeRjphdosN4lAfjhcGq"
  ) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  return next({
    ctx: {
      userId: ctx.userId,
    },
  });
});

export const organizationRoute = authenticatedRoute.use(
  async ({ ctx, next }) => {
    let organization = await db
      .select()
      .from(organizationUserTable)
      .where(eq(organizationUserTable.userId, ctx.userId));

    if (!organization[0]) {
      organization = await oldDb
        .select()
        .from(organizationUser)
        .where(eq(organizationUser.userId, ctx.userId));
    }
    if (!organization[0]) {
      throw new TRPCError({ code: "UNAUTHORIZED" });
    }
    return next({
      ctx: {
        userId: ctx.userId,
        organizationId: organization[0].organizationId,
      },
    });
  },
);

export {
  authenticatedRoute,
  publicProcedure,
  createTRPCRouter,
  t,
  createTRPCContext,
};

export type { Router };
