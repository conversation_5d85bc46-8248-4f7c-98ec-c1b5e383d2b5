import { Transform } from "stream";
import type { Readable, TransformCallback } from "stream";

import { LinkedInUser } from "@kalos/../../backend/src/modules/advertising/domain/entites/linkedInUser";
import { LinkedInUserService } from "@kalos/../../backend/src/modules/advertising/domain/services/linkedInUser.service";
import { LinkedInUserRepository } from "@kalos/../../backend/src/modules/advertising/infrastructure/repositories/linkedInUser.repository";
import { OrganizationUserRepository } from "@kalos/../../backend/src/modules/core/infrastructure/repositories/organizationUser.repository";

const baseUrl = "https://api.linkedin.com";
const baseRestUrl = `${baseUrl}/rest`;

export interface Exclude {
  or: Or;
}

export interface TargetingCriteria {
  include: Include;
  exclude?: Exclude;
}

export interface Include {
  and: And[];
}

export interface And {
  or: Or;
}

interface LinkedInChunk {
  firstByte: number;
  lastByte: number;
  uploadUrl: string;
}

async function splitAndUploadChunks(
  chunks: LinkedInChunk[],
  readable: Readable,
): Promise<string[]> {
  // First, load the entire file into memory
  const chunks_buffer = await new Promise<Buffer>((resolve, reject) => {
    const chunks: Buffer[] = [];
    readable.on("data", (chunk) => chunks.push(Buffer.from(chunk)));
    readable.on("end", () => resolve(Buffer.concat(chunks)));
    readable.on("error", (err) => reject(err));
  });

  const etags: string[] = [];

  // Process each chunk sequentially
  for (const chunk of chunks) {
    const chunkSize = chunk.lastByte - chunk.firstByte + 1;
    const chunkData = chunks_buffer.slice(chunk.firstByte, chunk.lastByte + 1);

    try {
      console.log(`Uploading chunk bytes ${chunk.firstByte}-${chunk.lastByte}`);
      const uploadRes = await fetch(chunk.uploadUrl, {
        method: "PUT",
        body: chunkData,
        headers: {
          "Content-Length": chunkSize.toString(),
          "Content-Range": `bytes ${chunk.firstByte}-${chunk.lastByte}/*`,
        },
      });

      if (!uploadRes.ok) {
        throw new Error(
          `Failed to upload chunk ${chunk.firstByte}-${chunk.lastByte}: ${uploadRes.status} ${uploadRes.statusText}`,
        );
      }

      const etag = uploadRes.headers.get("etag");
      if (!etag) {
        throw new Error("Failed to get etag for uploaded chunk");
      }
      etags.push(etag);
    } catch (error) {
      console.error("Error uploading chunk:", error);
      throw error;
    }
  }

  return etags;
}

export type Or = Record<string, string[]>;

export class LinkedInApiClient {
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  accessToken: string | undefined = undefined;
  constructor(refreshToken: string) {
    if (
      !process.env.LINKEDIN_CLIENT_ID ||
      !process.env.LINKEDIN_CLIENT_SECRET
    ) {
      throw new Error("Missing LinkedIn client ID or client secret");
    }
    this.clientId = process.env.LINKEDIN_CLIENT_ID;
    this.clientSecret = process.env.LINKEDIN_CLIENT_SECRET;
    this.refreshToken = refreshToken;
  }

  async authenticate(): Promise<
    { success: true } | { success: false; errorMessage?: string }
  > {
    const urlencoded = new URLSearchParams();
    urlencoded.append("grant_type", "refresh_token");
    urlencoded.append("client_id", this.clientId);
    urlencoded.append("client_secret", this.clientSecret);
    urlencoded.append("refresh_token", this.refreshToken);

    const res = await fetch(`${baseUrl}/oauth/v2/accessToken`, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: urlencoded,
    });
    if (!res.ok) {
      return { success: false, errorMessage: "Failed to authenticate" };
    }
    const data = (await res.json()) as {
      access_token: string;
    };
    this.accessToken = data.access_token;
    return { success: true };
  }

  async getAdAccountsForUser() {
    console.log(`${baseRestUrl}/adAccounts`);
    const res = await this.callFetchToLinkedInApi(
      `${baseRestUrl}/adAccountUsers?q=authenticatedUser`,
    );
    if (!res.success) {
      console.log(res.error);
      throw "err";
    }
    const data = res.data as { elements: unknown };
    return data.elements as {
      account: string;
    }[];
  }

  async getAdAccount(id: string) {
    const res = await this.callFetchToLinkedInApi(
      `${baseRestUrl}/adAccounts/${id}`,
    );
    if (!res.success) {
      console.log(res.error);
      throw "err";
    }
    return res.data as { id: number; name: string; reference: string };
  }

  async getFacets() {
    const res = await this.callFetchToLinkedInApi(
      "https://api.linkedin.com/rest/adTargetingFacets",
    );
    if (!res.success) {
      throw "err";
    }

    const resData: {
      elements: {
        facetName: string;
        adTargetingFacetUrn: string;
        availableEntityFinders: string[];
      }[];
    } = res.data as {
      elements: {
        facetName: string;
        adTargetingFacetUrn: string;
        availableEntityFinders: string[];
      }[];
    };
    const facets: {
      facetName: string;
      adTargetingFacetUrn: string;
      availableEntityFinders: ("TYPEAHEAD" | "AD_TARGETING_FACET")[];
    }[] = [];
    for (const facet of resData.elements) {
      if (
        facet.availableEntityFinders.includes("TYPEAHEAD") ||
        facet.availableEntityFinders.includes("AD_TARGETING_FACET")
      ) {
        const finders = facet.availableEntityFinders.filter(
          (each) => each == "TYPEAHEAD" || each == "AD_TARGETING_FACET",
        );
        facets.push({
          facetName: facet.facetName,
          adTargetingFacetUrn: facet.adTargetingFacetUrn,
          availableEntityFinders: finders,
        });
      }
    }
    return facets;
  }

  async getEntitesViaAdTargetingFacet(facetUrn: string) {
    const params = new URLSearchParams({
      q: "adTargetingFacet",
      facet: facetUrn,
      queryVersion: "QUERY_USES_URNS",
    });

    const strParams = params.toString();
    const entityRes = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adTargetingEntities?${strParams}`,
    );
    if (!entityRes.success) {
      throw new Error("Failed to get entities");
    }
    const res = entityRes.data as {
      elements: {
        name: string;
        urn: string;
      }[];
    };
    return res.elements;
  }

  async getEntitesViaTypeahead(facetUrn: string, query: string) {
    const params = new URLSearchParams({
      q: "TYPEAHEAD",
      facet: facetUrn,
      query: query,
      queryVersion: "QUERY_USES_URNS",
    });

    const strParams = params.toString();
    const entityRes = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adTargetingEntities?${strParams}`,
    );
    if (!entityRes.success) {
      throw new Error("Failed to get entities");
    }
    const res = entityRes.data as {
      elements: {
        name: string;
        urn: string;
      }[];
    };
    return res.elements;
  }

  async getAdPreview(creativeUrn: string, accountUrn: string) {
    const creativeUrnWithPrefix = creativeUrn.startsWith(
      "urn:li:sponsoredCreative:",
    )
      ? creativeUrn
      : `urn:li:sponsoredCreative:${creativeUrn}`;
    const accountUrnWithPrefix = accountUrn.startsWith(
      "urn:li:sponsoredAccount:",
    )
      ? accountUrn
      : `urn:li:sponsoredAccount:${accountUrn}`;
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adPreviews?q=creative&creative=${creativeUrnWithPrefix.replaceAll(":", "%3A")}&account=${accountUrnWithPrefix.replaceAll(":", "%3A")}`,
    );

    if (!res.success) {
      throw new Error(
        `Failed to get ad preview due to ${JSON.stringify(res.error, null, 2)}`,
      );
    }

    return res.data as {
      elements: {
        creative: string;
        placement: {
          linkedin: {
            contentPresentationType: string;
            placementName: string;
          };
        };
        preview: string;
      }[];
      paging: {
        count: number;
        links: unknown[];
        start: number;
        total: number;
      };
    };
  }

  async getLeadNotifications(adAccountUrn: string) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/leadNotifications?` +
        // `?q=criteria` +
        `owner=(value:(sponsoredAccount:${adAccountUrn}))` +
        `&leadType=(leadType:SPONSORED)`,
    );

    if (!res.success) {
      console.log("res", res);

      throw new Error(
        `Failed to get lead notifications due to ${JSON.stringify(res.error, null, 2)}`,
      );
    }

    return res.data;
  }

  async getAnalyticsForCreatives(
    creativeUrns: string[],
    startDate: Date = new Date("2024-05-01"),
    endDate?: Date,
    timeGranularity: "ALL" | "DAILY" | "MONTHLY" | "YEARLY" = "ALL",
  ) {
    // NOTE YOU CAN ONLY REQUEST 20 METRICS, look at &fields param
    // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/ads-reporting/ads-reporting?view=li-lms-2025-03&tabs=http#requesting-specific-metrics-in-the-analytics-finder

    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAnalytics?q=analytics` +
        `&dateRange=(start:(day:${startDate.getDate()},month:${startDate.getMonth() + 1},year:${startDate.getFullYear()})` +
        `${endDate ? `,end:(day:${endDate.getDate()},month:${endDate.getMonth() + 1},year:${endDate.getFullYear()})` : ""})` +
        `&timeGranularity=${timeGranularity}` +
        `&pivot=CREATIVE` +
        `&creatives=List(${creativeUrns
          .map((urn) =>
            `urn:li:sponsoredCreative:${urn}`.replaceAll(":", "%3A"),
          )
          .join(",")})` +
        `&fields=` +
        `impressions,clicks,costInUsd,pivotValues,costInLocalCurrency,` +
        `dateRange,oneClickLeads,externalWebsiteConversions,landingPageClicks,` +
        `sends,opens,actionClicks,oneClickLeads,oneClickLeadFormOpens,totalEngagements,` +
        `videoCompletions,videoFirstQuartileCompletions,videoMidpointCompletions,` +
        `videoThirdQuartileCompletions,videoStarts`,
      undefined,
      undefined,
      {
        LinkedInVersion: "202411",
        "X-Restli-Protocol-Version": "2.0.0", // Required to use LinkedIn API 2.0.
      },
    );
    if (!res.success) {
      throw new Error(
        `Failed to get creative analytics due to ${JSON.stringify(res.error)}`,
      );
    }

    return res.data as {
      elements: {
        actionClicks: number;
        adUnitClicks: number;
        clicks: number;
        costInLocalCurrency: string;
        costInUsd: string;
        landingPageClicks: number;
        oneClickLeadFormOpens: number;
        videoCompletions: number;
        videoFirstQuartileCompletions: number;
        videoMidpointCompletions: number;
        videoThirdQuartileCompletions: number;
        videoViews: number;
        videoStarts: number;
        dateRange: {
          end: {
            day: number;
            month: number;
            year: number;
          };
          start: {
            day: number;
            month: number;
            year: number;
          };
        };
        externalWebsiteConversions: number;
        impressions: number;
        pivotValues: string[];
        totalEngagements: number;
        oneClickLeads: number;
        sends: number;
        opens: number;
      }[];
      paging: {
        count: number;
        links: unknown[];
        start: number;
      };
    };
  }

  async createCampaign(data: {
    adAccount: string;
    campaignGroupUrn: string;
    budgetType: "DAILY" | "TOTAL";
    budget: number;
    name: string;
    startDate: Date;
    endDate?: Date;
    audienceTargets: TargetingCriteria;
    unitCost: number;
    manualBidding?: boolean;
    currencyCode?: string;
    objectiveType?:
      | "BRAND_AWARENESS"
      | "ENGAGEMENT"
      | "LEAD_GENERATION"
      | "WEBSITE_CONVERSIONS"
      | "WEBSITE_VISITS"
      | "VIDEO_VIEWS";
    type?: "SPONSORED_UPDATES" | "SPONSORED_INMAILS";
  }) {
    // Set defaults
    data.manualBidding = data.manualBidding ?? true;
    data.currencyCode = data.currencyCode ?? "USD";

    const diff = data.endDate
      ? Math.abs(data.startDate.getTime() - data.endDate.getTime())
      : undefined;
    const diffDays = diff ? Math.ceil(diff / (1000 * 3600 * 24)) : undefined;
    const dailyBudget = diffDays
      ? data.budgetType == "TOTAL"
        ? data.manualBidding
          ? Math.min((data.budget / diffDays) * 2, data.budget) // double the daily budget for manual bidding, but cannot exceed the total budget
          : data.budget / diffDays
        : data.budget
      : data.budget;
    const unitCost = Math.min(data.unitCost, dailyBudget); // cannot exceed daily budget
    let optimizationTargetType: undefined | string = undefined;
    let costType: undefined | string = undefined;
    let objectiveType: undefined | string = undefined;

    switch (data.objectiveType) {
      case "BRAND_AWARENESS":
        optimizationTargetType = "MAX_CLICK";
        costType = "CPC";
        objectiveType = "ENGAGEMENT";
        break;
      case "LEAD_GENERATION":
        if (data.type == "SPONSORED_UPDATES" || data.type == undefined) {
          optimizationTargetType = "MAX_LEAD";
        }
        costType = "CPM";
        objectiveType = "LEAD_GENERATION";
        break;
      case "VIDEO_VIEWS":
        optimizationTargetType = "MAX_CLICK";
        costType = "CPC";
        objectiveType = "ENGAGEMENT";
        break;
      case "WEBSITE_VISITS":
        optimizationTargetType = "MAX_CLICK";
        costType = "CPM";
        objectiveType = "WEBSITE_VISIT";
        break;
      default:
        throw new Error("Invalid objective type");
    }

    const suggestedBidding = await this.getSuggestedBidding({
      adAccountUrn: data.adAccount,
      bidType: costType as any,
      objectiveType: objectiveType,
      campaignType: data.type ?? "SPONSORED_UPDATES",
      targetingCriteria: data.audienceTargets,
      optimizationTargetType: optimizationTargetType,
      dailyBudget: dailyBudget,
      currency: "USD",
    });
    const firstElement = suggestedBidding.elements[0];

    if (!firstElement) {
      throw new Error("No suggested bid");
    }

    let actualBid: number | undefined = undefined;
    const suggestedBid = Number(firstElement.suggestedBid.default.amount);
    if (dailyBudget < suggestedBid) {
      actualBid = dailyBudget - 0.01;
    } else {
      actualBid = suggestedBid;
    }
    if (!actualBid) {
      throw new Error("No actual bid");
    }
    if (actualBid > dailyBudget) {
      throw new Error("Actual bid is greater than daily budget");
    }
    if (actualBid < Number(firstElement.suggestedBid.min.amount)) {
      throw new Error("Actual bid is less than minimum bid");
    }
    if (actualBid > Number(firstElement.suggestedBid.max.amount)) {
      throw new Error("Actual bid is greater than maximum bid");
    }

    const body = {
      account: `urn:li:sponsoredAccount:${data.adAccount}`,
      campaignGroup: `urn:li:sponsoredCampaignGroup:${data.campaignGroupUrn}`,
      name: data.name,
      offsiteDeliveryEnabled: false,
      connectedTelevisionOnly: false,
      audienceExpansionEnabled: false,
      creativeSelection: "ROUND_ROBIN",
      dailyBudget: {
        amount: dailyBudget.toString(),
        currencyCode: data.currencyCode,
      },
      runSchedule: {
        start: data.startDate.getTime(),
        end: data.endDate ? data.endDate.getTime() : undefined,
      },
      locale: {
        country: "US",
        language: "en",
      },
      unitCost: data.manualBidding
        ? {
            amount: actualBid.toString(),
            currencyCode: data.currencyCode,
          }
        : undefined,
      targetingCriteria: data.audienceTargets,
      costType: costType,
      type: data.type ?? "SPONSORED_UPDATES",
      status: "PAUSED",
      objectiveType: objectiveType,
    };

    console.log(JSON.stringify(body, null, 2));

    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${data.adAccount}/adCampaigns`,
      {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
          "LinkedIn-Version": "202411",
        },
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to create campaign ");
    }
    console.log("res.data", JSON.stringify(res.data, null, 2));
    const headers = res.data.headers;
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No location header");
    }
    return id;
  }

  // ** Update the campaign target criteria in linkedin using linkedin api
  // This function currently only updates the targeting criteria -- should be updated to update other fields as well
  async updateCampaign(data: {
    adAccountId: string;
    campaignId: string;
    audienceTargets?: TargetingCriteria;
  }): Promise<unknown> {
    if (!data.adAccountId || !data.campaignId) {
      throw new Error("Ad account urn and campaign urn are required");
    }

    if (!data.audienceTargets) {
      throw new Error("Audience targets are required");
    }

    const body = {
      patch: {
        $set: {
          targetingCriteria: data.audienceTargets,
        },
      },
    };

    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAccounts/${data.adAccountId}/adCampaigns/${data.campaignId}`,
      {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
          "LinkedIn-Version": "202411",
        },
      },
    );

    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to update campaign");
    }

    return res.data;
  }
  async uploadImage(data: {
    linkedInOrganizationId: string;
    body: Readable;
    type: "image" | "video";
    fileSizeBytes: number;
  }) {
    const url = data.type == "image" ? "images" : "videos";
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/${url}?action=initializeUpload`,
      {
        method: "POST",
        body: JSON.stringify({
          initializeUploadRequest: {
            owner: `urn:li:organization:${data.linkedInOrganizationId}`,
            fileSizeBytes:
              data.type == "video" ? data.fileSizeBytes : undefined,
            uploadCaptions: data.type == "video" ? false : undefined,
            uploadThumbnail: data.type == "video" ? false : undefined,
          },
        }),
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get image upload url");
    }
    let imageUrn: string | undefined = undefined;
    let uploadUrl: string | undefined = undefined;
    if (data.type == "image") {
      const resData = res.data as {
        value: {
          uploadUrlExpiresAt: number;
          uploadUrl: string;
          image: string;
        };
      };
      imageUrn = resData.value.image;
      uploadUrl = resData.value.uploadUrl;

      const uploadRes = await this.callFetchToLinkedInApiResOnly(
        uploadUrl,
        {
          method: "PUT",
          body: data.body,
          duplex: "half",
        },
        1,
        {
          "Content-Type": `${data.type}/*`,
        },
      );
      if (!uploadRes.success) {
        console.log(uploadRes.error);
        throw new Error("Failed to upload image");
      }

      let imageStatus = "";

      while (imageStatus !== "AVAILABLE") {
        const docRes = await this.callFetchToLinkedInApi(
          `https://api.linkedin.com/rest/images/${imageUrn.replaceAll(":", "%3A")}`,
          {
            method: "GET",
            headers: {
              "X-Restli-Protocol-Version": "2.0.0",
            },
          },
          undefined,
          {
            "X-Restli-Protocol-Version": "2.0.0",
          },
        );

        if (!docRes.success) {
          console.log(JSON.stringify(docRes.error, null, 2));
          throw new Error("Failed to get document");
        }

        const docResJson = docRes.data as {
          status: string;
        };
        imageStatus = docResJson.status;
        if (imageStatus == "PROCESSING_FAILED") {
          console.log("THE ERROR", JSON.stringify(docRes.data, null, 2));
          throw "DOCUMENT PROCESSING FAILED";
        }
        console.log(`Document status: ${imageStatus}`);
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
      return imageUrn;
    } else if (data.type == "video") {
      console.log(JSON.stringify(res.data, null, 2));
      const resData = res.data as {
        value: {
          video: string;
          uploadInstructions: {
            uploadUrl: string;
            lastByte: number;
            firstByte: number;
          }[];
          uploadToken: string;
        };
      };
      imageUrn = resData.value.video;
      uploadUrl = "";
      const etags = await splitAndUploadChunks(
        resData.value.uploadInstructions,
        data.body,
      );

      const uploadRes = await this.callFetchToLinkedInApiResOnly(
        "https://api.linkedin.com/rest/videos?action=finalizeUpload",
        {
          method: "POST",
          body: JSON.stringify({
            finalizeUploadRequest: {
              video: imageUrn,
              uploadedPartIds: etags,
              uploadToken: resData.value.uploadToken,
            },
          }),
        },
      );
      if (!uploadRes.success) {
        console.log(JSON.stringify(uploadRes.error, null, 2));
        throw new Error("Failed to upload image");
      }
      let status = "PROCESSING";
      while (status !== "AVAILABLE") {
        const videoRes = await this.callFetchToLinkedInApi(
          `https://api.linkedin.com/rest/videos/${imageUrn.replaceAll(":", "%3A")}`,
        );
        if (!videoRes.success) {
          console.log(JSON.stringify(videoRes.error, null, 2));
          throw new Error("Failed to get video");
        }
        const videoData = videoRes.data as {
          status: string;
        };
        status = videoData.status;
        console.log(`Video status: ${status}`);
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
      return imageUrn;
    }

    if (!uploadUrl || !imageUrn) {
      throw new Error("No upload url or image urn");
    }
    throw new Error("No upload url or image urn");
  }

  async uploadDocument(data: {
    linkedInOrganizationId: string;
    body: Readable;
    type: "document";
    fileSizeBytes: number;
  }) {
    const url = "document";
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/documents?action=initializeUpload`,
      {
        method: "POST",
        body: JSON.stringify({
          initializeUploadRequest: {
            owner: `urn:li:organization:${data.linkedInOrganizationId}`,
          },
        }),
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
        },
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get document upload url");
    }
    let documentUrn: string | undefined = undefined;
    let uploadUrl: string | undefined = undefined;
    const resData = res.data as {
      value: {
        uploadUrlExpiresAt: number;
        uploadUrl: string;
        document: string;
      };
    };
    documentUrn = resData.value.document;
    uploadUrl = resData.value.uploadUrl;

    if (!uploadUrl || !documentUrn) {
      throw new Error("No upload url or document urn");
    }

    console.log("HERE");

    console.log("UPLOADING DOC");
    const documentBuffer = await new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];
      data.body.on("data", (chunk) => chunks.push(Buffer.from(chunk)));
      data.body.on("end", () => resolve(Buffer.concat(chunks)));
      data.body.on("error", (err) => reject(err));
    });
    const uploadRes = await fetch(uploadUrl, {
      method: "PUT",
      body: documentBuffer,
      duplex: "half",
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
      },
    });
    console.log("BACK IN UPLOAD DOC");
    console.log(JSON.stringify(uploadRes.headers.entries(), null, 2));
    console.log("CONTENT LENGTH", uploadRes.headers.get("content-length"));
    console.log(uploadRes.status);

    let docStatus = "";

    while (docStatus !== "AVAILABLE") {
      const docRes = await this.callFetchToLinkedInApi(
        `https://api.linkedin.com/rest/documents/${documentUrn.replaceAll(":", "%3A")}`,
        {
          method: "GET",
          headers: {
            "X-Restli-Protocol-Version": "2.0.0",
          },
        },
        undefined,
        {
          "X-Restli-Protocol-Version": "2.0.0",
        },
      );

      if (!docRes.success) {
        console.log(JSON.stringify(docRes.error, null, 2));
        throw new Error("Failed to get document");
      }

      const docResJson = docRes.data as {
        status: string;
      };
      docStatus = docResJson.status;
      if (docStatus == "PROCESSING_FAILED") {
        return null;
      }
      console.log(`Document status: ${docStatus}`);
      await new Promise((resolve) => setTimeout(resolve, 10000));
    }

    return documentUrn;
  }

  async createInlineCreative(data: {
    adAccount: string;
    campaign: string;
    imageUrn: string;
    commentary: string;
    headline: string;
    linkedInOrgId: string;
    destinationUrl?: string;
    adFormUrn?: string;
    adName?: string;
  }) {
    console.log(data);
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAccounts/${data.adAccount}/creatives?action=createInline`,
      {
        method: "POST",
        body: JSON.stringify({
          creative: {
            content: data.imageUrn.startsWith("urn:li:document")
              ? {
                  documentAd: {
                    gatedLeadgenPreviewPageCount: 3,
                  },
                }
              : undefined,
            inlineContent: {
              post: {
                adContext: {
                  dscAdAccount: `urn:li:sponsoredAccount:${data.adAccount}`,
                  dscStatus: "ACTIVE",
                  dscName: data.adName,
                  desAdType: data.imageUrn.startsWith("urn:li:video:")
                    ? "VIDEO"
                    : data.imageUrn.startsWith("urn:li:document")
                      ? "NATIVE_DOCUMENT"
                      : "STANDARD",
                },
                author: `urn:li:organization:${data.linkedInOrgId}`,
                commentary: data.commentary,
                visibility: "PUBLIC",
                lifecycleState: "PUBLISHED",
                isReshareDisabledByAuthor: false,
                ...(!data.adFormUrn
                  ? data.imageUrn.startsWith("urn:li:video:") ||
                    data.imageUrn.startsWith("urn:li:document")
                    ? {
                        content: {
                          media: {
                            title: data.headline,
                            id: data.imageUrn,
                          },
                        },
                        contentCallToActionLabel: data.imageUrn.startsWith(
                          "urn:li:video:",
                        )
                          ? "LEARN_MORE"
                          : undefined,
                        contentLandingPage: data.imageUrn.startsWith(
                          "urn:li:video:",
                        )
                          ? data.destinationUrl
                          : undefined,
                      }
                    : {
                        contentLandingPage: data.destinationUrl,
                        content: {
                          article: {
                            title: data.headline,
                            thumbnail: data.imageUrn,
                            source: data.destinationUrl,
                          },
                        },
                        contentCallToActionLabel: "LEARN_MORE",
                      }
                  : {
                      content: {
                        media: {
                          title: data.imageUrn.startsWith("urn:li:document")
                            ? data.headline
                            : undefined,
                          id: data.imageUrn,
                        },
                      },
                    }),
              },
            },
            campaign: `urn:li:sponsoredCampaign:${data.campaign}`,
            intendedStatus: "ACTIVE",
            name: data.adName ?? "LinkedIn AD",
            ...(data.adFormUrn && {
              leadgenCallToAction: {
                destination: `urn:li:adForm:${data.adFormUrn}`,
                label: data.imageUrn.startsWith("urn:li:document")
                  ? "UNLOCK_FULL_DOCUMENT"
                  : "SIGN_UP",
              },
            }),
          },
        }),
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to create ad");
    }
    return res.data as {
      value: {
        creative: string;
      };
    };
  }

  async updateCampaignGroupEndDate(
    campaignGroup: string,
    adAccount: string,
    endDate: Date,
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaignGroups/${campaignGroup}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              runSchedule: { end: endDate.getTime() },
            },
          },
        }),
      },
      1,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
        "X-Restli-Method": "PARTIAL_UPDATE",
      },
    );

    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to patch campaign group");
    }
  }

  async updateCampaignGroupStatus(
    campaignGroup: string,
    adAccount: string,
    status: "ACTIVE" | "PAUSED",
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaignGroups/${campaignGroup}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              status: status,
            },
          },
        }),
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to update campaign group status");
    }
  }

  async setCampaignUnitCost(
    adAccountUrn: number,
    campaignUrn: string,
    unitCost: number,
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccountUrn}/adCampaigns/${campaignUrn}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              unitCost: {
                amount: unitCost.toString(),
                currencyCode: "USD",
              },
            },
          },
        }),
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to set campaign unit cost");
    }
  }

  async setCampaignCostType(
    adAccountUrn: number,
    campaignUrn: string,
    costType: "CPC" | "CPM" | "CPV",
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccountUrn}/adCampaigns/${campaignUrn}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              costType,
            },
          },
        }),
      },
      1,
      {
        "X-Restli-Protocol-Version": "2.0.0",
        "LinkedIn-Version": "202411",
        "X-Restli-Method": "PARTIAL_UPDATE",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to set campaign cost type");
    }
  }

  async setCampaignToManualBidding(adAccountUrn: number, campaignUrn: string) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccountUrn}/adCampaigns/${campaignUrn}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              optimizationTargetType: "NONE",
            },
          },
        }),
      },
      1,
      {
        "X-Restli-Protocol-Version": "2.0.0",
        "LinkedIn-Version": "202411",
        "X-Restli-Method": "PARTIAL_UPDATE",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to set campaign to manual bidding");
    }
  }

  async updateCampaignDailyBudget(
    adAccount: string,
    campaign: string,
    dailyBudget: number,
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaigns/${campaign}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              dailyBudget: {
                amount: dailyBudget.toString(),
                currencyCode: "USD",
              },
            },
          },
        }),
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to update campaign budget");
    }
  }

  async updateCampaignBudget(
    adAccount: string,
    campaign: string,
    budget: number,
    dailyBudget?: number,
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaigns/${campaign}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              totalBudget: {
                amount: budget.toString(),
                currencyCode: "USD",
              },
              dailyBudget: dailyBudget
                ? {
                    amount: dailyBudget.toString(),
                    currencyCode: "USD",
                  }
                : undefined,
            },
          },
        }),
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to update campaign budget");
    }
  }

  async updateCampaignEndDate(
    adAccount: string,
    campaign: string,
    startDateEpochMs: number,
    endDateEpochMs: number,
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaigns/${campaign}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              runSchedule: { start: startDateEpochMs, end: endDateEpochMs },
            },
          },
        }),
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to update campaign end date");
    }
    return { success: true };
  }

  async updateCampaignStatus(
    adAccount: string,
    campaign: string,
    status: "ACTIVE" | "PAUSED",
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaigns/${campaign}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              status: status,
            },
          },
        }),
      },
    );

    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to update campaign status");
    }
  }

  async updateAdStatus(
    adAccount: string,
    ad: string,
    status: "ACTIVE" | "PAUSED",
  ) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/creatives/${ad}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              intendedStatus: status,
            },
          },
        }),
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to update ad status");
    }
  }
  /*
  "inlineContent": {
      "post": {
        "adContext": {
          "dscAdAccount": "urn:li:sponsoredAccount:*********",
          "dscStatus": "ACTIVE",
          "dscName" : "legacy name field"
        },
        "author": "urn:li:organization:5803528",
        "commentary": "commentary about the post",
        "visibility": "PUBLIC",
        "lifecycleState": "PUBLISHED",
        "isReshareDisabledByAuthor": false,
        "content": {
          "media": {
            "title": "Title of the video",
            "id": "urn:li:video:C5510AQEdAeAosOmbOw"
          }
        }
      }
    },
    "campaign": "urn:li:sponsoredCampaign:*********",
    "intendedStatus": "ACTIVE",
    "name": "new name field"
  }*/

  async createCampaignGroup(data: {
    adAccount: string;
    name: string;
    startDate: Date;
    endDate?: Date;
    status: "ACTIVE" | "DRAFT";
    objectiveType:
      | "BRAND_AWARENESS"
      | "ENGAGEMENT"
      | "JOB_APPLICANTS"
      | "LEAD_GENERATION"
      | "WEBSITE_CONVERSIONS"
      | "WEBSITE_VISITS"
      | "VIDEO_VIEWS"
      | null;
  }) {
    let objectiveType = undefined;

    switch (data.objectiveType) {
      case "BRAND_AWARENESS":
        objectiveType = "ENGAGEMENT";
        break;
      case "LEAD_GENERATION":
        objectiveType = "LEAD_GENERATION";
        break;
      case "VIDEO_VIEWS":
        objectiveType = "ENGAGEMENT";
        break;
      case "WEBSITE_VISITS":
        objectiveType = "WEBSITE_VISIT";
        break;
      default:
        throw new Error("Invalid objective type");
    }

    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${data.adAccount}/adCampaignGroups`,
      {
        method: "POST",
        body: JSON.stringify({
          account: `urn:li:sponsoredAccount:${data.adAccount}`,
          name: data.name,
          status: data.status,
          runSchedule: {
            start: data.startDate.getTime(),
            end: data.endDate ? data.endDate.getTime() : undefined,
          },
          ...(objectiveType && { objectiveType: objectiveType }),
        }),
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to create campaign group");
    }
    const headers = res.data.headers;
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No location header");
    }
    return id;
  }

  async callFetchToLinkedInApi(
    path: string,
    init?: RequestInit | undefined,
    attempt = 1,
    additionalHeaders: Record<string, string> = {},
  ): Promise<
    | {
        success: false;
        error: { message: string; errorCode: string }[];
      }
    | { success: true; data: unknown }
  > {
    if (!this.accessToken) {
      await this.authenticate();
    }

    const res = await fetch(path, {
      ...init,
      headers: {
        "LinkedIn-Version": "202409",
        Authorization: `Bearer ${this.accessToken}`,
        "Content-Type": "application/json",
        ...additionalHeaders,
      },
    });

    if (!res.ok) {
      if (res.status === 401 && attempt < 2) {
        await this.authenticate();
        return this.callFetchToLinkedInApi(path, init, attempt + 1);
      }
      console.log("ERROR");
      console.log("ERROR RESPONSE", JSON.stringify(res, null, 2));
      return {
        success: false,
        error: (await res.json()) as { message: string; errorCode: string }[],
      };
    }
    try {
      const json = await res.json();

      return { success: true, data: json };
    } catch {
      return { success: true, data: {} };
    }
  }

  async callFetchToLinkedInApiResOnly(
    path: string,
    init?: RequestInit | undefined,
    attempt = 1,
    additionalHeaders: Record<string, string> = {},
  ): Promise<
    | {
        success: false;
        error: { message: string; errorCode: string }[];
      }
    | { success: true; data: Response }
  > {
    if (!this.accessToken) {
      await this.authenticate();
    }

    const res = await fetch(path, {
      ...init,
      headers: {
        "LinkedIn-Version": "202409",
        Authorization: `Bearer ${this.accessToken}`,
        "Content-Type": "application/json",
        ...additionalHeaders,
      },
    });
    console.log("REs", res);

    console.log(JSON.stringify(res, null, 2));

    if (!res.ok) {
      if (res.status === 401 && attempt < 2) {
        await this.authenticate();
        return this.callFetchToLinkedInApiResOnly(path, init, attempt + 1);
      }
      console.log("ERROR");
      console.log("ERROR RESPONSE", res);
      return {
        success: false,
        error: (await res.json()) as { message: string; errorCode: string }[],
      };
    }
    console.log("NO ERROR");
    return { success: true, data: res };
  }

  async getCampaign(adAccount: number, campaignUrn: number) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaigns?ids=List(${campaignUrn})`,
      undefined,
      undefined,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get campaign");
    }
    console.log("getCampaign::res.data", JSON.stringify(res.data, null, 2));
    const resultData = res.data as {
      results: Record<
        string,
        {
          account: string;
          associatedEntity: string;
          audienceExpansionEnabled: boolean;
          campaignGroup: string;
          changeAuditStamps: {
            created: {
              actor: string;
              time: number;
            };
            lastModified: {
              actor: string;
              time: number;
            };
          };
          connectedTelevisionOnly: boolean;
          costType: string;
          creativeSelection: string;
          dailyBudget: {
            amount: string;
            currencyCode: string;
          };
          format: string;
          id: number;
          locale: {
            country: string;
            language: string;
          };
          name: string;
          offsiteDeliveryEnabled: boolean;
          optimizationTargetType: string;
          runSchedule: {
            end: number;
            start: number;
          };
          servingStatuses: string[];
          objectiveType: string;
          status: string;
          storyDeliveryEnabled: boolean;
          targetingCriteria: {
            include: {
              and: {
                or: Record<string, string[]>;
              }[];
            };
          };
          test: boolean;
          totalBudget: {
            amount: string;
            currencyCode: string;
          };
          type: string;
          unitCost: {
            amount: string;
            currencyCode: string;
          };
          version: {
            versionTag: string;
          };
        }
      >;
    };
    if (!resultData.results[campaignUrn]) {
      throw new Error("Campaign not found");
    }
    return resultData.results[campaignUrn];
  }

  async getCampaigns(adAccount: number, campaignUrns: number[]) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaigns?ids=List(${campaignUrns.join(",")})`,
      undefined,
      undefined,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get campaigns");
    }
    return res.data as {
      results: Record<
        string,
        {
          account: string;
          associatedEntity: string;
          audienceExpansionEnabled: boolean;
          campaignGroup: string;
          changeAuditStamps: {
            created: {
              actor: string;
              time: number;
            };
            lastModified: {
              actor: string;
              time: number;
            };
          };
          connectedTelevisionOnly: boolean;
          costType: string;
          creativeSelection: string;
          dailyBudget: {
            amount: string;
            currencyCode: string;
          };
          format: string;
          id: number;
          locale: {
            country: string;
            language: string;
          };
          name: string;
          offsiteDeliveryEnabled: boolean;
          optimizationTargetType: string;
          runSchedule: {
            end: number;
            start: number;
          };
          servingStatuses: string[];
          status: string;
          storyDeliveryEnabled: boolean;
          targetingCriteria: {
            include: {
              and: {
                or: Record<string, string[]>;
              }[];
            };
          };
          test: boolean;
          totalBudget: {
            amount: string;
            currencyCode: string;
          };
          type: string;
          unitCost: {
            amount: string;
            currencyCode: string;
          };
          version: {
            versionTag: string;
          };
        }
      >;
    };
  }

  async getCampaignGroup(adAccount: string, campaignGroupUrn: string) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaignGroups/${campaignGroupUrn}`,
      undefined,
      undefined,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get campaign group");
    }
    return res.data;
  }

  async getCampaignGroups(adAccount: string, campaignGroupUrns: string[]) {
    const listOfCampaignGroupUrns = `(id:(values:List(${campaignGroupUrns.map((urn) => `${urn}`.replaceAll(":", "%3A")).join(",")})))`;

    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/adCampaignGroups?q=search&search=${listOfCampaignGroupUrns}`,
      undefined,
      undefined,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.error("Failed to get campaign groups", res.error);
      throw new Error("Failed to get campaign groups");
    }
    return res.data;
  }

  async updateAdCopyPost(
    postUrn: string,
    title: string,
    description: string,
    body: string,
  ) {
    const posts = await this.getPostsBatch([postUrn]);
    const post = posts.results[postUrn];
    if (!post) {
      throw new Error("Post not found");
    }
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/posts/${postUrn}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              commentary: body,
            },
          },
        }),
      },
      1,
      {
        "X-Restli-Method": "PARTIAL_UPDATE",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to update ad copy post");
    }
    const postsAfter = await this.getPostsBatch([postUrn]);
    const postAfter = postsAfter.results[postUrn];
    if (!postAfter) {
      throw new Error("Post not found");
    }
    console.log("postAfter", postAfter);
  }
  async getPostsBatch(postUrns: string[]) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/posts?ids=List(${postUrns
        .map((urn) =>
          (urn.startsWith("urn:li:share:")
            ? urn
            : `urn:li:share:${urn}`
          ).replaceAll(":", "%3A"),
        )
        .join(",")})`,
      undefined,
      undefined,
      {
        "X-RestLi-Method": "BATCH_GET",
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to batch get posts");
    }
    return res.data as {
      errors: Record<string, unknown>;
      results: Record<
        string,
        {
          adContext: {
            dscAdAccount: string;
            dscAdType: string;
            dscName: string;
            dscStatus: string;
            isDsc: boolean;
          };
          author: string;
          commentary: string;
          content: {
            article: {
              source: string;
              thumbnail: string;
              title: string;
            };
          };
          contentCallToActionLabel: string;
          contentLandingPage: string;
          createdAt: number;
          distribution: {
            feedDistribution: string;
            thirdPartyDistributionChannels: string[];
          };
          id: string;
          isReshareDisabledByAuthor: boolean;
          lastModifiedAt: number;
          lifecycleState: string;
          lifecycleStateInfo: {
            isEditedByAuthor: boolean;
          };
          publishedAt: number;
          visibility: string;
        }
      >;
    };
  }

  async getAdCreatives(adAccount: string, adIds: string[]) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/creatives?ids=List(${adIds
        .map((id) =>
          id.startsWith("urn:li:sponsoredCreative:")
            ? id
            : `urn:li:sponsoredCreative:${id}`,
        )
        .map((urn) => urn.replaceAll(":", "%3A"))
        .join(",")})`,
      undefined,
      undefined,
      {
        "X-RestLi-Method": "BATCH_GET",
        "X-Restli-Protocol-Version": "2.0.0",
        "LinkedIn-Version": "202411",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get ad creatives");
    }

    return res.data as {
      results: Record<
        string,
        {
          servingHoldReasons: string[];
          lastModifiedAt: number;
          lastModifiedBy: string;
          content: {
            reference: string;
          };
          createdAt: number;
          isTest: boolean;
          createdBy: string;
          review: {
            status: string;
          };
          isServing: boolean;
          name: string;
          campaign: string;
          id: string;
          intendedStatus: string;
          account: string;
        }
      >;
    };
  }

  async getAdCreative(adAccount: string, adUrn: string) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAccounts/${adAccount}/creatives/${adUrn.replaceAll(":", "%3A")}`,
      undefined,
      undefined,
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get ad creatives");
    }
    return res.data as {
      review: { status: string };
      intendedStatus: "ACTIVE" | "PAUSED" | "DRAFT";
    };
  }

  async getCampaignAnalytics(
    campaignUrns: string[],
    startDate: Date,
    endDate?: Date,
    timeGranularity: "ALL" | "DAILY" | "MONTHLY" | "YEARLY" = "ALL",
  ) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAnalytics?q=analytics` +
        `&pivot=CAMPAIGN&timeGranularity=${timeGranularity}` +
        `&dateRange=(start:(year:${startDate.getFullYear()},month:${startDate.getMonth() + 1},day:${startDate.getDate()})${endDate ? `,end:(year:${endDate.getFullYear()},month:${endDate.getMonth() + 1},day:${endDate.getDate()})` : ""})` +
        `&campaigns=List(${campaignUrns.map((urn) => `urn:li:sponsoredCampaign:${urn}`.replaceAll(":", "%3A")).join(",")})` +
        `&fields=` +
        `impressions,clicks,costInUsd,pivotValues,costInLocalCurrency,` +
        `dateRange,oneClickLeads,externalWebsiteConversions,landingPageClicks,` +
        `sends,opens,actionClicks,oneClickLeads,oneClickLeadFormOpens,totalEngagements,` +
        `videoCompletions,videoFirstQuartileCompletions,videoMidpointCompletions,` +
        `videoThirdQuartileCompletions,videoStarts`,
      undefined,
      undefined,
      {
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get analytics for campaigns");
    }
    console.log("res.data", res.data);
    return res.data as {
      elements: {
        actionClicks: number;
        adUnitClicks: number;
        clicks: number;
        costInLocalCurrency: string;
        costInUsd: string;
        landingPageClicks: number;
        oneClickLeadFormOpens: number;
        videoCompletions: number;
        videoFirstQuartileCompletions: number;
        videoMidpointCompletions: number;
        videoThirdQuartileCompletions: number;
        videoViews: number;
        videoStarts: number;
        dateRange: {
          end: {
            day: number;
            month: number;
            year: number;
          };
          start: {
            day: number;
            month: number;
            year: number;
          };
        };
        externalWebsiteConversions: number;
        impressions: number;
        pivotValues: string[];
        totalEngagements: number;
        oneClickLeads: number;
        sends: number;
        opens: number;
      }[];
      paging: {
        count: number;
        links: unknown[];
        start: number;
      };
    };
  }

  async getCampaignGroupAnalytics(
    campaignGroupUrns: string[],
    startDate: Date,
    endDate: Date,
  ) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAnalytics?q=analytics&pivot=CAMPAIGN_GROUP` +
        `&timeGranularity=ALL` +
        `&dateRange=(start:(year:${startDate.getFullYear()},month:${startDate.getMonth() + 1},day:${startDate.getDate()}),end:(year:${endDate.getFullYear()},month:${endDate.getMonth() + 1},day:${endDate.getDate()}))` +
        `&campaignGroups=List(${campaignGroupUrns.map((urn) => `${urn}`.replaceAll(":", "%3A")).join(",")})` +
        `&fields=impressions,clicks,oneClickLeads,totalEngagements,costInUsd,oneClickLeadFormOpens,pivotValues`,
      undefined,
      undefined,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );

    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error(
        "[linkedInClient:getCampaignGroupAnalytics] Failed to get analytics for campaign group",
      );
    }

    return res.data as {
      elements: {
        clicks: number;
        impressions: number;
        costInUsd: string;
        totalEngagements: number;
        oneClickLeads: number;
        pivotValues: string[];
      }[];
    };
  }

  async getAdAnalytics(
    adUrns: string[],
    startDate: Date,
    endDate?: Date,
    timeGranularity: "ALL" | "DAILY" | "MONTHLY" | "YEARLY" = "ALL",
  ) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adAnalytics?q=analytics` +
        `&pivot=CAMPAIGN&timeGranularity=${timeGranularity}` +
        `&dateRange=(start:(year:${startDate.getFullYear()},month:${startDate.getMonth() + 1},day:${startDate.getDate()})${endDate ? `,end:(year:${endDate.getFullYear()},month:${endDate.getMonth() + 1},day:${endDate.getDate()})` : ""})` +
        `&creatives=List(${adUrns.map((urn) => `urn:li:sponsoredCreative:${urn}`.replaceAll(":", "%3A")).join(",")})` +
        `&fields=impressions,clicks,costInUsd,pivotValues,dateRange,oneClickLeads,sends,opens`,
      undefined,
      undefined,
      {
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get analytics for campaigns");
    }
    return res.data as {
      elements: {
        actionClicks: number;
        adUnitClicks: number;
        clicks: number;
        costInLocalCurrency: string;
        costInUsd: string;
        videoViews: number;
        dateRange: {
          end: {
            day: number;
            month: number;
            year: number;
          };
          start: {
            day: number;
            month: number;
            year: number;
          };
        };
        externalWebsiteConversions: number;
        impressions: number;
        pivotValues: string[];
        videoStarts: number;
        oneClickLeads: number;
        sends: number;
        opens: number;
      }[];
      paging: {
        count: number;
        links: unknown[];
        start: number;
      };
    };
  }

  async getValidTargetingCriteria() {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/v2/adTargetingFacets`,
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get valid targeting criteria");
    }
    console.log("getValidTargetingCriteria", JSON.stringify(res.data, null, 2));
    const rawData = res.data as {
      elements: {
        entityTypes: string[];
        facetName: string;
        $URN: string;
        availableEntityFinders: string[];
      }[];
      paging: {
        count: number;
        links: unknown[];
        start: number;
      };
    };
    const validTargetingFacets = rawData.elements.map(
      (element) => element.$URN,
    );
    return validTargetingFacets;
  }

  async getTargetingFacetEntities(facetUrn: string) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adTargetingEntities?q=adTargetingFacet&queryVersion=QUERY_USES_URNS&facet=${facetUrn.replaceAll(":", "%3A")}`,
      undefined,
      undefined,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get targeting facet values");
    }
    return res.data;
  }

  // Ad Budgeting APIs
  async getSuggestedBidding({
    adAccountUrn,
    bidType = "CPC",
    optimizationTargetType,
    objectiveType,
    campaignType = "SPONSORED_UPDATES",
    targetingCriteria,
    countryCode,
    dailyBudget,
    currency = "USD",
    matchType = "EXACT",
  }: {
    adAccountUrn: string;
    bidType: "CPM" | "CPC" | "CPV";
    optimizationTargetType?: string;
    objectiveType: string | null | undefined;
    campaignType: "TEXT_AD" | "SPONSORED_UPDATES" | "SPONSORED_INMAILS";
    targetingCriteria: {
      include: {
        and: {
          or: Record<string, string[] | undefined>;
        }[];
      };
    };
    countryCode?: string;
    dailyBudget?: number;
    currency?: string;
    matchType?: "EXACT" | "AUDIENCE_EXPANDED";
  }) {
    const queryParams = new URLSearchParams({
      q: "criteriaV2",
      account: adAccountUrn.startsWith("urn:li:sponsoredAccount:")
        ? adAccountUrn
        : `urn:li:sponsoredAccount:${adAccountUrn}`,
      bidType,
      campaignType,
      matchType,
    });

    if (countryCode) queryParams.append("countryCode", countryCode);
    if (objectiveType) {
      if (objectiveType === "WEBSITE_VISITS") {
        queryParams.append("objectiveType", "WEBSITE_VISIT");
      } else {
        queryParams.append("objectiveType", objectiveType);
      }
    }
    if (optimizationTargetType)
      queryParams.append("optimizationTargetType", optimizationTargetType);

    const targetingCriteriaParam = `(include:(and:List(${targetingCriteria.include.and
      .map((andClause) => {
        const facetKey = Object.keys(andClause.or)[0];
        const values = andClause.or[facetKey ?? ""];
        return `(or:(${encodeURIComponent(facetKey ?? "")}:List(${values
          ?.map((v) =>
            v
              .replaceAll(":", "%3A")
              .replaceAll("(", "%28")
              .replaceAll(")", "%29")
              .replaceAll(",", "%2C"),
          )
          .join(",")})))`;
      })
      .join(",")})))`;
    console.log(targetingCriteriaParam);
    // queryParams.append("targetingCriteria", targetingCriteriaParam);

    console.log("LinkedIn pricing API query params", queryParams.toString());

    console.log(
      "WHOLE",
      `https://api.linkedin.com/rest/adBudgetPricing?${queryParams.toString()}&targetingCriteria=${targetingCriteriaParam},dailyBudget=${`(amount:${dailyBudget},currencyCode:${currency})`}`,
    );
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adBudgetPricing?${queryParams.toString()}&targetingCriteria=${targetingCriteriaParam}&dailyBudget=${`(amount:${dailyBudget},currencyCode:${currency})`}`,
      undefined,
      undefined,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );

    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get suggested bidding");
    }

    return res.data as {
      paging: {
        start: number;
        count: number;
        links: unknown[];
      };
      elements: {
        bidLimits: {
          max: {
            currencyCode: string;
            amount: string;
          };
          min: {
            currencyCode: string;
            amount: string;
          };
        };
        suggestedBid: {
          max: {
            currencyCode: string;
            amount: string;
          };
          default: {
            currencyCode: string;
            amount: string;
          };
          min: {
            currencyCode: string;
            amount: string;
          };
        };
        dailyBudgetLimits: {
          max: {
            currencyCode: string;
            amount: string;
          };
          default: {
            currencyCode: string;
            amount: string;
          };
          min: {
            currencyCode: string;
            amount: string;
          };
        };
      }[];
    };
  }

  async getFormLeadResponseById(formLeadResponseId: string) {
    console.log("getFormLeadResponseById", formLeadResponseId);
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/leadFormResponses/${formLeadResponseId}`,
      undefined,
      undefined,
      {
        "LinkedIn-Version": "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get lead form response by ID");
    }

    return res.data as {
      leadType: string;
      owner: {
        sponsoredAccount: string;
      };
      submitter: string;
      versionedLeadGenFormUrn: string;
      leadMetadata: {
        sponsoredLeadMetadata: {
          campaign: string;
        };
      };
      submittedAt: number;
      ownerInfo: {
        sponsoredAccountInfo: {
          name: string;
        };
      };
      testLead: boolean;
      associatedEntity: {
        associatedCreative: string;
      };
      leadMetadataInfo: {
        sponsoredLeadMetadataInfo: {
          campaign: {
            name: string;
            type: string;
            id: string;
          };
        };
      };
      associatedEntityInfo: {
        associatedCreative: {
          intendedStatus: string;
          content: {
            reference: string;
          };
          id: string;
        };
      };
      id: string;
      formResponse: {
        answers: {
          answerDetails: {
            textQuestionAnswer: {
              answer: string;
            };
          };
          questionId: number;
        }[];
        consentResponses: {
          accepted: boolean;
          consentId: number;
        }[];
      };
    };
  }

  async getLeadFormResponses(
    adAccountId: string,
    versionedLeadGenFormUrn?: string,
  ) {
    const adAccountUrnString =
      `urn:li:sponsoredAccount:${adAccountId}`.replaceAll(":", "%3A");

    let url;
    if (versionedLeadGenFormUrn) {
      const cleanedUrnString = versionedLeadGenFormUrn
        .replaceAll(":", "%3A")
        .replaceAll("(", "%28")
        .replaceAll(")", "%29")
        .replaceAll(",", "%2C");
      url = `https://api.linkedin.com/rest/leadFormResponses?owner=(sponsoredAccount:${adAccountUrnString})&q=owner&leadType=(leadType:SPONSORED)&versionedLeadGenFormUrn=${cleanedUrnString}&count=1000`;
    } else {
      url = `https://api.linkedin.com/rest/leadFormResponses?owner=(sponsoredAccount:${adAccountUrnString})&q=owner&leadType=(leadType:SPONSORED)&count=1000`;
    }

    const res = await this.callFetchToLinkedInApi(url, undefined, undefined, {
      LinkedInVersion: "202411",
      "X-Restli-Protocol-Version": "2.0.0", // Required to use LinkedIn API 2.0.
    });

    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get lead form responses");
    }
    // console.log("getLeadFormResponses", JSON.stringify(res.data, null, 2));
    return res.data as {
      paging: {
        start: number;
        count: number;
        links: unknown[];
      };
      elements: {
        id: string;
        leadType: string;
        versionedLeadGenFormUrn: string;
        testLead: boolean;
        owner: {
          sponsoredAccount: string;
        };
        leadMetadataInfo: {
          sponsoredLeadMetadataInfo: {
            campaign: {
              name: string;
              type: string;
              id: string;
            };
          };
        };
        leadMetadata: {
          sponsoredLeadMetadata: {
            campaign: string;
          };
        };
        submittedAt: number;
        formResponse: {
          answers: {
            answerDetails: {
              textQuestionAnswer: {
                answer: string;
              };
            };
            questionId: number;
          }[];
          consentResponses: {
            accepted: boolean;
            consentId: number;
          }[];
        };
      }[];
    };
  }

  async getLeadFormById(adFormId: string) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/leadForms/${adFormId}`,
      undefined,
      undefined,
      {
        LinkedInVersion: "202411",
        "X-Restli-Protocol-Version": "2.0.0", // Required to use LinkedIn API 2.0.
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get lead form");
    }
    return res.data as {
      content: {
        description: {
          localized: {
            en_US: string;
          };
        };
        headline: {
          localized: {
            en_US: string;
          };
        };
        legalInfo: {
          consents: string[];
          legalInfoId: number;
          privacyPolicyUrl: string;
        };
        postSubmissionInfo: {
          callToAction: {
            callToActionLabel: string;
            callToActionTarget: {
              landingPageUrl: string;
            };
          };
          message: {
            localized: {
              en_US: string;
            };
          };
        };
        questions: {
          label: string;
          name: string;
          predefinedField: string;
          question: {
            localized: {
              en_US: string;
            };
          };
          questionDetails: {
            textQuestionDetails: {
              maxResponseLength: number;
            };
          };
          questionId: number;
          responseEditable: boolean;
          responseRequired: boolean;
        }[];
      };
      created: number;
      creationLocale: {
        country: string;
        language: string;
      };
      hiddenFields: string[];
      id: number;
      lastModified: number;
      name: string;
      owner: {
        organization: string;
      };
      reviewInfo?: {
        lastUpdated: number;
        rejectionReasons: string[];
        reviewStatus: string;
      };
      state: "DRAFT" | "PUBLISHED" | "ARCHIVED";
      versionId: number;
    };
  }

  async getLeadForms(adAccountUrn: number) {
    const adAccountUrnString =
      `urn:li:sponsoredAccount:${adAccountUrn}`.replaceAll(":", "%3A");
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/leadForms?owner=(sponsoredAccount:${adAccountUrnString})&q=owner`,
      undefined,
      undefined,
      {
        LinkedInVersion: "202411",
        "X-Restli-Protocol-Version": "2.0.0", // Required to use LinkedIn API 2.0.
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get ad forms");
    }
    return res.data as {
      elements: {
        content: {
          description: {
            localized: {
              en_US: string;
            };
          };
          headline: {
            localized: {
              en_US: string;
            };
          };
          legalInfo: {
            consents: string[];
            legalInfoId: number;
            privacyPolicyUrl: string;
          };
          postSubmissionInfo: {
            callToAction: {
              callToActionLabel: string;
              callToActionTarget: {
                landingPageUrl: string;
              };
            };
            message: {
              localized: {
                en_US: string;
              };
            };
          };
          questions: {
            label: string;
            name: string;
            predefinedField: string;
            question: {
              localized: {
                en_US: string;
              };
            };
            questionDetails: {
              textQuestionDetails: {
                maxResponseLength: number;
              };
            };
            questionId: number;
            responseEditable: boolean;
            responseRequired: boolean;
          }[];
        };
        created: number;
        creationLocale: {
          country: string;
          language: string;
        };
        hiddenFields: string[];
        id: number;
        lastModified: number;
        name: string;
        owner: {
          sponsoredAccount: string;
        };
        reviewInfo: {
          lastUpdated: number;
          rejectionReasons: string[];
          reviewStatus:
            | "PENDING"
            | "APPROVED"
            | "REJECTED"
            | "PREAPPROVED"
            | "AUTO_APPROVED"
            | "NEEDS_REVIEW"
            | "AUTO_REJECTED";
        };
        state: "DRAFT" | "PUBLISHED" | "ARCHIVED";
        versionId: number;
      }[];
      paging: {
        count: number;
        links: string[];
        start: number;
        total: number;
      };
    };
  }

  async getAdSegments(adAccountId: number) {
    const adAccountUrn = `urn:li:sponsoredAccount:${adAccountId}`.replaceAll(
      ":",
      "%3A",
    );
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adSegments?q=accounts&accounts=List(${adAccountUrn})`,
      undefined,
      undefined,
      {
        LinkedInVersion: "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );

    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get ad segments");
    }

    const adSegments = res.data as {
      elements: {
        approximateMemberCount: number;
        created: {
          actor: string;
          time: number;
        };
        name: string;
        versionTag: string;
        id: number;
        lastModified: {
          actor: string;
          time: number;
        };
        type: "MARKET_AUTOMATION" | "BULK" | "RETARGETING";
        account: string;
        status: "ARCHIVED" | "READY";
        description?: string;
      }[];
    };

    return adSegments.elements;
  }

  async getDmpSegments(adAccountId: number) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/dmpSegments?q=account&account=urn:li:sponsoredAccount:${adAccountId}`,
      undefined,
      undefined,
      {
        LinkedInVersion: "202411",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get saved audiences");
    }
    //console.log("dmpSegments", JSON.stringify(res.data, null, 2));
    const dmpSegments = res.data as {
      elements: {
        account: string;
        destinations: {
          audienceSize: number;
          created: number;
          destination: string;
          destinationSegmentId: string;
          lastModified: number;
          matchedCount?: number;
          status: string;
        }[];
        id: number;
        name: string;
        description?: string;
        sourcePlatform: string;
        type: string;
        inputCount?: number;
      }[];
      paging: {
        count: number;
        links: {
          href: string;
          rel: string;
          type: string;
        }[];
        start: number;
        total: number;
      };
    };
    return dmpSegments.elements;
  }

  async getDmpSegmentById(segmentId: number) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/dmpSegments/${segmentId}`,
      undefined,
      undefined,
      {
        LinkedInVersion: "202411",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get saved audience");
    }
    console.log("getSavedAudienceByID", JSON.stringify(res.data, null, 2));
    return res.data as {
      account: string;
      destinations: {
        audienceSize: number;
        created: number;
        destination: string;
        destinationSegmentId: string;
        lastModified: number;
        status: string;
      }[];
      id: number;
      name: string;
      sourcePlatform: string;
      type: string;
    };
  }

  async getAdPageSets(accountUrn: number) {
    const adAccountUrn = `urn:li:sponsoredAccount:${accountUrn}`.replaceAll(
      ":",
      "%3A",
    );
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/v2/adPageSets?q=account&account=${adAccountUrn}`,
      undefined,
      undefined,
      {
        LinkedInVersion: "202411",
      },
    );
    if (!res.success) {
      console.log(res.error);
      throw new Error("Failed to get ad page sets");
    }
    return res.data as {
      elements: {
        id: string;
        name: string;
        status: string;
        account: string;
        created: number;
        lastModified: number;
        pages: {
          id: string;
          name: string;
          vanityName: string;
          logoUrl: string;
        }[];
      }[];
      paging: {
        count: number;
        start: number;
        total: number;
        links: {
          type: string;
          rel: string;
          href: string;
        }[];
      };
    };
  }

  async createAdPageSet(data: {
    accountId: number;
    name: string;
    matchRules: {
      matchType: "EXACT" | "STARTS_WITH" | "CONTAINS";
      matchValue: string;
    }[];
  }) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adPageSets`,
      {
        method: "POST",
        body: JSON.stringify({
          account: `urn:li:sponsoredAccount:${data.accountId}`,
          matchRules: data.matchRules,
          name: data.name,
        }),
      },
      undefined,
      {
        LinkedInVersion: "202411",
      },
    );

    if (!res.success) {
      throw new Error(
        `Failed to create ad page set due to ${JSON.stringify(res.error)}`,
      );
    }

    const headers = res.data.headers;
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No page set ID returned in response headers");
    }

    return id;
  }

  validateDmpSegmentCompanyListCsv(csvContent: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const requiredHeaders = [
      "companyname",
      "companydomain",
      "companywebsite",
      "linkedincompanypageurl",
    ];

    const validHeaders = new Set([
      "companyname",
      "companydomain",
      "companyemaildomain",
      "emaildomain",
      "domain",
      "companywebsite",
      "website",
      "linkedincompanypageurl",
      "stocksymbol",
      "stockticker",
      "ticker",
      "city",
      "ct",
      "state",
      "st",
      "province",
      "pr",
      "companycountry",
      "zipcode",
      "zip",
      "pincode",
      "industry",
      "industrytwo",
      "industrythree",
    ]);

    // Split into lines and remove empty lines
    const lines = csvContent.split("\n").filter((line) => line.trim());

    if (lines.length === 0) {
      errors.push("File is empty");
      return { isValid: false, errors };
    }
    if (!lines[0]) {
      errors.push("File header row is empty");
      return { isValid: false, errors };
    }

    // Validate header row
    const headerRow = lines[0].toLowerCase();
    const headers = headerRow.split(",").map((h) => h.trim());

    // Check for duplicate headers (including aliases)
    const normalizedHeaders = new Set();
    for (const header of headers) {
      if (header === "companywebsite" || header === "website") {
        if (normalizedHeaders.has("companywebsite")) {
          errors.push("Duplicate header found: companywebsite/website");
        }
        normalizedHeaders.add("companywebsite");
      } else if (
        [
          "companydomain",
          "companyemaildomain",
          "emaildomain",
          "domain",
        ].includes(header)
      ) {
        if (normalizedHeaders.has("companydomain")) {
          errors.push("Duplicate header found: companydomain and aliases");
        }
        normalizedHeaders.add("companydomain");
      } else {
        if (normalizedHeaders.has(header)) {
          errors.push(`Duplicate header found: ${header}`);
        }
        normalizedHeaders.add(header);
      }
    }

    // Validate headers are supported
    const unsupportedHeaders = headers.filter((h) => !validHeaders.has(h));
    if (unsupportedHeaders.length > 0) {
      errors.push(
        `Unsupported headers found: ${unsupportedHeaders.join(", ")}`,
      );
    }

    // Check if at least one required header exists
    const hasRequiredHeader = headers.some((h) => requiredHeaders.includes(h));
    if (!hasRequiredHeader) {
      errors.push(
        "Missing required header. At least one of: companyname, companydomain, companywebsite, linkedincompanypageurl is required",
      );
    }

    // Validate number of rows
    if (lines.length < 2) {
      errors.push("File must contain at least one data row");
    }
    if (lines.length > 300001) {
      // 300,000 + header row
      errors.push("File exceeds maximum of 300,000 companies");
    }

    // Validate each data row
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i]?.trim();
      if (!line) continue;

      // Handle quoted values properly
      const values = line.match(/(".*?"|[^",]+)(?=\s*,|\s*$)/g) || [];

      if (values.length !== headers.length) {
        errors.push(
          `Row ${i + 1}: Number of values (${values.length}) does not match number of headers (${headers.length})`,
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  validateDmpSegmentContactListCsv(csvContent: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const validHeaders = new Set([
      "email",
      "googleaid",
      "googleuid",
      "firstname",
      "fn",
      "first",
      "lastname",
      "ln",
      "last",
      "employeecompany",
      "title",
      "jobtitle",
      "country",
    ]);

    const headerAliases: Record<string, string> = {
      fn: "firstname",
      first: "firstname",
      ln: "lastname",
      last: "lastname",
      jobtitle: "title",
    };

    const requiredHeaderSets = [
      ["email"],
      ["googleaid"],
      ["googleuid"],
      ["firstname", "lastname"],
      ["first", "lastname"],
      ["firstname", "last"],
      ["fn", "lastname"],
      ["firstname", "ln"],
      ["fn", "ln"],
    ];

    const lines = csvContent.split("\n");

    if (lines.length === 0) {
      errors.push("File is empty");
      return { isValid: false, errors };
    }

    // Parse and validate headers
    const headerLine = lines[0]?.trim();
    if (!headerLine) {
      errors.push("Missing header row");
      return { isValid: false, errors };
    }

    const headers = headerLine.split(",").map((h) => h.trim().toLowerCase());
    const normalizedHeaders = new Set<string>();

    // Check for duplicate headers
    for (const header of headers) {
      const normalizedHeader = headerAliases[header] ?? header;
      if (normalizedHeaders.has(normalizedHeader)) {
        errors.push(`Duplicate header found: ${header}`);
      }
      normalizedHeaders.add(normalizedHeader);
    }

    // Validate headers are supported
    const unsupportedHeaders = headers.filter((h) => !validHeaders.has(h));
    if (unsupportedHeaders.length > 0) {
      errors.push(
        `Unsupported headers found: ${unsupportedHeaders.join(", ")}`,
      );
    }

    // Check if at least one required header set exists
    const hasRequiredHeaders = requiredHeaderSets.some((set) =>
      set.every(
        (header) =>
          headers.includes(header) ||
          headers.some((h) => headerAliases[h] === header),
      ),
    );

    if (!hasRequiredHeaders) {
      errors.push(
        "Missing required headers. Must include either: email, googleaid, googleuid, or firstname+lastname combination",
      );
    }

    // Validate number of rows
    if (lines.length < 2) {
      errors.push("File must contain at least one data row");
    }
    if (lines.length < 10001) {
      errors.push("LinkedIn recommends at least 10,000 contacts");
    }
    if (lines.length > 300001) {
      // 300,000 + header row
      errors.push("File exceeds maximum of 300,000 contacts");
    }

    // Validate each data row
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i]?.trim();
      if (!line) continue;

      // Handle quoted values properly
      const values = line.match(/(".*?"|[^",]+)(?=\s*,|\s*$)/g) ?? [];

      if (values.length !== headers.length) {
        errors.push(
          `Row ${i + 1}: Number of values (${values.length}) does not match number of headers (${headers.length})`,
        );
      }

      // Validate email format if present
      const emailIndex = headers.indexOf("email");
      if (emailIndex !== -1) {
        const emailHash = values[emailIndex]?.replace(/"/g, "") ?? "";
        if (emailHash && !/^[a-f0-9]{64}$/i.test(emailHash)) {
          errors.push(
            `Row ${i + 1}: Email must be SHA256 hashed (64 character hex string)`,
          );
        }
      }

      // Validate field lengths
      headers.forEach((header, index) => {
        const value = values[index]?.replace(/"/g, "");
        if (value) {
          if (
            (header === "firstname" || header === "fn" || header === "first") &&
            value.length > 35
          ) {
            errors.push(
              `Row ${i + 1}: First name exceeds maximum length of 35 characters`,
            );
          }
          if (
            (header === "lastname" || header === "ln" || header === "last") &&
            value.length > 35
          ) {
            errors.push(
              `Row ${i + 1}: Last name exceeds maximum length of 35 characters`,
            );
          }
          if (header === "employeecompany" && value.length > 50) {
            errors.push(
              `Row ${i + 1}: Company name exceeds maximum length of 50 characters`,
            );
          }
          if (
            (header === "title" || header === "jobtitle") &&
            value.length > 50
          ) {
            errors.push(
              `Row ${i + 1}: Title exceeds maximum length of 50 characters`,
            );
          }
          if (header === "country" && !/^[A-Z]{2}$/.test(value)) {
            errors.push(`Row ${i + 1}: Country must be ISO 2-letter code`);
          }
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
  async generateUploadUrl(adAccount: number) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/dmpSegments?action=generateUploadUrl`,
      {
        method: "POST",
        body: JSON.stringify({
          owner: `urn:li:sponsoredAccount:${adAccount}`,
        }),
      },
      undefined,
      {
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );

    if (!res.success) {
      throw new Error(
        `Failed to generate upload URL due to ${JSON.stringify(res.error)}`,
      );
    }

    return res.data as {
      value: string;
    };
  }

  async uploadAudienceList(uploadUrl: string, csvData: Buffer) {
    const res = await fetch(uploadUrl, {
      method: "POST",
      body: csvData,
      headers: {
        "Content-Type": "text/csv",
      },
    });

    if (!res.ok) {
      throw new Error(
        `Failed to upload audience list. Status: ${res.status} ${res.statusText}`,
      );
    }

    // Extract media ID from location header
    const location = res.headers.get("location");
    if (!location) {
      throw new Error("No location header in response");
    }

    // Location header contains path like: /AAYAAwEvAAQAAQAAAAAAAAv-R5Tyj8t6SZGT6Wjwlp-3Qg.csv
    // Extract just the media ID portion
    const mediaId = location.split("/").pop()?.replace(".csv", "");
    if (!mediaId) {
      throw new Error("Could not parse media ID from location header");
    }

    return mediaId;
  }

  async createDmpSegment(data: {
    adAccount: number;
    name: string;
    type: "COMPANY_LIST_UPLOAD" | "USER_LIST_UPLOAD";
  }) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/dmpSegments`,
      {
        method: "POST",
        body: JSON.stringify({
          account: `urn:li:sponsoredAccount:${data.adAccount}`,
          destinations: [
            {
              destination: "LINKEDIN",
            },
          ],
          name: data.name,
          sourcePlatform: "LIST_UPLOAD",
          type: data.type,
        }),
      },
      undefined,
      {
        LinkedInVersion: "202411",
      },
    );

    if (!res.success) {
      throw new Error(
        `Failed to create DMP segment due to ${JSON.stringify(res.error)}`,
      );
    }

    return res.data as {
      account: string;
      destinations: {
        destination: string;
        status: string;
      }[];
      id: number;
      name: string;
      sourcePlatform: string;
      type: string;
    };
  }

  async getSegments(
    adAccountUrn: string,
    config?: {
      types?: "BULK" | "RETARGETING" | "MARKET_AUTOMATION";
      start?: number;
      count?: number;
    },
  ) {
    const typesQueryParam =
      config?.types !== undefined ? `&types=(value:List(${config.types}))` : "";
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/adSegments?q=accounts&accounts=List(urn%3Ali%3AsponsoredAccount%3A${adAccountUrn})&start=${config?.start ?? 0}&count=${config?.count ?? 100}${typesQueryParam}`,
      {
        method: "GET",
      },
      undefined,
      {
        LinkedInVersion: "202411",
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );

    if (!res.success) {
      throw new Error(
        `Failed to get segments due to ${JSON.stringify(res.error)}`,
      );
    }

    return res.data as {
      paging: {
        start: number;
        count: number;
        total: number;
      };
      elements: {
        approximateMemberCount: number;
        created: {
          actor: string;
          time: number;
        };
        name: string;
        versionTag: string;
        lastModified: {
          actor: string;
          time: number;
        };
        id: number;
        type: "BULK" | "RETARGETING" | "MARKET_AUTOMATION";
        account: string;
        status:
          | "BUILDING"
          | "UPDATING"
          | "READY"
          | "FAILED"
          | "ARCHIVED"
          | "EXPIRED";
        description?: string;
      }[];
    };
  }

  async attachListToDmpSegment(data: {
    dmpSegmentId: number;
    mediaUrn: string;
  }) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/dmpSegments/${data.dmpSegmentId}/listUploads`,
      {
        method: "POST",
        body: JSON.stringify({
          inputFile: data.mediaUrn,
        }),
      },
      undefined,
      {
        LinkedInVersion: "202411",
        "X-Restli-Protocol-Version": "2.0.0", // Required to use LinkedIn API 2.0.
      },
    );

    if (!res.success) {
      throw new Error(
        `Failed to attach list to DMP segment due to ${JSON.stringify(res.error)}`,
      );
    }

    const headers = res.data.headers;
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No location header");
    }
    return id;
  }

  async createAdSegmentSource(data: {
    accountId: number;
    pageSetId: string;
    segmentId: string;
  }) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adSegmentSources/source=urn:li:adPageSet:${data.pageSetId}&segment=urn:li:adSegment:${data.segmentId}`,
      {
        method: "PUT",
        body: JSON.stringify({
          account: `urn:li:sponsoredAccount:${data.accountId}`,
          segment: `urn:li:adSegment:${data.segmentId}`,
          source: `urn:li:adPageSet:${data.pageSetId}`,
        }),
      },
      undefined,
      {
        LinkedInVersion: "202411",
      },
    );

    if (!res.success) {
      throw new Error(
        `Failed to create ad segment source due to ${JSON.stringify(res.error)}`,
      );
    }

    const headers = res.data.headers;
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No segment source ID returned in response headers");
    }

    return id;
  }

  async getSenders(adAccountUrn: string) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/v2/adInMailMemberSenderPermissions/account=${adAccountUrn.replace(":", "%3A")}?q=account`,
      {
        method: "GET",
      },
    );

    if (!res.success) {
      throw new Error(
        `Failed to get senders due to ${JSON.stringify(res.error)}`,
      );
    }

    const data = res.data as {
      elements: {
        member: string;
        state: "APPROVED" | "PENDING" | "REVOKED";
      }[];
    };

    const memberIds = data.elements
      .filter((e) => e.state === "APPROVED")
      .filter((e) => e.member.includes("urn:li:person:"))
      .map((e) => e.member);

    const members: {
      urn: string;
      firstName: string;
      lastName: string;
    }[] = [];

    for (const memberId of memberIds) {
      const memberInfo = await this.callFetchToLinkedInApi(
        `https://api.linkedin.com/v2/people/(id:${memberId.split(":")[3]})`,
        {
          method: "GET",
        },
        undefined,
        {
          "X-Restli-Protocol-Version": "2.0.0",
        },
      );
      if (!memberInfo.success) {
        throw new Error(
          `Failed to get member info due to ${JSON.stringify(memberInfo.error)}`,
        );
      }

      const memberInfoData = memberInfo.data as {
        urn: string;
        firstName: {
          localized: {
            en_US: string;
          };
        };
        lastName: {
          localized: {
            en_US: string;
          };
        };
      };
      console.log(memberInfoData);
      members.push({
        urn: memberId,
        firstName: memberInfoData.firstName.localized.en_US,
        lastName: memberInfoData.lastName.localized.en_US,
      });
    }

    return members;
  }

  async createSponsorConversation(data: {
    adAccountUrn: string;
    subject: string;
  }) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/conversationAds`,
      {
        method: "POST",
        body: JSON.stringify({
          parentAccount: data.adAccountUrn,
          headlineText: data.subject,
        }),
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
          "LinkedIn-Version": "202411",
        },
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to create campaign ");
    }
    console.log(JSON.stringify(res, null, 2));
    const headers = res.data.headers;
    console.log("HEADERS", JSON.stringify(headers, null, 2));
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No location header");
    }
    return id;
  }

  async createSponsorConversationMessage(data: {
    conversationId: string;
    message: TextConversationMessage | LeadGenMessage | ExternalWebsiteMessage;
  }) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/conversationAds/${data.conversationId}/sponsoredMessageContents`,
      {
        method: "POST",
        body: JSON.stringify(data.message),
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
          "LinkedIn-Version": "202411",
        },
      },
    );
    if (!res.success) {
      console.log(
        "MESSAGE",
        JSON.stringify(
          {
            body: data.message,
            url: `https://api.linkedin.com/rest/conversationAds/${data.conversationId}/sponsoredMessageContents`,
          },
          null,
          2,
        ),
      );
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to create campaign ");
    }
    const headers = res.data.headers;
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No location header");
    }
    return id;
  }

  async updateSponsorConversationFirstMessage(data: {
    conversationUrn: string;
    firstMessageContentUrn: string;
  }) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/conversationAds/${data.conversationUrn}`,
      {
        method: "POST",
        body: JSON.stringify({
          patch: {
            $set: {
              firstMessageContent: data.firstMessageContentUrn,
            },
          },
        }),
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
          "LinkedIn-Version": "202411",
        },
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to update conversation");
    }
  }

  async createInmail(data: {
    adAccountUrn: string;
    name: string;
    subject: string;
    senderUrn: string;
    conversationUrn: string;
  }) {
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/inMailContents`,
      {
        method: "POST",
        body: JSON.stringify({
          account: data.adAccountUrn,
          name: data.name,
          subject: data.subject,
          sender: data.senderUrn,
          subContent: {
            guidedReplies: {
              sponsoredConversation: data.conversationUrn,
            },
          },
        }),
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
          "LinkedIn-Version": "202411",
        },
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to create campaign ");
    }
    const headers = res.data.headers;
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No location header");
    }
    return id;
  }

  async createConversationCreative(data: {
    adAccountId: string;
    campaignUrn: string;
    inmailUrn: string;
    adName: string;
    adFormUrn?: string;
  }) {
    console.log(data);
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/adAccounts/${data.adAccountId}/creatives`,
      {
        method: "POST",
        body: JSON.stringify({
          campaign: data.campaignUrn,
          intendedStatus: "DRAFT",
          content: {
            reference: data.inmailUrn,
          },
          ...(data.adFormUrn && {
            leadgenCallToAction: {
              destination: `urn:li:adForm:${data.adFormUrn.split(":")[3]}`,
              label: "SIGN_UP",
            },
          }),
        }),
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
          "LinkedIn-Version": "202411",
        },
      },
    );

    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to create campaign ");
    }
    const headers = res.data.headers;
    const id = headers.get("x-linkedin-id");
    if (!id) {
      throw new Error("No location header");
    }
    return id;
  }

  async createDestinationUrlConversation(data: {
    adAccountUrn: string;
    destinationUrl: string;
    senderUrn: string;
    campaignUrn: string;
    body: string;
    subject: string;
    leadGenButtonText: string;
  }) {
    console.log("CONVO AD DATA", data);
    console.log("CREATING SPONSORED CONVERSATION DEST URL");
    const convoUrn = await this.createSponsorConversation({
      adAccountUrn: data.adAccountUrn,
      subject: data.subject,
    });
    console.log("SPONSORED CONVERSATION URN", convoUrn);
    const convoId = convoUrn.split(":")[3];
    if (!convoId) {
      throw new Error("No conversation ID");
    }

    console.log("CREATING FIRST MESSAGE");
    await new Promise((resolve) => setTimeout(resolve, 10000));
    const firstMessageUrn = await this.createSponsorConversationMessage({
      conversationId: convoUrn,
      message: {
        bodySource: {
          text: data.body,
        },
        nextAction: {
          options: [
            {
              replyType: "EXTERNAL_WEBSITE",
              optionText: data.leadGenButtonText,
              landingPage: data.destinationUrl,
            },
          ],
        },
      },
    });

    console.log("FIRST MESSAGE URN", firstMessageUrn);
    console.log("UPDATING FIRST MESSAGE");
    await new Promise((resolve) => setTimeout(resolve, 10000));
    const convo = await this.getSponsoredConversation(convoUrn);
    console.log("CONVO", JSON.stringify(convo, null, 2));
    const fm = await this.getSponsoredConversationMessage(
      convoUrn,
      firstMessageUrn,
    );
    console.log("FIRST MESSAGE", JSON.stringify(fm, null, 2));
    await this.updateSponsorConversationFirstMessage({
      conversationUrn: convoUrn,
      firstMessageContentUrn: firstMessageUrn,
    });
    console.log("FIRST MESSAGE UPDATED");

    console.log("CREATING INMAIL");
    await new Promise((resolve) => setTimeout(resolve, 10000));
    const inmailUrn = await this.createInmail({
      adAccountUrn: data.adAccountUrn,
      name: data.subject,
      subject: data.subject,
      senderUrn: data.senderUrn,
      conversationUrn: convoUrn,
    });
    console.log("INMAIL URN", inmailUrn);
    const adAccountId = data.adAccountUrn.split(":")[3];
    if (!adAccountId) {
      throw new Error("No ad account ID");
    }
    console.log("CREATING SPONSOREDCREATIVE");
    await new Promise((resolve) => setTimeout(resolve, 10000));
    let res = "";
    let attempts = 0;
    while (attempts < 10) {
      try {
        res = await this.createConversationCreative({
          adAccountId: adAccountId,
          campaignUrn: data.campaignUrn,
          inmailUrn: inmailUrn,
          adName: data.subject,
        });
        break;
      } catch (e) {
        attempts++;
        if (attempts === 10) {
          throw e;
        }
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }
    console.log("SPONSORED CREATIVE CREATED", res);
    if (res == "") {
      throw new Error("Failed to create sponsored creative after 10 attempts");
    }
    return res;
  }

  async createLeadGenConversation(data: {
    adAccountUrn: string;
    leadGenFormUrn: string;
    senderUrn: string;
    campaignUrn: string;
    body: string;
    subject: string;
    leadGenButtonText: string;
  }) {
    console.log("CONVO AD DATA", data);
    console.log("CREATING SPONSORED CONVERSATION");
    const convoUrn = await this.createSponsorConversation({
      adAccountUrn: data.adAccountUrn,
      subject: data.subject,
    });
    console.log("SPONSORED CONVERSATION URN", convoUrn);
    const convoId = convoUrn.split(":")[3];
    if (!convoId) {
      throw new Error("No conversation ID");
    }

    console.log("CREATING LEAD GEN SPONSORED MESSAGE");
    await new Promise((resolve) => setTimeout(resolve, 10000));
    const leadGenMessageUrn = await this.createSponsorConversationMessage({
      conversationId: convoUrn,
      message: {
        bodySource: {
          leadGenerationForm: data.leadGenFormUrn.replace(
            "leadGenForm",
            "adForm",
          ),
        },
        nextAction: {
          options: [
            {
              replyType: "LEAD_GENERATION_THANK_YOU",
              optionText: "Thank you",
            },
          ],
        },
      },
    });

    console.log("LEAD GEN SPONSORED MESSAGE URN", leadGenMessageUrn);
    console.log("CREATING FIRST MESSAGE");

    await new Promise((resolve) => setTimeout(resolve, 10000));
    const firstMessageUrn = await this.createSponsorConversationMessage({
      conversationId: convoUrn,
      message: {
        bodySource: {
          text: data.body,
        },
        nextAction: {
          options: [
            {
              replyType: "LEAD_GENERATION",
              optionText: data.leadGenButtonText,
              nextContent: leadGenMessageUrn,
            },
          ],
        },
      },
    });

    console.log("FIRST MESSAGE URN", firstMessageUrn);
    console.log("UPDATING FIRST MESSAGE");
    await new Promise((resolve) => setTimeout(resolve, 10000));
    const convo = await this.getSponsoredConversation(convoUrn);
    console.log("CONVO", JSON.stringify(convo, null, 2));
    const fm = await this.getSponsoredConversationMessage(
      convoUrn,
      firstMessageUrn,
    );
    console.log("FIRST MESSAGE", JSON.stringify(fm, null, 2));
    await new Promise((resolve) => setTimeout(resolve, 10000));
    await this.updateSponsorConversationFirstMessage({
      conversationUrn: convoUrn,
      firstMessageContentUrn: firstMessageUrn,
    });
    console.log("FIRST MESSAGE UPDATED");

    console.log("CREATING INMAIL");
    await new Promise((resolve) => setTimeout(resolve, 10000));
    const inmailUrn = await this.createInmail({
      adAccountUrn: data.adAccountUrn,
      name: data.subject,
      subject: data.subject,
      senderUrn: data.senderUrn,
      conversationUrn: convoUrn,
    });
    console.log("INMAIL URN", inmailUrn);
    const adAccountId = data.adAccountUrn.split(":")[3];
    if (!adAccountId) {
      throw new Error("No ad account ID");
    }
    console.log("CREATING SPONSOREDCREATIVE");
    await new Promise((resolve) => setTimeout(resolve, 10000));
    let res = "";
    let attempts = 0;
    while (attempts < 10) {
      try {
        res = await this.createConversationCreative({
          adAccountId: adAccountId,
          campaignUrn: data.campaignUrn,
          inmailUrn: inmailUrn,
          adName: data.subject,
          adFormUrn: data.leadGenFormUrn,
        });
        break;
      } catch (e) {
        attempts++;
        if (attempts === 10) {
          throw e;
        }
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }
    console.log("SPONSORED CREATIVE CREATED", res);
    if (res == "") {
      throw new Error("Failed to create sponsored creative after 10 attempts");
    }
    return res;
  }

  async getAudienceCount(data: TargetingCriteria) {
    let queryString =
      "q=targetingCriteriaV2&targetingCriteria=(include:(and:List(";
    for (const andGroup of data.include.and) {
      queryString += "(or:(";
      const orFacets = andGroup.or;
      for (const [facet, entities] of Object.entries(orFacets)) {
        if (entities) {
          queryString += `${facet.replaceAll(":", "%3A")}:List(`;
          queryString += entities
            .map((entity) => `${encodeQueryParam(entity)}`)
            .join(",");
          queryString += ")";
        }
      }
      queryString += "))";
      // Add comma if this isn't the last AND group
      if (andGroup !== data.include.and[data.include.and.length - 1]) {
        queryString += ",";
      }
    }
    if (data.exclude) {
      queryString += "))";
      queryString += ",exclude:(or:(";
      const orFacets = data.exclude.or;
      const total = Object.entries(orFacets).length;
      let count = 0;
      for (const [facet, entities] of Object.entries(orFacets)) {
        if (entities) {
          queryString += `${facet.replaceAll(":", "%3A")}:List(`;
          queryString += entities
            .map((entity) => `${encodeQueryParam(entity)}`)
            .join(",");
          queryString += `${count < total - 1 ? ")," : ")"}`;
          count++;
        }
      }
      queryString += ")))";
    } else {
      queryString += ")))";
    }

    console.log(queryString);
    //q=targetingCriteriaV2&targetingCriteria=(include:(and:List((or:(urn%3Ali%3AadTargetingFacet%3Atitles:List(urn%3Ali%3Atitle%3A6635,urn%3Ali%3Atitle%3A26,urn%3Ali%3Atitle%3A6171,urn%3Ali%3Atitle%3A10120,urn%3Ali%3Atitle%3A5505,urn%3Ali%3Atitle%3A1759,urn%3Ali%3Atitle%3A4388,urn%3Ali%3Atitle%3A13530,urn%3Ali%3Atitle%3A3997,urn%3Ali%3Atitle%3A18963,urn%3Ali%3Atitle%3A22499,urn%3Ali%3Atitle%3A9579,urn%3Ali%3Atitle%3A1336,urn%3Ali%3Atitle%3A1575,urn%3Ali%3Atitle%3A7819,urn%3Ali%3Atitle%3A297))),(or:(urn%3Ali%3AadTargetingFacet%3Aindustries:List(urn%3Ali%3Aindustry%3A6))),(or:(urn%3Ali%3AadTargetingFacet%3Arevenue:List(urn%3Ali%3Arevenue%3A(10,100)))),(or:(urn%3Ali%3AadTargetingFacet%3Alocations:List(urn%3Ali%3Ageo%3A103644278))),(or:(urn%3Ali%3AadTargetingFacet%3AinterfaceLocales:List(urn%3Ali%3Alocale%3Aen_US))))))
    //q=targetingCriteriaV2&targetingCriteria=(include:(and:List((or:({encoded facet_URN_1}:List({encoded facet_URN_1_value_1}, {encoded facet_URN_1_value_2}))),(or:({encoded facet_URN_2}:List({encoded facet_URN_2_value_1},{encoded facet_URN_2_value_2}))))))

    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/audienceCounts?${queryString}`,
      {
        method: "GET",
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
        },
      },
      undefined,
      {
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get audience count");
    }
    const resData = res.data as {
      elements: {
        total: number;
        active: number;
      }[];
    };
    if (!resData.elements[0]) {
      throw new Error("No elements in response");
    }
    return resData.elements[0].total;
  }

  async getSponsoredConversationMessage(
    conversationUrn: string,
    messageUrn: string,
  ) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/conversationAds/${conversationUrn}/sponsoredMessageContents/${messageUrn}`,
      {
        method: "GET",
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
        },
      },
    );
    return res;
  }

  async getSponsoredConversation(conversationUrn: string) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/conversationAds/${conversationUrn}`,
      {
        method: "GET",
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
        },
      },
    );
    return res;
  }
  // https://api.linkedin.com/rest/campaignConversions/(campaign:urn%3Ali%3AsponsoredCampaign%3A378853486,conversion:urn%3Alla%3AllaPartnerConversion%3A18605540)
  // https://api.linkedin.com/rest/campaignConversions/(campaign:urn%3Ali%3AsponsoredCampaign%3A337643194,conversion:urn%3Alla%3AllaPartnerConversion%3A70203)

  async associateConversationWithCampaign(
    conversationUrn: string,
    campaignUrn: string,
  ) {
    console.log(
      `https://api.linkedin.com/rest/campaignConversions/(campaign:${campaignUrn.replaceAll(":", "%3A")},conversion:${conversationUrn.replaceAll(":", "%3A")})`,
    );
    const res = await this.callFetchToLinkedInApiResOnly(
      `https://api.linkedin.com/rest/campaignConversions/(campaign:${campaignUrn.replaceAll(":", "%3A")},conversion:${conversationUrn.replaceAll(":", "%3A")})`,
      {
        method: "PUT",
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
        },
        body: JSON.stringify({
          campaign: campaignUrn,
          conversion: conversationUrn,
        }),
      },
      undefined,
      {
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to associate conversation with campaign");
    }
  }

  async getConversionsForAdAccount(adAccountUrn: string) {
    const res = await this.callFetchToLinkedInApi(
      `https://api.linkedin.com/rest/conversions?q=account&account=${adAccountUrn.replaceAll(":", "%3A")}`,
      {
        method: "GET",
        headers: {
          "X-Restli-Protocol-Version": "2.0.0",
        },
      },
      undefined,
      {
        "X-Restli-Protocol-Version": "2.0.0",
      },
    );
    if (!res.success) {
      console.log(JSON.stringify(res.error, null, 2));
      throw new Error("Failed to get conversions for ad account");
    }
    return res.data as {
      elements: {
        postClickAttributionWindowSize: number;
        viewThroughAttributionWindowSize: number;
        created: number;
        type: string;
        enabled: boolean;
        name: string;
        lastModified: number;
        id: number;
        attributionType: string;
        conversionMethod: string;
        account: string;
      }[];
    };
  }
}

interface TextConversationMessage {
  bodySource: {
    text: string;
  };
  nextAction: {
    options: [
      {
        replyType: "LEAD_GENERATION";
        optionText: string;
        nextContent: string;
      },
    ];
  };
}

interface LeadGenMessage {
  bodySource: {
    leadGenerationForm: string;
  };
  nextAction: {
    options: [
      {
        replyType: "LEAD_GENERATION_THANK_YOU";
        optionText: "Thank you";
      },
    ];
  };
}

interface ExternalWebsiteMessage {
  bodySource: {
    text: string;
  };
  nextAction: {
    options: [
      {
        replyType: "EXTERNAL_WEBSITE";
        optionText: string;
        landingPage: string;
      },
    ];
  };
}

export async function getLinkedInApiClientFromOrganizationId(
  organizationId: number,
) {
  const linkedInUserRepository = new LinkedInUserRepository();

  const refreshTokenAndInstanceUrl =
    await linkedInUserRepository.getOneByOrganizationId(organizationId);

  if (!refreshTokenAndInstanceUrl) {
    throw new Error("LinkedIn user not found");
  }

  console.log(refreshTokenAndInstanceUrl);
  if (!refreshTokenAndInstanceUrl) {
    return null;
  }
  return new LinkedInApiClient(refreshTokenAndInstanceUrl.refreshToken);
}

export async function getLinkedInFromUserId(userId: string) {
  const linkedInUserRepository = new LinkedInUserRepository();
  const linkedInUserService = new LinkedInUserService({
    linkedInUserRepository,
  });

  const refreshTokenAndInstanceUrl: LinkedInUser | null =
    await linkedInUserService.getLinkedInUser(userId);

  console.log(refreshTokenAndInstanceUrl);
  if (!refreshTokenAndInstanceUrl) {
    return null;
  }
  return new LinkedInApiClient(refreshTokenAndInstanceUrl.refreshToken);
}

function encodeQueryParam(value: string) {
  return value.replace(
    /[^A-Za-z0-9]/g,
    (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`,
  );
}
