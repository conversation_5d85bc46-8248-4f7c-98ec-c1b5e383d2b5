"use client";

import type { DotLottieReactProps } from "@lottiefiles/dotlottie-react";
import * as React from "react";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";

import { cn } from "./index";

export interface LottieAnimationProps
  extends Omit<DotLottieReactProps, "className"> {
  className?: string;
  containerClassName?: string;
  onComplete?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
  onFrame?: (frameData: { currentFrame: number }) => void;
}

/**
 * Lottie animation component for displaying animations from .lottie or lottie .json files
 *
 * Uses the DotLottieReact library to render animations with various configuration options.
 * Provides callbacks for animation events and ref access for animation control.
 */
const LottieAnimation = React.forwardRef<
  HTMLCanvasElement,
  LottieAnimationProps
>(
  (
    {
      className,
      containerClassName,
      onComplete,
      onPlay,
      onPause,
      onFrame,
      ...props
    },
    ref,
  ) => {
    const [dotLottie, setDotLottie] = React.useState<any>(null);

    // Setup event listeners when dotLottie instance is available
    React.useEffect(() => {
      if (!dotLottie) return;

      const eventHandlers = [
        { event: "complete", handler: onComplete },
        { event: "play", handler: onPlay },
        { event: "pause", handler: onPause },
        { event: "frame", handler: onFrame },
      ];

      // Add event listeners
      eventHandlers.forEach(({ event, handler }) => {
        if (handler) {
          dotLottie.addEventListener(event, handler);
        }
      });

      // Cleanup event listeners on unmount
      return () => {
        eventHandlers.forEach(({ event, handler }) => {
          if (handler) {
            dotLottie.removeEventListener(event, handler);
          }
        });
      };
    }, [dotLottie, onComplete, onPlay, onPause, onFrame]);

    const dotLottieRefCallback = React.useCallback((instance: any) => {
      setDotLottie(instance);
    }, []);

    return (
      <div className={cn("relative", containerClassName)}>
        <DotLottieReact
          ref={ref}
          className={cn("h-full w-full", className)}
          dotLottieRefCallback={dotLottieRefCallback}
          {...props}
        />
      </div>
    );
  },
);

LottieAnimation.displayName = "LottieAnimation";

// New types for the loading animation component
export interface AnimationConfig {
  src: string;
  loop?: boolean;
  autoplay?: boolean;
}

export interface LottieLoadingProps {
  messages: string[];
  animations: {
    primary: AnimationConfig;
    secondary: AnimationConfig;
  };
  messageChangeInterval?: number;
  className?: string;
  containerClassName?: string;
}

/**
 * Loading animation component that alternates between two Lottie animations
 * and displays cycling messages with smooth transitions
 */
const LottieLoading = React.forwardRef<HTMLDivElement, LottieLoadingProps>(
  (
    {
      messages,
      animations,
      messageChangeInterval = 2000,
      className,
      containerClassName,
    },
    ref,
  ) => {
    const [currentAnimationKey, setCurrentAnimationKey] = React.useState<
      "primary" | "secondary"
    >("primary");
    const [currentMessage, setCurrentMessage] = React.useState(0);
    const [animatingOut, setAnimatingOut] = React.useState(false);
    const [animatingIn, setAnimatingIn] = React.useState(false);

    // Message cycling effect
    React.useEffect(() => {
      const interval = setInterval(() => {
        // Start animation out
        setAnimatingOut(true);

        setTimeout(() => {
          // Change message and prepare for animation in
          setCurrentMessage((prev) => (prev + 1) % messages.length);
          setAnimatingOut(false);
          setAnimatingIn(true);

          // Complete animation in
          setTimeout(() => {
            setAnimatingIn(false);
          }, 300);
        }, 300); // Wait for slide-out animation to complete
      }, messageChangeInterval);

      return () => clearInterval(interval);
    }, [messages.length, messageChangeInterval]);

    const handleAnimationComplete = () => {
      setCurrentAnimationKey((prev) =>
        prev === "primary" ? "secondary" : "primary",
      );
    };

    const currentAnimation = animations[currentAnimationKey];

    return (
      <div
        ref={ref}
        className={cn(
          "absolute inset-0 z-50 flex flex-col items-center justify-center",
          containerClassName,
        )}
      >
        <div
          className={cn("flex flex-col items-center justify-center", className)}
        >
          <LottieAnimation
            src={currentAnimation.src}
            autoplay={currentAnimation.autoplay ?? true}
            loop={currentAnimation.loop ?? false}
            className="h-[120px] w-[120px]"
            onComplete={handleAnimationComplete}
          />
          <div className="mt-9 h-5 overflow-hidden">
            <p
              className={cn(
                "font-inter text-center text-sm font-medium leading-5 text-[#171717] transition-all duration-300 ease-out",
                animatingOut
                  ? "translate-y-[-100%] transform opacity-0"
                  : animatingIn
                    ? "translate-y-0 transform opacity-100"
                    : "opacity-100",
              )}
            >
              {messages[currentMessage]}
            </p>
          </div>
        </div>
      </div>
    );
  },
);

LottieLoading.displayName = "LottieLoading";

export { LottieAnimation, LottieLoading };
